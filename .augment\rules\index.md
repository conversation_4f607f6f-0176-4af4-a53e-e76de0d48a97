---
type: "always_apply"
---

# 项目规则目录

本目录包含项目的各种规则和约束条件，用于指导项目开发和内容生成。

## 规则分类

### 1. 文件处理规则
- [文件过滤规则](./file-filters.md) - 定义项目中需要过滤的文件类型和处理方式

### 2. 内容生成规则  
- [AI检测规避规则](./anti-ai.md) - AI高嫌疑词汇及用法清单，用于内容人工化处理

### 3. 文档规范
- [文档规范](./documentation-standards.md) - 项目文档编写规范和格式要求

## 使用说明

1. 所有规则文件均采用Markdown格式编写
2. 规则文件应保持简洁明确，便于理解和执行
3. 如需修改规则，请更新对应的分项文件
4. 新增规则类别时，请同步更新本目录文件

## 规则优先级

1. 项目特定规则 > 通用规则
2. 最新规则 > 历史规则  
3. 明确规则 > 模糊规则

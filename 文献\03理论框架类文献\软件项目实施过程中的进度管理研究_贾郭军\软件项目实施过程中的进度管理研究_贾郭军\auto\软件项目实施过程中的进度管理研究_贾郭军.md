# 软件项目实施过程中的进度管理研究

贾郭军

（ 山西师范大学 数学与计算机学院山西 临汾 041004）

摘 要：深入分析了软件项目实施中影响进度的主要原因根据软件开发过程中不同阶段进度管理的特点 将软件项目进度管理划分为 个主要的工作阶段 计划阶段 需求阶段 实施阶段及收尾阶段结合各阶段进度管理工作的特点 说明进度管理需要解决的关键问题及相应的解决办法

关键词 进度管理 项目计划 软件工程 软件项目管理中图分类号 文献标识码：A

随着计算机应用的日益普及和深入 软件产品的规模越来越大 复杂性越来越高 传统个人作坊式的开发方式已经越来越不适应软件产业发展的要求 软件产业已逐步进入以团队开发为主的阶段［1］ 软件开发过程中的项目管理工作日益受到人们的关注和重视 在软件项目实施过程中 影响项目成功的因素有许多 其中进度管理是软件项目管理的一个重要方面

# 1 影响软件项目进度的因素分析

统计表明：导致软件项目不能按进度要求完成的主要因素有以下几个方面［2～6］ 。

缺乏详细准确的项目计划 项目计划确定了项目的范围 进度 审核 验收 费用等项目管理的诸多因素 是整个软件生命周期中的重要环节 也是项目管理最重要的方面之一 项目计划是项目跟踪和管理的重要基础 许多项目失败就是由于缺乏详细准确的项目计划 导致项目进度管理失去控制

缺乏对需求变更的有效管理 对于应用软件项目来说 影响项目进度的一个非常重要因素就是项目实施中的需求变更 需求变更管理不善将会导致开发工作不断反复 开发进度停滞不前

开发过程缺乏有效的控制和管理 软件开发过程中 一方面 由于开发工作缺乏有效的监督检查机制造成软件开发各阶段的进度管理工作失去控制 另一方面 由于开发过程中的阶段性成果失去有效的版本管理使整个开发工作陷入混乱。

不重视团队建设工作 目前 软件开发过程中存在的一个严重问题就是人员的流动问题 许多合同软件项目从开始实施到项目完成人员流动频繁 造成这种现象的原因固然有许多 但一个根本的原因就是在项目实施中忽略了团队建设 造成整个项目团队没有凝聚力

# 2 软件项目进度管理的阶段划分

在软件开发过程中 无论采用什么样的开发模型 软件开发都要经过 启动 $\twoheadrightarrow$ 需求 $\nrightarrow$ 设计 编码 测试 验收 等多个工作阶段［7］ 为了深入分析研究各阶段中影响项目进度的主要因素 根据各阶段进度管理的特点 将软件项目的进度管理工作划分为4个阶段：计划阶段、需求阶段、实施阶段、收尾阶段。这4个阶段与软件工程各阶段的关系如图1所示。

计划阶段进度管理的重点是从软件项目整体进度管理的要求出发 对项目实施中影响进度的全局因素进行分析 制定项目实施的总体工作计划 需求阶段是开发过程中项目双方协作最为密切的一个工作阶段 进度管理工作涉及到对项目双方工作进度的管理和控制 实施阶段的特点是全部工作由项目开发方承担并完成 进度管理工作的重点主要是对开发方的工作进度和产品质量进行管理 收尾阶段的工作重点是如何做好项目的验收工作 进度管理工作主要是项目验收的准备和验收工作的实施

# 3 计划阶段的进度管理

在软件项目启动阶段 需要根据项目的合同条款及总体工作的目标要求制定整个项目的总体工作计划 即要对项目实施中的各项活动做出周密的安排 计划阶段与进度管理有关的因素主要有以下3个方面。

项目进度计划 在项目的初期 项目负责人

![](images/a8188623c99e02400e5bda9e86369c52fc7bbd6b558df42ecf877c7a9ff81846.jpg)  
图 进度管理各阶段与软件工程各阶段对应关系 Fig ．1 The relationship bet ween scheduli ng manage ment st ages and soft ware engi neeri ng st ages

首先应该根据项目的合同要求明确项目的工作范围。然后依据工作内容对资源、成本及工作进度做出合理估算 进度计划应明确项目开始日期及完成日期 项目各工作阶段的工作内容及开始时间和完成时间等 由于项目进度计划是整个项目计划工作的基础 项目的进度计划必须详细 准确 合理 项目的进度计划将是进行项目进度跟踪和控制的重要依据。

里程碑设置 为了便于对进度计划的执行情况进行跟踪和控制 需要对项目进度计划中某些重要的时间点进行设置 即将这些时间点设置为里程碑 里程碑描述了每一开发阶段项目应达到的状态 每当项目进行到每一个里程碑时间点时 要进行本阶段进度完成情况的工作检查 里程碑确定了软件开发各工作阶段的最后完成时间及需要交付的阶段性工作成果。

3） 需求的变更控制 对软件项目进度影响最大的因素是需求变更。所以不论是ISO9000认证还是CMM认证都是十分强调对需求的变更控制。对软件项目的变更控制管理工作必须从项目计划阶段开始确定需求变更的工作流程 这有助于将需求变更带来的不利影响减到最小程度 由于软件项目实施中存在许多不确定因素 所以项目实施过程中要允许对项目计划进行调整 但是对计划的修改工作都必须在有效地控制下进行

# 4 需求阶段的进度管理

需求分析阶段的工作目标是要获取详细 准确地用户需求 分析工作要想按计划完成 需要项目双方共同努力才可以实现 本阶段进度管理工作的因素主要有 个方面 技术因素 管理因素及沟通因素 图

快速原型技术及需求复用技术 快速原型技术和软件复用技术是开发方快速 准确获得用户需求的主要技术手段 通过原型技术可有效解决软件产品可见性差的问题 用户通过对原型系统实物的使用 有助于提高对未来系统的认识能力 利用需求复用技术可以复用其他相似系统的需求分析结果 有助于加快整个需求分析的工作进度

进行需求的管理 需求阶段的工作一方面是进行需求的获取 另一方面需要对已获取的需求进行管理 通过需求管理一方面可以有效遏制需求分析阶段的需求变更 确保需求分析的工作进度；另一方面通过良好的需求管理工作可以提高需求分析结果的可复用性

![](images/f8d81f61bf8427c8bea3771b8005783e42a32c68074856fa9f88e28b5a1bdcd7.jpg)  
图 需求阶段影响项目进度的主要因素 Fig ．2 I ngredients of i nfluenci ng scheduli ng i n require ment st age

与用户进行有效的沟通 分析人员要快速 准确地获得用户的实际需求 除了具有优秀的需求分析经验和技能外很重要的一点是必须与用户进行良好的沟通。通过有效的沟通工作分析人员一方面可以准确、全面地了解用户的真实想法 提高需求分析的工作进度和质量 另一方面也容易赢得用户的信任和尊重 在需求分析工作中得到用户更多的支持和配合

# 5 实施阶段的进度管理

项目实施阶段包括设计 编码 测试几个软件开发工作阶段 开发工作在本阶段进入以开发方为主的项目实施阶段。本阶段影响项目进度的主要因素有以下两个方面（ 图3） 。

# 5．1 组建结构合理的项目团队提高团队战斗力

1） 组建结构合理的项目团队。实施阶段的项目团队需要由具有不同技能的技术人员组成［7］ 在组建团队时 项目负责人必须根据工作内容 分析项目实施过程中涉及到的技术因素 确定项目团队的人员构成 在项目实施中遇到相应技术问题时 团队中都有熟悉该领域的人员能够予以解决 所以结构合理的项目团队是各阶段工作进度按计划进行的关键。

职责明确 分工合理 在各阶段项目实施的过程中 要求项目组各成员的工作分工和责任明确 防止团队成员挑肥拣瘦 推委扯皮 不负责任现象的发生 使开发工作从制度上得到保证。

![](images/a6590834e78d535cc58e5f4801c9ebf00d4251709507ea01ef69ce0b6fcf2b14.jpg)  
图 实施阶段影响项目进度的主要因素 Fig ．3 I ngredients of i nfluenci ng scheduli ng i n develop ment st age

3） 加强团队建设降低人员风险。团队开发的最大问题就是团队管理。一个人心涣散、人员流动频繁的开发团队很难做到按计划、高质量地完成软件开发任务。所以项目负责人要加强项目团队的建设工作营造一种团结协作 认真负责 积极向上的工作氛围 增强团队的凝聚力和战斗力 降低团队成员流动的频率 做好人员流动风险的防范工作［5］ 只有团队稳定 团队具有较强战斗力 项目进度计划才能得到落实和保证

# 5．2 采用新技术、新方法提高开发工作的劳动效率

采用软件工程研究的新技术 新方法支持各阶段软件开发工作 是提高软件开发工作效率 加快软件开发进度的有效手段 提高软件项目工作进度的技术手段有 类

软件复用技术 软件复用技术［6］ 适用于软件开发的各个工作阶段 通过软件复用可以大大加快软件开发的工作进度并提高产品质量 所以复用技术是提高软件开发劳动生产率的重要手段 要在项目团队甚至整个软件企业实现更大范围的软件复用 做好知识管理工作是实现软件复用的根本 对于软件开发中的知识管理来说目前还是一个薄弱的环节制约了在更大范围内进行软件复用的能力。

技术 计算机辅助软件工程 技术是提高软件开发工作效率的另一个主要的手段 通过用于辅助软件开发 运行 维护和管理的工具支持 能够加快软件开发速度 降低开发成本

# 6 收尾阶段的进度管理

收尾阶段是整个软件项目实施的最后阶段 本阶段进度管理的目标是做好项目验收的准备工作 使软件顺利通过用户验收并交付使用 本阶段进度管理工作的重点体现在以下 个方面

做好验收测试工作 在软件项目验收之前需要接受用户的验收测试 对于合同软件项目来说 用户的验收测试工作往往以系统试运行的形式出现 为使软件系统能够顺利通过验收测试并交付用户使用 项目组首先要做好试运行工作计划与试运行工作准备 其次 要做好试运行期间运行情况的记录 试运行结果数据将是软件能否通过验收的重要依据 再次 对于系统试运行期间出现的问题 项目组需要认真分析原因 及时完成系统的修改和完善工作。

2） 做好验收文档资料的准备工作。软件项目验收的一个重要内容就是文档资料验收。在项目验收之前项目组需要根据合同要求 向用户项目验收组提供相关的软件系统文档资料 为保证文档资料的准确 全面 并能顺利通过用户的验收 需要对文档资料进行认真准备和审核 防止将不合格的文档资料提交给用户 造成工作上的返工。

目前 进度管理问题已经成为软件项目实施的主要问题 文中研究了软件项目实施中影响进度的主要因素 提出了一个软件项目进度管理的阶段划分方法 给出了各阶段进度管理需要解决的重点问题 文中给出的软件项目进度管理策略适合于合同软件开发的进度管理工作 对其他类型的软件项目实施也具有一定的参考

# 参考文献：

［ 1］ RisingL Janoff N.The scrum soft ware development processfor smll teams[J]IEEE Soft ware,2OOo,17(4):26-32.［ 2］ Johnson J $\cdot$ Tur ni ng chaos i nt o success［ J］ $\cdot$ ．Soft ware Magazine 199919（3） ：30－39．［ 3］ Boeh m B De Macro T $\cdot$ ．Soft ware risk manage ment ［ J］ ．I EEE Soft ware 199714（3） ：17－19．［ 4］ 白思俊．现代项目管理［ M］ ．北京：机械工业出版社2002．

［ 5］ Nei ghbors Ja mes $\cdot$ ．The DRACO approach t o constructi ng soft ware fro m reusable co mponents［ J］ $\cdot$ ．I EEE Transaction on Soft-ware Engineering 198410（5） ：47－54．  
［ 6］ Perry De wayne E Vott a La wrence G $\cdot$ ．People $,$ organizations $,$ and process i mprove ment ［ J］ ．I EEE Soft ware 199411（4） ：36  
［7］ 郑人杰殷人昆．实用软件工程（ 第二版）［ M］ ．北京：清华大学出版社1997．

# Scheduli ng manage ment i n soft ware project devel opi ng

JI A Guo-jun（ I nstit ute of Mat he matics and Co mp uter Shanxi Teacher Uni versit y Li nf en 041004Chi na）

Abstract ： This paper analyses so me i nfluenci ng fact ors f or schedule i n soft ware developi ng $\cdot$ ．Accordi ng t o characteristics on every phase i n soft ware developi ng $,$ f our scheduli ng phases were classified ：planni ng phase $,$ requiri ng phase $,$ developi ng phase $,$ and ending phase $\ast$ ．Relyi ng on scheduli ng manage ment characteristics of every phase $,$ gives so me key questions and met hods f or scheduli ng manage ment ．

Key words ：scheduli ng manage ment ；project plan $;$ ；soft ware engi neeri ng ；soft ware project manage ment

（ 上接第202页）

# 参考文献：

# Designi ng and i mple menti ng of a mani pul ator for ultrasonic nondestructive testi ng

DU Gong-ru QI U Bao-mei MA Hong-wei （ School of Mechanical Engi neeri ng Xi ’an Uni versit y of Science and Technology Xi ’an 710054Chi na）

Abstract ：I n or der t o solve ultrasonic t esti ng questions about t he parts of plane $,$ cyli ndrical surface $,$ sphere surface and so on $,$ a syst e m f or ultrasonic t esti ng based on mani pulat or is present ed $\ast$ ．The manipulat or f or ultrasonic testi ng is developed by means of t he ultrasonic testi ng technology $,$ robotics $,$ nu meral control co mputer technology and so on $\cdot$ ．This manipulat or is mai nly made up of mechanical syste m $,$ control syste m $,$ serve syste m and i nspecti ng syste m $\ast$ ．It can realize control aut o matically and get dat a of real-ti me coordi nates by PC control $\ast$ The orient ation $,$ quantit ative and qualit ative analysis of defects benefit fro mit $\cdot$ ．The struct ure of t his manipulat or is si mple and t he cost is lo wer $\ast$ ．It not onl y uses f or ultrasonic t esti ng $,$ but also can use i n ot hers fiel ds $\ast$ ．It has broad applicabilit y $\cdot$

Key words ： ultrasonic testi ng $;$ manipulat or ；aut o mation
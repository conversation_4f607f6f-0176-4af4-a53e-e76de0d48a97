# 文档规范

## 概述
本文档定义了项目文档的编写规范和格式要求，确保文档的一致性、可读性和维护性。

## 通用原则

### 1. 文档目标
- **准确性**：内容准确无误，与实际情况一致
- **完整性**：涵盖所有必要信息，无重要遗漏
- **清晰性**：表达清晰，逻辑结构合理
- **时效性**：及时更新，保持内容的现时性

### 2. 文档结构
- **标题层次**：使用清晰的标题层次结构
- **目录导航**：为长文档提供目录
- **章节编号**：使用一致的编号系统
- **交叉引用**：合理使用链接和引用

### 3. 语言风格
- **简洁明了**：避免冗长复杂的句子
- **术语一致**：统一使用专业术语
- **语态规范**：优先使用主动语态
- **避免AI痕迹**：参考[AI检测规避规则](./anti-ai.md)

## Markdown规范

### 1. 文件结构
```markdown
# 文档标题

## 概述
简要介绍文档内容和目的

## 目录
- [章节1](#章节1)
- [章节2](#章节2)

## 章节1
具体内容...

## 章节2
具体内容...

## 参考资料
相关链接和引用
```

### 2. 标题规范
```markdown
# 一级标题 - 文档标题
## 二级标题 - 主要章节
### 三级标题 - 子章节
#### 四级标题 - 详细说明
```

### 3. 列表格式
```markdown
**有序列表：**
1. 第一项
2. 第二项
   - 子项目
   - 子项目

**无序列表：**
- 项目一
- 项目二
  - 子项目
  - 子项目
```

### 5. 表格格式
```markdown
| 列标题1 | 列标题2 | 列标题3 |
|---------|---------|---------|
| 数据1   | 数据2   | 数据3   |
| 数据4   | 数据5   | 数据6   |
```

### 6. 链接和图片
```markdown
**内部链接：** [链接文本](./relative-path.md)
**外部链接：** [链接文本](https://example.com)
**图片：** ![图片描述](./images/example.png)
```

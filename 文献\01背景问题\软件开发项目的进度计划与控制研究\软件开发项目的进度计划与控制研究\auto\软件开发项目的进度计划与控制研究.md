<table><tr><td>武汉理工大学</td></tr><tr><td>硕士学位论文</td></tr><tr><td>软件开发项目的进度计划与控制研究</td></tr><tr><td>姓名：石慧</td></tr><tr><td>申请学位级别：硕士</td></tr><tr><td>专业：信息管理与信息系统</td></tr><tr><td>指导教师：王虎</td></tr><tr><td>20071101</td></tr></table>

# 摘要

随着信息技术的飞速发展，软件开发规模及开发队伍越来越庞大，个人单打独斗的作坊式开发方式己经越来越不适应形势发展的需要。各软件企业都在积极将软件项目管理引入到开发活动中，怎样对软件项目进行有效的管理就成为一个需要研究的课题。而在软件项目管理过程中，项目进度的计划和控制管理是决定项目能否顺利实施的关键内容。本文对软件项目计划控制过程中的具体方法进行系统研究，为软件企业实施软件项目管理提供参考。

本文运用动态规划的思想和网络计划的方法，利用马尔可夫链模型进行软件项目进度预测，包括并行进度的预测。并结合参与项目开发课题的实际调研资料，对软件开发项目中的计划和控制应用进行研究，构建了一个并行的IT项目进度马尔可夫链预测模型。

本文首先介绍了论文的选题背景及依据、研究现状及意义、研究内容及框架。接着，综合运用项目管理、软件工程、软件项目管理方面的知识，对软件开发项目中的进度计划管理和进度控制管理进行了系统研究，重点分析了计划管理中的工作量估算、工作结构分解、制定计划的三种常用技术(Gantt，PERT,CPM)，并对软件项目进度控制中的常用技术（里程碑进度、人为设定活动进度、工作单元进展和挣值法）进行分析和研究。

在此基础上，介绍了马尔可夫分析法的基本原理和软件开发经历阶段及开发方法。对软件项目开发中的并行工程进行分析和研究。引入马氏链和 Delphi法来预测软件项目进度。然后，以三环海通汽车4S店办公自动化系统为例，证实基于马氏链的软件项目进度预测的有效性和可行性。

最后，对全文进行总结和展望。马尔可夫链预测是建立在对历史数据的分析统计之上的。历史数据越多越精确，预测也就越可靠。进度完成状态的马尔可夫链预测是一种概率预报，其准确性和可靠性要通过大量的预测检验才能显示出来。本文中马尔可夫链是有限期的预测，也可以将之扩展为无限期的。这一点在生命周期法中有很强的实用价值。

本文的创新之处在于：把马尔可夫链思想引入到软件项目管理进度预测中，并尝试解决软件开发项目中并行进度的预测问题。

关键词：软件项目，软件项目管理，计划和控制，马尔可夫链

# Abstract

To meet the need of the larger and larger scale in the software development and more and more people joining in this career， which results from the rapid development of info-tech, software project managing has widely been introduced into software companies. Thus， it is necessary to work out an effective way of managing software project. It is known that the vital elements in managing software project must be planning and controlling of the project. The present dissertation is an attempt of a systematic study on the approaches adopted in the process of controlling the plan of a software project, which might offer a practical reference to software companies.

The prediction of IT project scheduling as well as the scheduling in parallel is carried out by adopting the theory of Dynamic Programming and the method of network planning and making use of the model of the Markov Chain in this thesis. We have studied the application of schedule prediction of $\boldsymbol { \mathsf { \Pi } }$ project with the actual research data on software development projects,establishing Markov's dynamic predict model of parallel IT project schedule.

In this paper， firstly, I introduce the background and basis,purpose and significance，content and framework of this paper. Secondly， based on the achievements made in the fields of project managing, software engineering and software project managing, managing of plan and controlling in the software development project is dealt with in this dissertation, and especially the estimating of workload, assignment of the task, and three techniques most taken (Gantt, PERT and CPM) in plan-making. Then I introduce four common techniques, milestone progress, activities progress decided by people, work modules progress, earned value method.

Based on the knowledge mentioned above, this paper introduce the Basic principles of Markov, the stage experience and method of $\mathbf { I T }$ project development. Then we studied the concurrent engineering of $\pmb { \Gamma }$ project development and predict the schedule of IT project with the methods of the Markov Chain and DELPHI. Based on the above analyses, a project of OA system is taken to testify the feasibility and validity of this frame base on Markov Chain.

Finally,I give the conclusion of the whole paper. Markov Chain prediction is based on analysis of previous statistics.With the help of detailed and accurate historical data series analysis, the present model can give satisfactory forecasting. However, as a probability forecasting model of scheduling, the accuracy and reliability of the present model should be verified through a huge amount of data forecasting and testing. The Markov Chain in this paper is used to predict within a certain period; however, it can be expanded into an unlimited one, which has more feasible value in life cycle.

Its innovation process lies in the combination of the Markov Chain and technology of the network planning, giving a concise analysis and prediction of schedule. .

Key words: software project,software project managing, planning and controlling, Markov Chain

# 独创性声明

本人声明，所呈交的论文是本人在导师指导下进行的研究工作及取得的研究成果。尽我所知，除了文中特别加以标注和致谢的地方外，论文中不包含其他人已经发表或撰写过的研究成果，也不包含为获得武汉理工大学或其它教育机构的学位或证书而使用过的材料。与我一同工作的同志对本研究所做的任何贡献均已在论文中作了明确的说明并表示了谢意。

签名：石慧日期：200.11.20

# 关于论文使用授权的说明

本人完全了解武汉理工大学有关保留、使用学位论文的规定，即学校有权  
保留、送交论文的复印件，允许论文被查阅和借阅：学校可以公布论文的全部  
或部分内容，可以采用影印、缩印或其他复制手段保存论文。（保密的论文在解密后应遵守此规定）

签名：石慧导师签名：日期：207120

# 第1章绪论

# 1.1 引言

软件项目管理引起注意最早源自于20世纪70年代。当时美国国防部曾立题专门研究软件项目做不好的原因[，发现 $70 \%$ 的项目是因为管理不善引起的，而并不是因为技术实力不够，进而得出一个结论，即管理是影响软件研发项目全局的因素，而技术只影响局部。到了90年代中期，软件项目管理不善的问题仍然存在[2。根据美国软件工程实施现状的调查，软件研发的情况仍然很难预测，大约只有 $10 \%$ 的项目能够在预定的费用和进度下交付。在商用软件产业中，这一现象尤为严重。1995年，美国共取消了810亿美元的软件项目，其中 $31 \%$ 的项目未做完就取消了， $53 \%$ 的软件项目进度通常要延长一半的时间，只有 $9 \%$ 的软件项目能够及时交付并且费用也不超支。软件项目失败的主要原因有：求不明确，计划不充分；工作量评估与实际值差距较大；一个严谨的追踪控制过程;用新技术；理无力，组织不当：关心创新而不关心费用和风险；缺乏有效的沟通等等。在关系到软件项目成功与否的众多因素中，项目计划、工作量估计、进展控制、需求变化和风险管理等都是与项目管理直接相关的因素。由此可见，软件项目管理的意义至关重要。

进入二十一世纪，随着信息技术的飞速发展，软件开发规模及开发队伍越来越庞大，个人单打独斗的作坊式开发方式已经越来越不适应形势发展的需要。各软件企业都在积极将软件项目管理引入开发活动中，对开发实行有效的管理。从概念上讲，软件项目管理是为了使软件项目能够按照预定的成本、进度、质量顺利完成，而对成本、人员、进度、质量、风险等进行分析和管理的活动。实际上，软件项目管理的意义不仅仅如此，进行软件项目管理有利于将开发人员的个人开发能力转化成企业的开发能力，企业的软件开发能力越高，表明这个企业的软件生产越趋向于成熟，企业越能够稳定发展（即减小开发风险）。在软件开发项目管理过程中，项目的计划和控制是决定项目能否顺利实施的关键管理内容，而进度管理是软件项目管理中核心的内容，因此，对其进行研究有着较大的现实意义。怎样结合目前国内大部分软件开发企业的实际情况，研究相应的软件项目进度计划和控制方法成为我们迫切需要解决的课题。

# 1.2国内外研究现状

JohnM.Nicholas在《面向商务技术的项目管理》中描绘了与商务和技术项目管理（包括与各种企业经营活动相关的产品和系统开发项目活动管理）的一幅全景图，涉及它的起源、应用、原理、本质和实施的步骤等。他从系统、管理和行为的角度进行了全方位的论述，介绍了项目管理的进度计划、控制技术。

杰克.吉多詹姆斯，P.克莱门斯等等在《成功的项目管理》中，从管理的层面剖析了项目生命周期各阶段的管理要点，生动地刻画了项目经理，项目团队的职责和作用，深入研究和分析了项目管理的进度计划与控制问题。

毕星，翟丽在《项目管理》中分别阐述：项目概述；项目组织；项目经理；项目目标；项目计划；项目估算及预算：项目进度计划；资源分配；项目跟踪与控制；项目风险管理；项目审查；项目沟通与冲突管理；项目采购与分包；项目终止；计算机辅助项目管理软件；项目管理展望等。书中还以附录的形式对ProjectWorkbench这种项目管理软件的使用作了详细的说明。

Hughes.B，Cotterell.M在《ITProjectManagement》全书从项目管理的角度，采用步进式策划方法逐一分析了软件开发的各个环节。主要内容包括：项目评估、选择合适的项目方法、软件工作量估计、活动策划、风险管理、资源分配、监督与控制、管理合同、管理人员和组织群组、软件质量等。

1948年Rand公司产生的一种预测未来时间的技术，随后在诸如联合规划和成本估算之类的各种其他应用中作为使专家意见一致的方法。

S.Pressman.在 《Software Engineering A Practitioner's Approach》谈到类比法就是把当前项目和以前做过的类似软件项目比较，通过比较获得其工作量的估算值。

Chatzoglou PD，Macaulay L A在《A review of existing models for project planning and estimation and the need for a new approach》中提到在制定软件项目计 划时，主要有三种常用技术：Gantt，PERT，CPM。

McGrry J,Card D,Jones C在《实用软件度量》中明确指出客观的、有意义的和量化的度量是成功地开发当今复杂软件的关键。

荣钦科技在《Project 2003 在项目管理中的应用》着重介绍Microsoft Project

2003 的常用功能，如何进行项目的规划、资源和成本管理、进度的控件等。

PERT是由美国学者Malcolm 等提出的一种进度计划技术。

戴建国在《PERT工序时间估计的一些研究》中对经典PERT的计算误差进行了详细分析，认为用该方法计算的活动期望值和方差的误差均较大且使得计算的项目工期偏小。

Keefer D L，Verdini W A. Better 在《Estimation of PERT Activity TimeParameters》中对于服从分布的活动持续时间归纳总结出五种计算期望值和方差的方法，并对这些方法的误差进行了详细的分析。

Pedberg F在 《Scheduling software project to minimize the development timeand cost withagiven staff》中提出了一种基于马尔可夫模型的进度计划方法。该方法将IT项目抽象化为一个离散的马尔可夫决策过程并通过最小化费用函数求出最优的进度计划策略。

目前，国内外对IT项目的进度预测和控制通常采取三种办法：一是管理者依赖他们的成功经验去制定计划和实施过程控制。二是遵循标准指南。如IEEE(Institute of Electrical and Electronic Engineers)的 SPMPs ( Standard forSoftware ProjectManagement Plans)和美国Carnegie Mellon大学软件工程研究所(CMU/SEI)支持研究与开发的CMM/PSP/TSP软件过程改进体系[2I，这两个标准的主要目的旨在阐述IT项目管理计划的内容，本身并不指定预测控制过程中使用的具体技术、方法和步骤。三是利用支持工具。比较有名的有微软的MicrosoftProject 2OO2 Primavera 公司的高档项目管理工具Primavera Project Planner(简称P3)以及CA的All Fusion Process Management Suite。从当前国内外研究现状看，关注的重点都主要集中在IT项目管理预测控制的标准内容和工具平台方面，缺乏对预测控制过程中具体方法的系统研究。

# 1.3论文的主要内容

本文主要包括以下内容：

第一部分介绍了论文的选题背景及依据、研究现状及意义、研究内容及框架。

第二部分首先从概念和特点两个方面阐述了项目管理的基本内容，然后从概念、特点、原则、范围和内容这五个方面介绍软件项目管理的基本内容。

第三部分首先对软件项目进度计划和控制的目的和意义做了简单介绍，然后对软件开发项目进度计划中的工作量估算、工作结构分解、制定计划的三种常用技术（Gantt、PERT、CPM）和软件项目进度控制中的常用技术（里程碑进度、人为设定活动进度、工作单元进展和挣值法）进行分析和研究。

第四部分介绍了马尔可夫分析法的基本原理和软件开发经历阶段及开发方法。并对软件项目开发中的并行工程从概念、本质、组织形式、过程模型和开发模型等方面进行分析和研究。最后引入马氏链和DELPHI法来预测软件项目进并对并行模型转化为串行模型的方法进行概括。

第五部分以某汽车4S店办公自动化系统为例，引入马氏链对该项目进度进行预测，证实基于马氏链的软件项目进度预测的可行性。

第六部分进行简要回顾和总结。马尔可夫链预测是建立在对历史数据的分析统计之上的。历史数据越多越精确，预测也就越可靠。进度完成状念的马尔可夫链预测是一种概率预报，其准确性和可靠性要通过大量的预测检验才能显示出来。

本文的总体研究框架如下图1-1所示：

![](images/9d49eaba02aa4a41c3a1f50b9a0cb2f85334afec3763659ebc6a406445f42330.jpg)  
图1-1总体结构框架图

# 第2章理论基础

# 2.1项目管理概述

# 2.1.1项目的概念及特点[3]4]5]

项目是指在一定约束条件下具有特定目标的一项一次性任务。具体说，项目是人们通过努力，运用新的方法，将人力的、材料的和财务的资源组织起来，在给定的费用和时间约束规范内，完成一项独立的一次性的工作任务，以期达到由数量和质量指标所限定的目标。由概念可以看出：

(1）一次性是指应当在规定的时间内，由为此专门组织起来的人员完成;(2)应有一个明确的预期目标;(3)要有明确的可利用的资源范围，需要运用多种学科的知识解决问题；(4）没有或很少有以往的经验可以借鉴。通常，人们把他们进行的任何工作都叫做项目。在各种不同的项目中，项目内容可以说是千差方别，但项目本身有共同的特点，概括如下：(1）一次性。指这次任务完成之后，不会再有与此完全相同的另一任务，所以没有完全照搬的经验可以利用。(2)整体性。一个项目就是一个整体管理对象。由于内外环境的变化，要提高项目的总体效益，达到数量、质量、结构的总体优化，项目的管理和生产要素的配置必须是动态的。(3)独特性。每一个项目的内涵是唯一的或者说是专门的。即使在同类别内，各产品或服务也是存在差异的。(4）目标明确性。项目的目标有成果性目标和约束性目标。成果性目标是指项目的功能性要求，约束性目标是指资源消耗、时间要求、质量规定等限制条件。(5)生命周期性。项目从开始到结束，一般都要经历启动、计划、实施、结束等几个阶段。

# 2.1.2项目管理的概念、特点及其内容

项目管理是通过项目组织运用系统理论和方法，对项目及其资源进行计划、组织、控制和协调，旨在实现项目目标的管理方法体系，它是以项目经理负责制为基础的目标管理，它的主要特点是：

(1）充满了不确定性。项目一般来说是某种新的、前人未做过的事情，研制、生产的过程和最终产品是不确定的，具有较大的风险，因而对管理提出了更高的要求，要求管理体制要有较大的柔性。

(2)项目管理是一种过程管理.这与传统的面向职能和专业的管理不同，项目管理要在企业纵向工作流程的基础上，建立横向工作流程，跨越职能部门的界限，使企业现有的资源得到更有效的利用，信息得到更顺畅的沟通。目前在企业中存在着很多“管理间隙”和“专业化间隙”，企业被分为许多的“孤岛”，如图2-1所示，它们在本能上不愿相互联系，担心在组织中的利益和地位受到损害。项目管理就是要在这些“孤岛”之间进行沟通，从而完成共同的目标。

![](images/945c8e590825d5760bc1be789f816a8ba8e3e19ce1f3f00b2116ee6e7bacbc0f.jpg)  
图2-1管理的“孤岛”

(3)项目管理有严格的约束条件，项目管理必须在确定的期限内，通过不确定的过程，获得符合费用、性能、进度等方向严格要求的最终产品。

(4)项目管理是一种创造性的活动。它把项目的各个要素在相互结合的过程中，注入了创造性的思维，进行了融合，从新的角度和层面去解决问题。

美国《项目管理概要》，将项目管理为八个领域7:项目的范围管理、进度管理、技术管理、费用管理、质量管理、人力资源管理、通信管理、风险管理和采购管理，并将全寿命管理贯穿整个项目管理过程。

项目管理从时间上可以分为概念、开发、实施和结束四个阶段：从运作上可分为计划、组织和控制三个基本过程和功能分析、系统综合、评估与决策三项基本活动：从内容上可分为范围管理、时间管理等9个方面：从层次上可分为战术、战略和综合。如图2-2所示，形成了项目管理的框架。

![](images/0b5adc88fce985328ebf5c22192b4a141e242109f5ddf860ae518fa7586b55fb.jpg)  
图2-2项目管理的框架

# 2.2软件项目管理概述

21世纪是信息社会高速发展的世纪，软件作为信息技术的核心，起着至关重要的作用。随着信息产业的发展，人们日益关注软件开发过程中管理的规范化及标准化问题.然而软件开发中有太多的不可预知性[。但这种不可预知是对总体来说的，当软件开发进行到一定程度时，不可预知的东西就会变成可预知的东西。以往的做法是不去管它，这样所带来的就是项目的失败。要是有好的管理方法就可以控制这些不可预知的东西，软件项目就会一步一步随着你的设计思路走向成功。本节将对软件项目管理及相关内容进行论述。

# 2.2.1软件项目管理的概念及其特点

软件项目管理的提出是在20世纪70年代中期的美国，当时美国国防部专门研究了软件开发不能按时提交、预算超支和质量达不到用户要求的原因，结果发现 $7 0 \%$ 的项目是因为管理不善引起的，并非技术原因。于是软件开发者开始逐渐重视起软件开发中的各项管理。直到20世纪90年代中期，软件研发项目管理不善的问题仍然存在。国外项目管理研究小组Standish经过调查发现：在软件项目中约 $3 0 \%$ 的项目被取消；约75%的项目延时完成；约61%的项目不能达到预期的功能和特性：平均每个项目超过预算成本率 $1 8 9 \%$ ：平均项目完成超时概率 $2 2 2 \%$ □

在软件开发过程中，软件项目管理日益受到人们的关注。各软件企业都在积极将软件项目管理引入开发活动中，对开发实行有效的管理。软件项目管理和其他的项目管理相比有相当的特殊性。主要有以下几点：

(1）软件作为智力创造性的产品，其开发的整个过程都是设计过程，文档管理(包括程序代码)是软件项目管理的流程基础;(2）成本、进度、质量和风险是软件项目管理的对象；(3)人力资源管理是软件项目管理的基础，团队的和谐和配合是软件项目实施的保证。

从概念上讲，软件项目管理是为了使软件项目能够按照预定的成本、进度、质量顺利完成，而对成本、人员、进度、质量、风险等进行分析和管理的活动。进行软件项目管理的根本目的是为了让软件项目尤其是大型项目的整个软件生命周期(从分析、设计、编码到测试、维护全过程)都能在管理者的控制之下，以预定成本按期、按质的完成软件交付用户使用。

# 2.2.2软件项目管理的原则

在进行软件开发和项目管理时，应该遵循以下七条软件工程原则[0。它们是：

(1）用分阶段的生命周期计划严格管理

这条基本原理意味着，应该把软件生命周期划分成若干个阶段，并相应地制定出切实可行的计划，然后严格按照计划对软件的开发与维护工作进行管理。不同层次的管理人员都必须严格按照计划各尽其职地管理软件开发与维护工

作，绝不能受客户或上级人员的影响而擅自背离预定计划。

# (2）坚持进行阶段评审

软件的质量保证工作不能等到编码阶段结束之后再进行。在每个阶段都进行严格的评审，以便尽早发现在软件开发过程中所犯的错误，是一条必须遵循的重要原则。

# (3）实行严格的产品控制

在软件开发过程中不应随意改变需求，因为改变一项需求往往需要付出较高的代价，但是，在软件开发过程中改变需求又是难免的，当改变需求时，为了保持软件各个配置成分的一致性，必须实行严格的产品控制，其中主要是实行基准配置管理。

# (4）采用现代程序设计技术

实践表明，采用先进的技术既可提高软件开发的效率，又可提高软件维护的效率。

# (5)结果应能清楚地审查

为了提高软件开发过程的可见性，更好地进行管理，应该根据软件开发项目的总目标及完成期限，规定开发组织的责任和产品标准，从而使得所得到的结果能够清楚地审查。

# (6)开发小组的人员应该少而精

这条基本原理的含义是，软件开发小组的组成人员的素质应该好，而人数则不宜过多。组成少而精的开发小组是软件工程的一条基本原理。

(7）承认不断改进软件工程实践的必要性

遵循上述六条基本原理，就能够按照当代软件工程基本原理实现软件的工程化生产，但是，仅有上述六条原理并不能保证软件开发与维护的过程能赶上时代前进的步伐，能跟上技术的不断进步。这就需要把承认不断改进软件工程实践的必要性作为软件工程的第七条基本原理。按照这条原理，不仅要积极主动地采纳新的软件技术，而且要注意不断总结经验。

# 2.2.3软件项目管理的范围

有效的项目管理集中在三个P上:人员(people)、问题(problem)和过程(process)。其顺序不是任意的，任何管理者如果忘记了软件工程是人的智力密集型劳动，她就永远不可能在项目管理上得到成功：任何管理者如果在项目早期没有支持有效的用户通信，他有可能为错误的问题建造一个不错的解决方案，最后，对过程不在意的管理者可能冒着技术方法和工作插入到真空中的风险。

(1)人员

培养有创造力的、技术水平高的软件人员是从20世纪60年代就开始讨论的话题。事实上，“人因素”非常重要，以至于软件工程研究所专门开发了一个人员管理能力成熟度模型(PM-CMM)[1I]，旨在“通过吸引、培养、鼓励和留住改善其软件开发能力的人才增强针对软件组织日益复杂的应用程序开发的能力”。

# (2)问题

在进行项目计划之前，应该首先明确该项目的目的和范围，考虑可选的解决方案，定义技术和管理约束。没有这些信息，就不可能进行合理的成本估算；有效的风险评估;适当的项目任务划分；或是给出了意义明确的项目进度的标志的项目管理计划。

软件开发者和用户必须一起定义项目的目的和范围。在很多情况下，这项活动作为系统工作的一部分开始的，持续到作为软件需求分析的第一步。目的说明该项目的总体目标，而不考虑这些目标如何实现。范围说明给出于问题相关的主要数据、功能和行为，更为重要的是，它以量化的方式约束了这些特性。

一旦了解了项目的目的和范围，就考虑可选的方案了。虽然这一步并不讨论细节，但它使得管理者和开发者可以选择一条“最好的”途径，并且根据产品交付的期限、预算的限制、可用的人员、技术接口及其各种其他因素，给出项目的约束。

(3)过程

软件过程提供了一个框架，在该框架下可以建立一个软件开发的综合计划。若干框架活动适用于所有软件项目，而不在乎其规模和复杂性。若干不同的任务集合一一-每一个集合都出任务、里程碑、交付物以及质量保证点组成——使得框架活动适用于不同的软件项目的特征和项目组的需求。最后是保护性活动-如软件质量保证，软件配置管理，和测试一他们贯穿于整个过程模型中。保护性活动独立于任何一个框架活动，且贯穿于整个过程中。

软件过程的一般阶段(定义、开发和维护)适用于所有软件项目，问题在于如何选择一个适合项目组要开发的软件的过程模型。

项目管理者必须决定哪一个过程模型最适合待开发项目，然后局域公共过程框架活动集合，定义一个初步计划。一旦建立了初步的计划，便可以开始过程分解，即必须建立一个完整的计划，以反映框架活动中所需要的工作任务。

# (4)合并问题的过程

项目计划开始于问题和过程的合并。软件项目组要开发的每一个功能都必须通过为软件组织定义的框架活动集合来完成。

软件项目管理是软件工程的保护性活动。先于任何技术活动之前开始，并且持续贯穿于整个计算机软件的定义、开发和维护之中。

软件项目管理活动包含测度和度量、估算、风险分析、进度安排、跟踪和控制等活动。而计划和控制管理则是进行良好的项目管理的基础。在后续章节中将进行详细分析。

# 2.2.4软件项目管理的内容

从概念上讲，软件项目管理是为了使软件项目能够按照预定的成本、进度、质量顺利完成，而对成本、人员、进度、质量、风险等进行分析和管理的活动。软件开发项目管理是以最合理、有效、经济的手段来保证软件开发项目的成功完成。

软件项目管理的内容主要包括项目的进度、质量和水平等，其次是费用和效益。从用户的角度来看，软件项目的生命周期应该包括项目前期的论证工作、项目计划、软件开发、运行、维护及项目评价。由此可见，软件项目管理的范围不仅包括传统的软件开发过程，还应该包括开发前的准备工作以及运行中的维护工作和对项目的总结工作。软件项目管理可分为下面的几个方面：

# (1）项目的组织

目前大多数软件产品规模都很大，这就要求把多名软件开发人员组织起来，使他们分工配合共同完成开发工作。为了成功地完成软件项目，项目组成员必须以一种有效的方式彼此交流和沟通。管理者必须合理地组织和管理项目组，使项目组具有高度的凝聚力，从而保持较高的生产率，能够按预定的进度计划完成所承担的工作。

通常有三种组织结构模式：

$\textcircled{1}$ 按项目划分的模式：把开发人员按项目组成小组，小组成员自始至终承担项目的各项任务。该模式适用于规模不大的项目，并且要求小组成员在各方面有技术专长。

$\textcircled{2}$ 按职能划分的模式：按职能对组织进行划分，所有任务都由职能团队执

行，并由一位部门主管领导。

$\textcircled{3}$ ：矩阵形模[12]:这种模式是以上两种模式的复合。一方面按工作职能成立一些部门，另一方面每一个项目都有它的项目经理负责。每一个软件开发人员属于某一个部门。并参加某一个项目的工作。该模式的优点有：一方面部门的成员可以在部门内交流在各个项目中取得的经验，这更有利于发挥专业人员的作用；另一方面，各个项目有专门的人员负责，有利于软件项目的完成。这种模式比较适合于规模大的项目。

除了选择适当的组织形式，在项目的实施过程中，还要合理地配备人员。按不同阶段适时运用人员，恰当掌握用人标准。一般来说，软件项目不同阶段不同层次技术人员的参与情况是不一样的。图2-3是典型的软件开发人员参与情况曲线图[13]。

![](images/ab17ecc8ddd0d990c0b4ae512ba2a5736afceed43d529584a608007d4458eba7.jpg)  
图2-3软件开发人员参与情况曲线图

在人员配备问题上，由于配置不当，很容易造成人力资源的浪费，并延误工期。特别是采用恒定人员配备方案时在项目的开始和最后都会出现人力过剩，而在中期又会出现人力不足的情况。因此在计划中要合理地配备各类人员，分配相应的任务。

# (2）配置管理

软件配置管理 SCM（Software Configuration Management）是应用于由软件组成的系统的配置管理，通过一套工程规范，在整个软件生命周期中跟踪、记录软件，以标识、控制和管理软件变更的一种管理[4]。软件配置管理是软件

项目运作的一个支撑平台，图2-4是其简单示意图。

![](images/d46d2a7d2dd130d17af5b201c8241e39c6138b5a16168914c2cdb9d0aa17bfae.jpg)  
图2-4SCM作为支撑平台

(3）风险管理

软件风险管理是对影响软件项目、过程或产品的风险进行估计和控制的实践过程。它是一项包含风险识别、分析、计划、监督与控制的系统过程，也是一项实现项目目标机会最大化与损失最小化的过程。

# （4）质量保证

软件质量保证是一系列系统性的活动，他提供开发出满足使用要求产品的软件过程的能力证据[15]。影响软件质量的主要因素有正确性、可靠性、效率、完整性、使用性、维护性、测试性、灵活性、移植性、复用性、健壮性、风险性和可理解性.随着软件技术与用户需求的发展，软件开发的规模越来越大，软件的质量问题也显得越来越突出.

# (5)项目资源管理

软件项目资源管理主要包括人力资源管理、软件资源管理和硬件资源管理。人力资源管理的主要内容在于人力资源规划和分析，软件资源管理强调在软件开发的过程中，应该尽可能地重复使用以前开发活动中曾经积累或使用过的软件资源，硬件资源管理主要是对经济状态和技术状态的全面管理。

# (6)进度管理

软件项目任务分解、任务相关性分析、任务持续时间估算、任务完成情况的动态跟踪。

这是本文主要的研究内容，后文中会从进度的计划管理和跟踪控制方面进行详细论述。

# 第3章软件项目进度计划与控制的技术方法

凡事预则立，不预则废。这里的“预”就是计划。计划的重要性对软件企业是不言而喻的，然而在具体软件项目运作过程中，却经常不受重视。许多人对计划编制工作都抱有消极的态度，因为编制的计划常常没有用于促进实际行动。然而，项目计划的主要目的就是指导项目的具体实施。为了做出一个具有现实性和实用性的计划书，需要对计划过程中的工作量估算、工作结构分解、制定计划的常用技术和应把握的原则等进行分析。另外，虽然良好的计划是软件项目成功的重要基础，但在实际执行过程中，由于软件项目本身的特点和一些不可预测的因素，使得项目的进展不能完全按照计划进行。为了确保项目取得成功，必须对项目计划的执行过程进行追踪控制。本章将系统分析软件开发项目的计划和控制管理以及相关技术的介绍。

# 3.1软件项目进度计划与控制

软件项目进度管理主要包括两个内容：进度计划和进度控制。软件项目管理的进度机制实际上是一个闭环控制系统，如图3-1所示。

![](images/fc7625fccb3bed9fc815fc9023b654bee31547a7d6cb62a936268bfa06126d05.jpg)  
图3-1软件进度管理机制示意图

# 3.1.1软件项目进度计划

软件项目管理主要集中反映在项目的成本、质量和进度三个方面，这反映了软件项目管理的实质，这三个方面通常称为软件项目管理的“三要素”。进度是三要素之一，它与成本、质量二要素有着辨证的有机联系。软件项目进度计划是软件项目计划中的一个重要组成部分，它影响到软件项目能否顺利进行，资源能否被合理使用，直接关系到项目的成败。它包括以下方面的内容：

(1）项目活动排序。或者说确定工作包的逻辑关系。活动依赖关系确认的正确与否，将会自接影响到项目的进度安排、资源调配和费用的开支。项目活动的安排主要是用网络图法、关键路径法和里程碑制度。

(2）项目历时估算。历时估算包括一项活动所消耗的实际工作时间加上工作间歇时间。注意到这一点非常重要。历时估算方法主要有：类比法。通过相同类别的项日比较。确定不同的项目工作所需要的时间：专家法。依靠专家过去的知识、经验进行估算：参数模型法。是通过依据历史数据。用计算机回归分析来确定一种数学模型的方法。

(3）制定进度计划。制定进度计划就是决定项目活动的开始和完成的日期。根据对项目内容进行的分解.找出了项目工作的先后顺序.估计出了工作完成时间之后.就要安排好工作的时间进度。随着较多数据的获得.对日常活动程序反复进行改进.进度计划也将不断更新。

由于软件项目自身的特点，很多适合一般工程项目的进度计划方法，直接应用在软件项目中是不合适的。如何建立一个适合软件项目进度计划的模型，为以后的软件项目进度跟踪与控制打好基础，是本文重点研究的问题。

# 3.1.2软件项目进度控制

软件项目控制是指在计划执行过程中，由于诸多的不确定因素，使项目进展偏离正确的轨道，引起项目失控，项目管理者根据项目出现的新情况，对照原计划进行适当的控制和调整，实施纠偏措施，以确保项目计划取得成功。进行软件项目控制管理可以使项目出现问题及时得以解决，避免损失扩大。项目控制主要包括进度控制、成本控制、变更控制。其中，进度控制是本文要研究的重点内容之一。

一般说来，成功的软件项目就是能够在规定工期、成本的约束下，满足或超过客户要求的项目。也就是说时间、成本、质量、范围是软件项目成功的基本要素，对项目的成败起着至关重要的作用。其中时间因素又会对其他方面产生很大的影响。

从软件项目实施的结果来讲，能够在预定的时间内，达到预期的工作目标，就可以说是项目得到了有效的进度控制。从软件项目实施的过程来讲，有效的进度控制应该具有以下的特征：

(1）项目经理能够实时地掌握项目实际进展状况；  
(2)能预见性地发现和解决在项目实施中影响项目进展的问题；  
(3）能够采取有效方法控制影响项目进展的因素；  
(4)项目能在预定的（或可接受的）时间内完成。

# 3.2软件项目进度计划方法

# 3.2.1软件项目估算

进度计划是决定项目开发成功与否的关键因素，而估算是任何软件项目进度计划中不可或缺的重要内容，是确保软件项目进度计划制定的基础。

软件项目估算包括工作量估算和成本估算两个方面。由于两者在一定条件下可以相互转换，所以这里不刻意区分[1。软件项目中工作量的单位通常是人月。

一般说来有专家判定、类比、功能点估计法三种估算方法。

# 3.2.1.1专家判定

专家判定就是与一位或多位专家商讨，专家根据自己的经验和对项目的理解对项目成本做出估算。由于单独一位专家可能会产生偏颇，因此最好由多位专家进行估算。对于由多个专家得到的多个估算值，需要采取某种方法将其合成一个最终的估算值。可采取的方式有：

(1）求中值或平均值。  
这种方法非常简便，但易于受到极端估算值的影响而产生偏差。  
(2)召开小组会议。

组织专家们召开小组会议进行讨论，以使他们统一于或者同意某一估算值。该方法能去掉一些极为偏颇无知的估算，但易于受权威人士或能言善辩人士的影响。

# (3) Delphi技术

Delphi 是1948年 Rand 公司产生的一种预测未来时间的技术[7，随后在诸如联合规划之类的各种其他应用中作为使专家意见一致的方法。采用标准Delphi技术的步骤如下：

具体做法如图3-2所示：

![](images/d15cc689a39b4e69a2a35d76627650497fced60f5807e51b3c23a7bcc78d22a1.jpg)  
图3-2Delphi法示意图

协调员给每位专家一份软件规格说明书和一张记录估算值的表格；

专家无记名填写表格，可以向协调员提问，但相互之间不能讨论；

协调员对专家填在表上的估算进行小结，据此给出估算迭代表，要求专家进行下一轮估算。迭代表上只标明专家自己的估计，其他估计匿名；

专家重新无记名填写表格。该步骤要适当的重复多次，在整个过程中，不得进行小组讨论。

# 3.2.1.2类比

类比法就是把当前项目和以前作过的类似软件项目比较，通过比较获得其工作量的估算值[。该方法适合评估一些与历史项目在应用领域、环境和复杂度方面相似的项目，通过新项目与历史项目的比较得到规模估计。类比法估计结果的精确度取决于历史项目数据的完整性和准确度，因此，用好类比法的前提条件之一是组织建立起较好的项目后评价与分析机制，对历史项目的数据分析是可信赖的。

其基本步骤是：

(1）整理出项目功能列表和实现每个功能的代码行；  
(2)标识出每个功能列表与历史项目的相同点和不同点；  
(3)注意历史项目做得不够的地方;

(4）通过步骤1和2得出各个功能的估计值；

(5)产生规模估计。

软件项目中用类比法，往往还要解决可重用代码的估算问题。估计可重用代码量的最好办法就是由程序员或系统分析员详细地考查已存在的代码，估算出新项目可重用的代码中需重新设计的代码百分比、需重新编码或修改的代码百分比以及需重新测试的代码百分比。根据这三个百分比，可用下面的计算公式计算等价新代码行：

等价代码行=[（重新设计 $\% +$ 重新编码 $\% +$ 重新测试 $\% ) / 3 ] \times$ 已有代码行

比如：有10000行代码，假设 $3 0 \%$ 需要重新设计， $5 0 \%$ 需要重新编码， $7 0 \%$ 需要重新测试，那么其等价的代码行可以计算为：

$[ ( 3 0 \% + 5 0 \% + 7 0 \% ) / 3 ] \times 1 0 0 0 0 = 5 0 0 0$ 等价代码行意即：重用这10000代码相当于5000代码行的工作量。

# 3.2.1.3功能点估计法

功能点测量是在需求分析阶段基于系统功能的一种规模估计方法。通过研究初始应用需

求来确定各种输入、输出、计算和数据库需求的数量和特性(0]。通常的步骤是：

(1）计算输入，输出，查询，主控文件和接口需求的数目；   
(2)将这些数据进行加权乘。   
(3）估计者根据对复杂度的判断，总数可以用 $+ 2 5 \%$ ，0、或一25%调整。

# 3.2.2工作分解结构

软件项目进度计划管理的另一个重要环节是进行有效的工作结构分解。工作分解结构(Work Breakdown Structure，WBS)是对工作的分级描述。它可以将项目中的工作分解为更小的，易于管理的组成部分，直至最后分解成具体的工作的系统方法。它是项目规划的基础，是项目管理的主要技术之一。

WBS 的基本要素主要有三个：层次结构、编码和报告[18]。

# 3.2.2.1层次结构

WBS 结构的总体设计对于一个有效的工作系统来说是个关键。结构应以等级状或“树状”来构成，使底层代表详细的信息，而且其范围很大，逐层向上。

即 WBS结构底层是管理项目所需的最低层次的信息，在这一层次上，能够满足用户对交流或监控的需要，这是项目经理、工程和建设人员管理项目所要求的最低水平；结构上的第二个层次将比第一层次要窄，而且提供信息给另一层次的用户，以后依此类推。

结构设计的原则是必须有效和分等级，但不必在结构内建太多的层次，因为层次太多了不易有效地管理。对一个大项目来说，4到6个层次就足够了。

在设计结构的每一层中，必须考虑信息如何向上流入第二层次。原则是从一个层次到另一个层次的转移应当以自然状态发生。此外，还应考虑到使结构具有能够增加的灵活性，并从一开始就注意使结构被译成代码时对于用户来说是易于理解的。图3-3展示了办公自动化开发项目中的一种工作分解结构。

![](images/dcc8837b53e80edaf4cddd71961097bbd4c0567bd9714780459e5fc171224709.jpg)  
图3-3项目工作分解结构表

# 3.2.2.2编码设计

工作分解结构中的每一项工作或者称为单元都要编上号码，用来唯一确定项目工作分解结构的每一个单元，这些号码的全体称为编码系统。编码系统同项目工作分解结构本身一样重要，在项目规划和以后的各个阶段，项目各基本单元的查找、变更、费用计算、时间安排、资源安排、质量要求等各个方面都要参照这个编码系统。若编码系统不完整或编排不合适，会引起很多麻烦。

在 WBS 编码中，任何等级的一个工作单元，是次一级工作单元的总和。如第二个数字代表子工作单元(或子项目)一也就是把原项目分解为更小的部分。于是，整个项目就是子项目的总和。所有子项目的编码的第一位数字相同，而代表子项目的数字不同，再下一级的工作单元的编码依次类推，如上图3.3中所示。

# 3.2.2.3报告设计

设计报告的基本要求是以项目活动为基础产生所需的实用管理信息，而不是为职能部门产生其所需的职能管理信息或组织的职能报告。即报告的目的是要反映项目到目前为止的进展情况，通过这个报告，管理部门将能够去判断和评价项目各个方面是否偏离目标，偏离多少[19]。

# 3.2.3进度计划的技术方法

# 3.2.3.1甘特图(Gantt)

甘特图(Gantt)是美国工程师和社会学家在1916年发明的，又称横道图(BarChart，也称条形图)，是各种任务活动与日历表的对照图。甘特图主要用于对软件项目的阶段、活动和任务的进度完成状态的跟踪。

该方法依据软件项目WBS的各层节点的进度估计值，使用直观的甘特图显示工作进度计划和工作实际进度状态。甘特图是WBS的图示，也是工作完成状态的可视化快照(Snapshot)，能够动态地、实时地、直观地比较工作的进展状态。甘特图中横坐标是时间维，纵坐标是WBS维，甘特图的起点和终点分别表示阶段、活动和任务的开工时间和完工时间，甘特图的长度表示工期。甘特图的示例如图3-4所示。

<table><tr><td rowspan="2"></td><td rowspan="2">任务名称</td><td rowspan="2">开始时间</td><td rowspan="2">完成时间</td><td rowspan="2">持续时间</td><td colspan="8">2006</td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td>1调研</td><td>2008-3-10</td><td>2006-4-256周4.22天</td><td></td><td colspan="7"></td></tr><tr><td></td><td>2系统需求报告</td><td>2006-4-25</td><td>2006-4-25</td><td></td><td colspan="7"></td></tr><tr><td></td><td>3系统分析与设计</td><td>2008-4-26</td><td>2006-7-15 11周4天</td><td></td><td colspan="7"></td></tr><tr><td>4</td><td>系统分析设计报告</td><td>2006-7-15</td><td>2006-7-15</td><td>调</td><td colspan="7"></td></tr><tr><td></td><td>5编写代码及单元测试</td><td>2006-7-16</td><td>2006-11-7</td><td>16周3天</td><td colspan="7"></td></tr><tr><td></td><td>6系统软件</td><td>2008-11-7</td><td>2006-11-7</td><td></td><td colspan="7"></td></tr><tr><td></td><td>7系统测试和系统修改</td><td>2006-11-82008-12-25</td><td></td><td>6周6天</td><td colspan="7"></td></tr><tr><td></td><td>8系统测试报告</td><td>2006-12-252006-12-25</td><td></td><td></td><td colspan="7"></td></tr><tr><td>9</td><td>系统交付</td><td>2008-12-262006-12-31</td><td></td><td>天</td><td colspan="7"></td></tr></table>

在甘特图中，每一任务的完成不以能否继续下一阶段的任务为标准，其标准是是否交付相应文档和通过评审。甘特图清楚地表明了项目的计划进度，并能动态地反映当前开发紧张状况，但其不足之处在于不能表达出各任务之间复杂的逻辑关系。所以，甘特图大多用于小型项目。

# 3.2.3.2 PERT

PERT(计划评审技术，Program Evaluation and Review Technique)[是 50年代末美国海军部在研制北极星潜艇系统时为协调3000多个承包商和研究机构而开发的，其理论基础是假设软件项目持续时间以及整个项目完成时间是随机的，且服从某种概率分布。PERT可以估计整个项目在某个时间内完成的概率。

构造PERT陈需要明确三个概念：事件、活动和关键路线。事件（Events)表示主要活动结束的那一点:活动(Activities)表示从一个事件到另一个事件之间的过程；关键路线(Critical Path)是 PERT 网络中花费时间最长的事件和活动的序列。开发一个PERT网络要求管理者确定完成项目所需的所有关键活动，按照活动之间的依赖关系排列它们之间的先后次序，以及完成每项活动的时间。这些工作可以归纳为五个步骤：

(1）确定完成项目必须进行的每一项有意义的活动，完成每项活动都产生事  
件或结果;(2）确定活动完成的先后次序；(3）绘制活动流程从起点到终点的图形，明确表示出每项活动及其它活动的  
关系，用圆圈表示事件，用箭头线表示活动，结果得到一副箭头线流程图，我

们称之为PERT网络，图3-5表示PERT的标准术语；

![](images/05b784ee82fd681cc055b0884b5875a80a626ebd3bc0d5c70e7981f553f5c980.jpg)  
图3-5PERT的标准术语

(4）估算每项活动的完成时间；

(5）借助包含活动时间估计的网络图，制定出包括每项活动开始和结束日期的全部项目的日程计划。在关键路线上没有松弛时间，沿关键路线的任何延迟都直接影响整个项目的完成期限。

下面通过一个项目实例来对PERT技术加以说明。

(1）PERT对活动时间的估算

PERT对各个项目活动的完成时间按三种不同情况统计：

乐观时间（optimistic time）-—任何事情都顺利的情况，完成某项工作的时间：

最可能时间（mostlikelytime）—正常情况下，完成某项工作的时间；  
悲观时间（Pessmistic time）-一最不利情况下，完成某项工作的时间。

假设三个估计服从 $\pmb \beta$ 分布，由此可算出每个活动的期望 $t _ { i }$ ：

$$
 t _ { i } = \frac { a _ { i } + 4 m _ { i } + b _ { i } } { 6 }
$$

其中： ${ \pmb { a } } _ { i }$ 表示第 $i$ 项活动的乐观时间， ${ \pmb m } _ { i }$ 表示第 $\pmb { i }$ 项活动的最可能时间， $\pmb { b _ { i } }$ 表示第 $\pmb { i }$ 项活动的悲观时间。

根据 $\pmb \beta$ 分布的方差计算方法，第i项活动的持续时间方差为：

$$
\sigma _ { i } ^ { 2 } = \frac { ( b _ { i } - a _ { i } ) ^ { 2 } } { 3 6 }
$$

(2)项目周期的估算

PERT认为整个项目的完成时间是各个任务完成时间之和，且服从正态分布，完成时间 $\pmb { t }$ 的方差 $\pmb { s } ^ { 2 }$ 和数学期望 $\pmb { T }$ 分别等于：

g²-o²  
T-M  
标准差为： $\sigma = \sqrt { \sigma ^ { 2 } }$

据此，可以得出正态分布曲线。通过查标准正态分布表，可得到整个项目在某一时间内完成的概率。

# 3.2.3.3 CPM

关键路径法（CriticalPathMethod,CPM）是一项用于确定软件项目的起始时间和完工时间的方法。该方法的结果是指出一条关键路径，或指出从项目开始到结束由各项活动组成的不间断活动链。任何关键路径上的活动开始时间的延迟都会导致项目完工时间的延迟。正因为它们对项目完工的重要性，关键活动在资源管理上享有最高的优先权。

关键路径具有下列特征：

(1)网络图上至少存在一条关键路径;(2)关键路径是网络图中的最长路径;(3)关键路径的工期是完成项目的最短工期;(4)关键路径上的活动是关键活动，任何关键活动的延迟都会导致整个项目完成的延迟；(5)关键路径是动态变化的，随着项目的进展，非关键路径可能会变成关键路径。在项目初始策划和进度跟踪过程中，可确定和调整网络图，识别和监控关键路径。

![](images/6606613d662ac2f5148d59e1ba65da2376f0c5ca6f5f94beabc98f5f5a0afb3b.jpg)  
图3-6某项目网络图

在图3-6中，字母A、B、C、D、E、F、G、H、I、J代表了项目中需要进行的子项目或工作包，连线箭头则表明了工作包之间的关系，节点数字1,2,3,4,5,6,7,8则表明的是一种状况，从1开始，到8结束，中间的数字则表明上一工作包的结束和下一工作包的开始。

$\mathbf { A } { = } 1$ ，表示A工作包的持续时间为1天。由图中可反映出该项目的路径共有4条，它们的历时长度分别为：

$\scriptstyle \mathbf { A } + \mathbf { D } + \mathbf { H } + \mathbf { J } = 1 + 4 + 6 + 3 = 1 4$ （天）$\mathbf { B } + \mathbf { E } + \mathbf { H } + \mathbf { J } = 2 + 5 + 6 + 3 = 1 6$ （天）$\mathbf { B + F + J = 9 }$ （天） $\mathbf { C } + \mathbf { G } + \mathbf { I } + \mathbf { J } = 1 4$ （天）

关键路径是该图中最长的路径，即路径2，由B,E,H.3组成，历时16天。关键路径反映了完成项目需要的最短时间，其所有的组成工作包的执行情况都应给予密切关注，避免项目的延期完成。

# 3.3软件项目进度控制方法

# 3.3.1主要软件进度跟踪控制方法比较

表3-1主要软件进度跟踪控制方法比较  

<table><tr><td>方法名称</td><td>适用于WBS的 节点级别</td><td>结果表示</td><td>使用时机</td><td>方法意义</td></tr><tr><td>里程碑 进度</td><td>（1）阶段级 （2）活动级 （3）任务级</td><td>(1）偏差百分比 （2）偏差量折线</td><td>（1）项目周期内的阶 段节点 (2）阶段内的里程 碑、月、周节点</td><td>反映对项目交期、 阶段节点的进度 偏差程度</td></tr><tr><td>人为设定 活动进度</td><td>活动级</td><td>(1）进度百分比 (2）计划完成与实际 完成比较折线图</td><td>阶段内的里程碑、月、 周节点</td><td>主观假设的进度 关系</td></tr><tr><td>工作单元 进展</td><td>任务级</td><td>（1）进度百分比 (2）计划完成与实际 完成比较折线图</td><td>阶段内的里程碑、月、 周节点</td><td>任务完成的状态</td></tr><tr><td>挣值法</td><td>任务级</td><td>（1）进度与成本偏差 百分比 （2）BCWS、BCWP、ACWP 比较折线图</td><td>阶段内的里程碑、月、进度与成本完成 周节点</td><td>的状态</td></tr></table>

历史上，人们使用过多种软件进度跟踪方法，它们主要包括里程碑进度、

人为设定活动进度、工作单元进展、挣值法等[20，对这些方法有关特征的比较，如表3-1所示。

# 3.3.2里程碑进度

里程碑进度方法主要用于对项目总体进度的跟踪，尤其是对项目交付日期的持续跟踪。该方法度量里程碑的进度延迟（或提前).量，计算公式如下：

里程碑进度差异一项目周期

公式（3-4）

公式中项的解释如下：当对软件项目整体进度进行考察时，里程碑一般指软件项目生存周期内的阶段节点，项目周期指整个项目的工期；当对软件项目阶段的内部进度进行考察时，里程碑一般指阶段内的活动（或任务）节点，项目周期指该阶段的工期。

$\Delta _ { i }$ 是第 $\pmb { i }$ 个里程碑的进度延迟量，单位是天，计算如下：

(1)对每个已经完成的里程碑， $\Delta _ { j } =$ 第 $\pmb { i }$ 个里程碑实际完成日期-第i个里程碑计划完成日期；(2)对每个已经开始但尚未完成的里程碑， $\Delta _ { i } =$ 第 $\pmb { i }$ 个里实际开始日期一第$\pmb { i }$ 个里程碑计划开始日期。

项目周期是项目(或阶段)的持续时间(Duration)，单位是天，计算如下：

项目周期 $=$ 项目(或阶段)的计划完成日期一项目(或阶段)的计划开始日期

以上计算时， $\Delta _ { i }$ 取值可能为正数（对应进度滞后）或负数（对应进度超前）或为0（对应进度持平)，其累计值中正负数发生抵消。实际上，当WBS的阶段、活动或任务节点不在一个执行路径上时（如并行执行时)，一个节点超前或滞后对另一个节点在进度上没有影响，因此它们的进度差异不能抵消。为此，可以对里程碑进度差异公式进行改进，以 $\Delta _ { j }$ 的绝对值 $\left| \Delta _ { i } \right|$ 替代 $\Delta _ { i }$ 代入公式。原里程碑进度差异公式的计算结果掩盖了不在一个执行路径上的节点的无关性，易导致人们盲目乐观。改进后的公式反映了实际进度对项目交期、阶段节点的计划进度的绝对偏差程度，昭示了项目的计划水平或实施能力，具有很高的实用价值[21]。

# 3.3.3人为设定活动进度

人为设定活动进度方法主要用于对软件项目阶段的内部进度的跟踪控制。该方法对软件项目阶段内的里程碑点赋予进度百分比预算值，以测量和跟踪阶段内部的进度[22]。某项目在概要设计阶段的进度设置如表3-2所示，表中的缩写词是软件设计文档 SDD（Software Design Document）、软件需求规格说明 SRS(Software Requirements Specification）、数据流图 DFD(Data Flow Diagram）。

表3-2体系结构设计和文档编制的进度算法  

<table><tr><td>事件（活动）</td><td>完成进度的%</td></tr><tr><td>任务开始</td><td>10</td></tr><tr><td>完成DFD和相关算法定义</td><td>40</td></tr><tr><td>完成软件单元定义和结构关系定义</td><td>55</td></tr><tr><td>完成SDD到SRS的追溯</td><td>65</td></tr><tr><td>内部评审开始</td><td>75</td></tr><tr><td>内部评审结束</td><td>90</td></tr><tr><td>完成文档更新并提交基线</td><td>100</td></tr></table>

在早期的软件项目管理中，以上类似的进度设定方法使用比较普遍。该方法的问题是阶段内的活动（或任务）的进度设置完全凭主观假设，缺乏物理意义，并且带有乐观性当然，这也是当时缺乏有效的手段对软件进度进行度量的无奈之举。在挣值法出现以后，人们普遍使用挣值法，该方法被渐渐地抛弃。

# 3.3.4工作单元进展

工作单元进展方法主要用于对软件项目阶段的内部工程任务(即工作单元)完成状态的跟踪。该方法依据软件项目阶段内的详细WBS.的底层任务节点及其进度的计划值，观测任务的完成状态，计算公式如下：

公式（3-5)

使用工作单元进展方法跟踪软件项目阶段的进展情况，只考虑了任务是否完成的信息，不涉及任务的内部信息，是一种粗粒度的跟踪方法。使用该方法时，应该注意的是阶段内的工程任务的划分需要均衡，任务之间进度、工作量等估计值的差距应不超过一个数量级。只有任务划分遵循“粒度适当、大小可比”原则，使用该方法跟踪进度才比较有效，否则易导致盲目乐观（如当许多小任务都顺利完成而一个大任务没有按期完成时）或盲目悲观，对进度的评判带来不利。

# 3.3.5挣值法

挣值法主要用于对软件项目阶段的内部工程任务进度与成本完成状态的跟踪。该方法依据软件项目阶段内的详细WBS的底层任务节点的估计值，观测任务进度与成本的完成状态，计算公式如下：

公式（3-6)

采用挣值法跟踪项目的进度和成本时，使用下列3个变量：

(1)BCWS(Budgeted Cost of Work Scheduled)已计划工作的预算成本，统计本阶段迄今为止所有计划应完成的任务的工作量估计值；(2)BCWP(Budgeted Cost of Work Performed)已完成工作的预算成本，即.所谓的“挣值”，统计本阶段迄今为止所有已完成的任务的工作量估计值；(3)ACWP(Actual Cost of Work Performed)已完成工作的实际成本，统计本阶段迄今为止所有已完成的任务的工作量实际值。

挣值法利用以上3个变量生成一对李生指标：进度性能指标(SchedulePerformance Index，SPI）和成本性能指标(Cost Performance Index，CPI)。使用 SPI和CPI可以对软件项目在本阶段的进度和成本进行跟踪和分析。2个指标必须联合判别，判别准则如下：

(1)2个都为正，软件项目在进度和成本方面的进展较好，进度超，成本省；(2)2个都为负，软件项目在进度和成本方面的进展较差，进度拖，成本超；(3)一正一负，不能说明问题，需进一步分析；(4)任何一个为零，说明它代表的属性恰好达到预期目标，属于最佳状态；(5)任何一个不为零，需将其与事先设定的跟踪阈值进行比较，超出阈值范  
围即表示它代表的属性处于异常状态，需进一步分析并采取纠正措施以期重新  
将其纳入正常轨道。采用挣值法的前提是将任务假定为原子任务，对任务的进  
度度量使用二元度量，即任务要么未完成，要么完成。挣值法依据任务的工作

量和进度估计值跟踪软件项目阶段的进度与成本完成状态。在CMM等软件项目管理的多年理论探索和实践确证后，挣值法已经证实是对软件项目的进度、成本进行联合跟踪的有效方法[23]。

# 3.4现有方法的不足之处

在以往的项目管理中，甘特图之所以得到普遍应用，是因为它具有容易理解、作图方便的优点。但它带有明显的计划性和主观性，难以表现大型复杂项目的全貌，不能清楚地表示活动之间的依赖性，也不能表示个别活动在按时完成项目中的相对重要性。当一项工作不能按时完成时，不能反映其对整个进度的影响，更不宜做动态调整。因此它的应用受到一定限制。

PERT 在软件项目进度管理中得到一定程度的应用，却存在一些不足之处，主要表现为以下两点：用“三点法”来近似计算活动持续时间的期望值和方差，存在较大误差；忽略了进度计划中多条线路的共同作用对项目工期的影响。软件项目和一般的工程项目有一个重要的不同点，就是在软件项目开发中，会应用并行工程的思想，这部分内容会在后面章节重点介绍。软件并行开发方法节约开发时间，对需求不明确时能进行软件开发并最大限度的保证软件质量，降低开发风险，能在软件开发范围、软件开发时间、软件开发成本、软件质量之间找到最佳的平衡点将原有软件开发时的需求、设计、编码、测试结合在一起并行执行。而一般的工程项目的各个子阶段是串行的过程，按照先后顺序一步一步完成。

PERT 的计算误差较大，认为用该方法计算的活动期望值和方差的误差均较大且使得计算的项目工期偏小。对于一般性项目，PERT所作的活动持续时间服从分布的假设是成立的，前人所作的改进工作也是卓有成效的，但对于软件开发这种复杂项目来说，PERT的这个假设就显得脱离实际了，并且多条线路共同作用的影响也是不可忽略的。

针对软件开发项目并行开发的特点，我们需要找到一种方法，可以针对并行进度进行预测的方法，从而更有效的进行软件开发项目进度的计划和控制工作。

# 第4章基于马氏链的软件项目进度预测研究

# 4.1马尔可夫分析法的基本原理

# 4.1.1马尔可夫过程基本原理

按照系统的发展，时间可离散化为 $n = 0 , 1 , 2 , 3 , \cdots , i \cdots ,$ 对每个系统的状态可用随机变量表示，并目对应一定的概率，该概率称为状态概率。当系统出某一阶段状态转移到另一阶段状态时，在这个转移过程中，存在着转移的概率，称为转移概率。如果转移概率只与目前相邻两状态的变化有关，即下阶段的状态只与现在状态有关而与过去无关，那么这种离散状态按照离散时间的随机转移系统过程，称为马尔可夫过程[24]。

马尔可夫的数学模型表示如下：

设系统的每个阶段含有 $\pmb { S } _ { 1 } , \pmb { S } _ { 2 } , \cdots \pmb { S } _ { n }$ 个可能状态：

(1）该系统的初始阶段向量记为向量 ${ \pi } ( 0 )$ ，系统第 $\pmb { i }$ 阶段的状态向量记为$\pi ( i )$ ，两相邻系统出现出状态 $\pmb { S _ { i } }$ 变到 $s$ 的状态转移概率为 $p _ { i j } ( 1 \leq i \leq n , 1 \leq j \leq n )$ ，由 $P _ { i j }$ 构成的矩阵称为系统状态转移概率矩阵，记为 $\pmb { P }$ ，即 $P = \left( p _ { i j } \right) _ { n ^ { \ast } n }$ $\pmb { P }$ 的第 $\pmb { i }$ 行表示系统现阶段处于状态 $\pmb { S _ { i } }$ ，下阶段转移到 $\pmb { S _ { 1 } } , \pmb { S _ { 2 } } , \cdots , \pmb { S _ { n } }$ 状态的概率，所以，$\sum ^ { n } p _ { i j } = 1 , i = 1 , 2 , \cdots n$ 。这里，不同阶段的状念向量分别为：$\dddot { \pi } \{ 1 \} = \pi ( 0 ) p , \pi ( 2 ) = \pi ( 1 ) p , \cdots , \pi ( i ) = \pi ( i - 1 ) p , i = 1 , 2 , \cdots n \ o$

(2）假设系统发展过程状态向量 $\pmb { \pi }$ 满足条件： ${ \mathcal { R } } = { \mathcal { \pi } }$ ，则系统处于稳定状态。$\pmb { \pi }$ 为状态转移矩阵 $P$ 的不变向量，记 $\pi = \left( { \pmb x } _ { 1 } , { \pmb x } _ { 2 } , \cdots { \pmb x } _ { n } \right)$ ，且满足条件：

$$
\left\{ \begin{array} { l l } { \displaystyle \mathcal { W } = \pi } \\ { \displaystyle \sum _ { i = 1 } ^ { n } x _ { i } = 1 } \end{array} \right. .
$$

# 4.1.2马尔可夫链

有限个马尔可夫过程的整体称为马尔可夫链。马尔可夫链的运动变化分析，  
主要是分析研究链内有限马尔可夫过程的状态及相互关系，进而预测链的未来  
状况，据此做出决策。根据马尔可夫链的构成，其过程具有如下三个特点[25]：(1）过程的离散性。该系统的发展，在时间上可离散化为有限或可列个状态。

(2）过程的随机性。该系统内部从一个状态转移到另一个状态是随机的，转变的可能由系统内部的原先历史情况的概率值表示。

(3)过程的无后效性。系统内部的转移概率只与当前状态有关，而与以前的状态无关。即一个系统的某些因素在转移中第i次结果只受第i-1次结果的影响，与其它结果无关。

凡是满足以上三个特点的系统，均可用马尔可夫链研究其过程，并可预测其未来。而IT项目管理中的进度管理符合以上三个特点，是具有马尔可夫性的。

# 4.2软件开发项目分析

# 4.2.1软件开发经历阶段

自20世纪40年代世界上第一台电子计算机问世以来，软件开发大致经历了4个阶段：

第一个阶段从20世纪50年代初期至60年代初期。这一时期计算机系统中的多数软件由用户自己设计、使用与维护，其特点是软件规模小、开发不规范、文档不完全、开发过程很难管理。

第二个阶段从20世纪60年代中期至70年代末期。这一时期出现了“软件车间”，“软件车间”生产并推销自己的产品，从而改变了软件由用户自己开发以及使用的被动局面。由于软件是逻辑产品，每个软件都有自己的个性，准确理解软件内部结构是一件十分困难的工作，因此软件维护问题的矛盾日益加剧，最终导致了“软件危机”的出现，并变得十分严重。

第三个阶段从20世纪70年代末期至80年代末期。这一时期软件工程在软件开发中得到了广泛的应用，出现了各种类型的软件公司，使得软件的品种和质量都有了很大提高。

第四个阶段从20世纪80年代末期开始至今。随着计算机性能的提高、多媒体技术的出现以及网络的发展，软件的并行性和分布性要求不断增加，规模越来越庞大，分布性也越来越广。

# 4.2.2软件开发方法分析[2]

# 4.2.2.1生命周期法

从20世纪60年代开始，人们已经注意到管理信息系统的开发方法和工具。20 世纪70年代，系统开发的生命周期法诞生，它较好地给出了过程的定义，大大改善了开发的过程。该方法从整体出发，对软件开发和维护的复杂为体进行分解，把软件的开发划分为若干个阶段，每个阶段有相对独立的任务，然后逐步完成每个阶段的任务，前一阶段任务完成后，才进行后一阶段的任务。在每一阶段结束之前需要从技术和管理两方面进行严格的技术审查和管理复审，审查的主要标准就是每个阶段都应该提交和欲开发系统完全一致的高质量的文档资料。如果文档不合格，需重新修改后再审查，知道符合规定的要求。

在图41中，是一般的IT项目的流程图，其中有计划、需求分析、系统分析、系统设计、编码、单元测试、集成测试、系统测试、安装维护等几个阶段。每个阶段会分为几个子阶段，而且这些阶段是个生命周期过程，见图4-2。

![](images/bf07c2f1e8b80cfe5afb58e4572d47e4071e0d2c9e23f7f9ad024876ba14fe44.jpg)  
图4-1IT项目管理流程图

![](images/3e223764b806ba0a51d479a4279a2832af0b43d0247bf5ff78d5501f70e7fd64.jpg)  
图4-2IT项目的生命周期

表4-1各阶段主要任务  

<table><tr><td></td><td>阶段</td><td>主要任务</td></tr><tr><td rowspan="3">系统 规划</td><td>战略规划</td><td>根据组织的日标发展战略确定管理信息系统发展战略</td></tr><tr><td>需求分析</td><td>提出总体结构方案；制定主发展计划，安排项目开发计划</td></tr><tr><td>资源分配</td><td>指定软硬资、数据通信设备、人员、技术、服务、资金等计划，</td></tr><tr><td rowspan="3">系统 开发</td><td>系统分析</td><td>系统初步调查，开发项日的可行性研究、系统详细调查和逻辑设计， 建立数据库逻辑模型，完成数据字典的编制，进行功能分析，确定 新系统逻辑结构</td></tr><tr><td>系统设计</td><td>系统配置方案设计，总体设计，代码设计，数据库设计，处理过程 设计（输出设计、输入设计、处理流程图设计及编写程序设计说 明5）</td></tr><tr><td>系统实施</td><td>建立系统实施的组织结构，硬件的购置及安排，程序的编写和软件 购置，操作人员的培训，数据和系统的转换、运行</td></tr><tr><td colspan="2">系统维护</td><td>软件的维护，应用程序维护，数据文件的维护，代码的维护，机器、 设备的维护，机构和人员的变动：系统管理；系统运行实用性评价</td></tr><tr><td colspan="2">系统更新</td><td>现行系统问题分析，新系统的建设</td></tr></table>

生命周期法是基于--些理想化的假设，因此具有以下不足：

(1）开发过程复杂，周期长、成本高。(2）软件重用程度很低。实际上除了一些接口十分简单的标准数学函数是经常重用之外，对一些其他系统的软件开发仍要作大量重复而又烦琐的工作，思维成果的可重用性差。(3）软件仍然很难维护。统计数字表明，用生命周期法开发出的软件维护的费用是软件开发的费用的几倍。据报导，20世纪80年代美国一年花费的软件维护费用高达300多亿美元。(4）软件往往不能真正满足用户需要。主要表现为，开发人员不能完全获得或不能彻底理解用户的需求，以致开发出的软件系统与用户预期的不一致；所开发出的系统不能适应用户需求经常变化的情况，系统的稳定性和可扩充性不能满足要求。

出现问题的原因主要有以下几点：

(1）僵化的瀑布模型：它强调预先定义需求的重要性，而管理信息系统的需求往往是模糊的，很难预先准确的指定。许多用户对他们的需求最初只有模糊笼统的概念，项目参与者之间也存在通信鸿沟，很难做到完全沟通和相互理解。在需求分析阶段定义的用户需求，常常是不完整的和不准确的。同时，各种稳当本质上都是被动、静止的通信工具，通过他们来深刻理解一个动态系统是困难的。另外，预先定义的需求可能是随时间变化的，当软件开发出来的时候就已经过时了，不符合那时的用户需要了。

(2)结构化技术的缺点：其稳定性、可修改性和可重用习惯都比较差。用户需求的变化大部分是针对功能的，用这种技术设计出来的系统结构常常是不稳定的，用户需求的变化往往是造成系统结构的较大变化，从而需要花费很大的代价才能实现这种变化。

# 4.2.2.2原型法

20 世纪70年代末至80年代初，随着计算机硬件和软件技术，包括计算机网络和数据库技术不断更新，企业的管理模式、组织机构、人员的变化对需求的修改和变化更多。人们希望能够快速生成一个系统的“雏形”，然后在这个基础上进行修改完善，直至符合要求，于是系统开发的原型法产生了。原型法认为，需求的反复和多变是正常的、不可避免的，因此反复修改也是必要的，应该鼓励用户对需求提更多的、更高的要求，从而使未来的新系统提供的信息真正地满足管理和决策的需要。原型法的开发思想是：对需求简单快速分析后，利用先进的开发工具，尽快构造出一个原型系统提供给用户评价、试用，在试用中不断修改完善原型系统，直到用户满意为止。如果原型系统离要求相差太远，需要重新构造。它的出发点是：用户只有看到一个具体的系统，才能清楚地理解到自己的需要及系统还存在的缺陷，这也说明了并非所有的需求都能预定义。在最终评价系统之前，通过动态模型的演示比书面的文档和图表的表达更为直观、更为生动，而且模型在演示的过程中可以修改和完善，运用模型方法构造模型，使用户和开发者协调一致，及早暴露出潜在问题以作修改。

原型法，对于比较小的系统采用该模型能显出较高的效率，但在大的复杂的信息系统中，由于经常要与用户沟通，如果用户的需求不明确，经常调整，则花在沟通的时间过长，会有难以按时完成的风险。

# 4.2.2.3面向对象法

面向对象法是在20世纪80年代末期各种面向对象的程序设计方法（如 $\mathbf { C } { \boldsymbol { + } } { \boldsymbol { + } } ,$ 的基础上逐步发展起来的。面向对象法在20世纪80年代已用于计算机科学，20 世纪80年代末开始用于管理信息系统。在面向对象法之前的程序设计是面向过程的，是为了解决某个问题的，而面向对象法符合软件工程的快速原型开发要求，自顶向下，从抽象到具体，在最高层次上先设计系统的功能、接口界面，而推迟具体细节的实现。面向对象法的关键是将开发的程序分解为类，类有可继承性，使程序有可重用性。面向对象法有以下特点：它把数据和操作绑扎在一起作为一个对象，数据是主动的，操作跟随数据，不像通常的程序，程序是主动的，而数据是被动的；面向对象的方法很容易做到程序重用，使新系统开发和维护系统很相似，因为均是重复使用已有的部件；面向对象的方法用于企业管理时就像给出一个企业模型，模拟企业的运行，开发者和企业管理者之间只需用企业语言沟通，如会计、顾客、报告等；面向对象的方法也特别适用于图形、多媒体运用的复杂系统。

# 4.3软件项目开发中的并行工程研究

60年代后期，为克服“软件危机”诞生了软件工程学，为软件的开发和维护注入了生机和活力。随着实践的深入，人们逐渐认识到：在软件开发过程中，很多开发活动实际上是可以并行的[27][28]，这一点在开发实践中得到了验证。例如，在编码阶段，可以出多个小组同时对不同的模块进行编码。当然，此时的并行粒度是很细的。并行开发意味着多个开发小组并行地执行多个开发活动，例如，并行地进行需求分析或测试。面向对象的软件开发方法以数据为中心设计，软件系统是由对象组成的集合。面向对象的方法以对象作为软件系统的基本元素，对象之间具有并行性，封装性以对象为单位实现，对象之间的接口是明确定义的。由于面向对象的软件开发所具有上述特点，很适宜于把并行开发的思想引入面向对象的软件开发过程。以对象作为可并行开发的原子单元，实现软件系统的粗粒度并行开发，使并行开发在较高抽象级上进行[29][30]。

# 4.3.1软件并行开发简述

软件项目阶段并行开发是指在软件项目开发的过程中，按照一定的划分机制，将软件项目过程分割成系列相对独立的开发阶段，每个阶段都有个完成标志，只有实现这个标志，才表明该阶段工作的结束。在某阶段内，项目团队的各项资源相对独立，成员间有独立分工也有协作交流，任务间存在并行、协同和交叉等复杂关系，阶段内有顺序、迭代和螺旋等多种项目演义模式。当阶段内所有成员都完成任务，才可进入下阶段，否则首先完成工作的成员要等待没

有完成工作的成员。

就面向对象的软件并行开发过程来说，我们把软件系统的开发过程划分为若干个可以并行的成分。这个成分称之为并行开发进程（ConcurrentDevelopmentProcess）。并行开发进程是一个动态概念，和操作系统中的进程概念有类似之处。并行开发进程可以定义为：并行开发进程 $\displaystyle { \overline { { \mathbf { \Omega } } } }$ 开发小组 $^ { + }$ 软件对象$\star .$ 对软件对象的开发活动，或者说，并行开发进程是一个开发小组对一个相对独立的软件对象的动态开发过程。

在此，我们把整个面向对象的软件并行开发活动看作是一个并行系统，称作并行开发系统。并行开发进程是对并行开发系统一种动态描述。此系统中的实体是开发小组，实体属性是被开发的软件对象，行为是开发软件对象的活动。每个并行开发进程完成一个子系统或一个模块的开发任务，当各个开发进程都完成之后，进行系统集成和测试，最终完成整个系统的开发，如图4-3所示。

![](images/ac7ef07213a4b91de065e51cb84950412cdf53bce8ea34b09972142369f253c7.jpg)  
图4-3并行的开发过程

# 4.3.1.1传统软件开发模式的不足

“传统的串行工程方法是基于亚当·斯密的劳动分工理论。西方把这种方式称为“抛过墙法”（throwoverthewall)。”这种方法相对早期的手工作坊式开发是一个巨大的进步，但还存在很多问题，具体表现在以下几个方面[2]。

(1)项日参与者之间存在通信鸿沟。这往往会使开发效率和软件可靠性降低，并且所开发的软件常不能真正满足用户的需求。

(2)开发的反复次数过多，造成开发周期变长，成本增加，生产率低。发现缺陷后，修改周期长，最后造成软件质量差。

(3）传统的串行工程方法是以职能和分工任务为中心的，不一定存在完整的、统一的软件产品概念，对各部分工作的评价，主要是看是否出色地完成交给他的任务，而不是看整个产品的整体优化。例如，就设计而言，主要是看设计方案是否有创造性，软件产品是否有优良的性能，但是不能保证这个方案是可开发的，也不能保证能在要求的时间内开发出来。对软件测试人员，主要看测试方案是否达到要求，能否尽可能多的找出缺陷，但是如果在开发后期发现缺陷就已经晚了，“质量不是靠测试来提高的”，这时尽管可能将缺陷修复，但软件质量并没改善。

# 4.3.1.2软件开发的井行工程的含义

多年来，也许没有一种方法能像并行工程那样能将先进的管理思想与产品开发技术有机的结合起来，使产品开发小组成员采用集成化和并行协同的工作方式来开发产品。从而达到缩短产品开发周期，降低开发成本，提高产品质量的目的。根据R.I.Winner在国防分析研究所的研究报告定义：“并行工程是集成，并行设计产品和相关过程（包括制造和支持）的系统方法。这种方法可以使产品开发人员从一开始就能够考虑到产品从概念设计到消亡的整个生命周期里的所有因素，包括质量、成本、作业调度以及客户需求等”。并行工程提倡信息交流、资源共享、过程优化、人员协同。这些特点是传统的产品开发模式无法比拟的。并行工程的基本思想是在信息集成的基础上实现产品开发的各个开发过程的重组、优化、集成和资源共享，并且强调过程和人员的协同与受控。它充分体现了集体出智慧，团结出效益的集体主义精神。通过运用计算机网络技术，并行工程突破了地域和时空的限制。现在，专业学科背景以及不同部门和不同工作岗位己不再是阻碍信息交流，妨碍资源共享的障碍。相反，倒成了扬长避短、优势互补、群策群力协同开发产品的优势。总之，并行工程带给企业的将是更高的产品质量、更低的开发成本、更短的开发周期、更灵活的组织形式和更加合理的资源配置。

需要指明的是，事物的发展是一个否定之否定的过程，软件开发的并行工程是软件工程发展的新阶段，绝不是手工作坊式开发方法的回归。软件开发的并行工程决不是不要分工，而是在严格分工的基础上的并行化，是目前软件工程方法的螺旋上升的新阶段。

# 4.3.2软件开发的并行工程的本质

针对软件这种特定产品，软件开发的并行工程相应的本质特征为[33]

(1）强调设计的可实现性、可集成性、可测试性、可开发性、可使用性、可维护性。

在设计时，要充分考虑己有的软硬件技术条件，使系统设计方案是可实现的：要使设计的各模块与整个系统进行良好的集成；设计的系统及其模块要能进行严格的测试；要充分考虑自己的软硬件配置和人员的能力，使设计可以实现；要使设计出的系统能够满足用户的需求；要使设计的系统便于以后的升级和维护。在开发过程中强调而向整个过程和产品。设计人员在设计时不仅要考虑设计，还要在其他员工的协助下考虑这种设计的可实现性、可测试性、可开发性、可维护性等等。如测试人员在开发时就要参与，以便考虑设计的可测试性。所以整个开发的每个子过程都要着眼于整个过程和产品。从串行到并行，是观念上的很大转变。

注意可实现性和可开发性是不同的，“可实现性”主要是从软件开发技术的角度，看能否将一个软件的设计用己有的技术实现。而“可开发性”除了“可实现性”这一层含义外，主要指软件开发的并行工程要考虑开发软件的企业自身的软硬件和人力资源。

(2)在整个开发过程中强调软件的设计、实现、测试等开发子过程的并行。  
(3）在开发过程中强调系统集成与整体优化。

软件开发的并行工程强调系统集成与整体优化，他并不完全追求单个部门、局部过程和单个模块的最优，而是追求全局优化，追求软件产品整体的竞争能力。设计某个模块时要考虑与其他模块之间的集成，要有整个系统的概念，要考虑设计的模块能方便安全地为其他模块提供数据、服务。对软件产品而言，这种竞争能力就是软件产品的TQCS 综合指标一交货期、质量、价格和服务。在不同情况下，侧重点可以不同。如在某个阶段，交货期可能是关键因素，而另一阶段可能是质量、价格或是他们中的几个综合指标。对每一个产品而言，企业都对他有一个竞争目标的合理定位，因此软件开发的并行工程应围绕这个目标来进行整个软件开发活动。只要达到整体优化和全局目标，并不追求每个环节的局部工作最优。因此对整个工作的评价是根据整体优化结果来评价的。

# 4.3.3软件开发并行工程的组织形式

并行工程的开发小组是由跨部门人员组成的多功能小组(cross一functionalmultidisciplinary development team)，或称 IPT 小组(Integrated Product Team）。软件开发并行工程也采用这种理念，组织适合软件开发的IPT小组。这种组织形式是保证软件开发并行工程实现的重要管理手段[34。

要实现并行工程，使各部门人员在一起高效和谐的协同开发，必须打破原有的各自对自己部门负责的组织形式，加强各个部门之间的信息沟通和交流。因此建立一个有利于信息交流的组织形式一一跨部门的组织形式，是实施并行工程的首要条件。软件开发并行工程的开发小组是出跨部门人员组成的多功能小组。这个小组要包括需求分析、设计、实现、测试、销售、服务等各部门人员，有时还包括用户、供应商或协作公司的代表。总之，只要是与产品整个生命周期中有关的，而且对该产品的本次设计有影响的人员都需要参加。

一般认为，为了使小组成员之间有效地相互作用，小组人数不能超过8--12人。因此完整的多功能小组往往是由许多分小组组成。有一种方法是按照模块分解来建立一个分层次的多级的结构。通常出低一级小组的成员组成较高一级小组，并将其他小组的结果集成起来。

一般一个小组可出组长、系统设计人员(可兼几个组)、详细设计人员、测试人员、编码人员、质量工程师(可兼几个组)、系统集成人员(可兼几个组)、系统测试人员(可兼几个组)、软件配置管理人员组成，其中系统集成和测试人员是为了保证模块与整个系统能良好的集成。而质量工程师是独立和不可缺少的，他要保证开发的每一步规范、严格、一致。测试人员不能由设计人员兼职，特别是系统测试人员必须是独立的，单元测试可出编码人员进行，但必须经过质量工程师的审查，并有规范完整的文档、代码和注释。

# 4.3.4软件开发并行工程的过程模型[10][33][35]

“有效的软件项目管理需要关注的因素依次为：人(People)、产品(Product)、过程(Process)和项目(Project)。这个顺序不是任意的"。

因此，软件开发并行工程最重要的是对人员的管理。但是过程模型也起着重要作用，目前过程模型有很多种，如瀑布模型、原型系统模型、快速应用开发模型、螺旋模型、并行开发模型、统一软件开发过程等等，其中瀑布模型是

其他模型的基础。这些模型都有各自的特点，也都可以根据实际情况被采用。  
要强调的是，并行是相对串行而言的，而过程模型只是其采用的技术工具。

以上各种模型在并行开发中相对于串行开发的不同点主要表现在：开发小组是跨部门人员组成的多功能小组，由于在开发一开始就考虑各个子过程，这样软件开发的反复迭代次数大大减少，并且软件的质量得到很好的保证。以最简单(也是最基本)的瀑布模型为例，串行开发的瀑布模型如图44所示，并行开发的瀑布模型如图4-5所示，由于依靠多功能小组的组织形式，同样是瀑布模型，并行化后，可以使反复的次数大大减少，尽管开始阶段(分析)可能用的时间较多，但交付期却大大缩短。

![](images/32d060788b339455f730d338632ba10facc48d8ebf22fb0be08f452af4c445f0.jpg)  
图44传统串行开发模型

![](images/ec22d293f8a7941cf3eb11ae614af207050f66f3ffbe462fde844a35909b47cf.jpg)  
图4-5并行开发模型

# 4.3.5软件开发并行工程的模式

定义1在信息系统开发生命周期中，我们把系统分析(需求分析)、概要设计、详细设计定义为上游工程。

定义2在信息系统开发生命周期中，我们把系统实施、系统测试、系统运行与维护定义为下游工程。如46所示。

![](images/22add0a5604199817e0e48d6b0c68d52b787f99692968017b1ceac3a8e64949a.jpg)  
图4-6信息系统开发的并行工程模式

统计分析表明[36]，一般产品成本的 $83 \%$ 以上在产品开发的早期阶段—一概念设计、结构设计、详细设计、过程设计一被确定，而这一阶段本身所占有的费用仅为产品全部成本的 $7 \%$ 以下，如图4-7所示。信息系统作为一种产品，根据我们的经验，基本遵循这一分析结果(上游工程对应于这里所说的早期阶段)。

![](images/06a2d9a9db697726cef5fc7ac79821423e545a583ac9c1d91f179dc5b591d3d9.jpg)  
图4-7产品开发过程一成本曲线

在信息系统的开发过程中，许多开发人员包括项目的组织者往往忽略上游工程的工作，或者简略地考虑上游工程后就仓促地开始下游工程的设计，进入系统的具体实施工作(如编程、硬件调试等)，结果随着工作的深入，发现前期设计中的问题越来越多，必须从头修改，造成大量的人力、物力和财力损失，同时也使信息系统开发的时间延长，产品投入市场延期。从并行工程的角度来看必然会产生不良后果。并行工程方法要求开发者从一开始就考虑产品的生命周期的全部因素，要求在上游工程的设计中就提前考虑下游工程的设计工作，将错误或缺陷发现在最容易修改的上游工程中。应用这一原理，我们在软件开发过程中，必须充分重视上游工程的工作，在上游工程的设计中把下游工程的问题提前考虑好，因为上游工程中的每一后续过程都对下游工程产生影响。因此，信息系统开发的运行模式如图4-8所示，在上游工程的设计中开发团队集中起来同步地设计或考虑整个的生命周期的所有因素这是一个迭代的过程。

![](images/aa80259b54c4b62e000530165dbe4ca36c353e4499223074c40cad148c39d3c3.jpg)  
图4-8软件并行开发的运行模式

# 4.3.6阶段并行开发模型描述

阶段并行开发模型的构建基于下列思想：模型设计初始阶段并不追求最优的进度规划，而是在平衡项目稳定性的基础上，依靠局部优化，寻求总体项目进度的最优近似解[37]。

(1）设 $T _ { \mathfrak { o } }$ 为项目划分阶段的时间消耗，第i阶段需要的时间为 $T _ { \iota }$ ，则项目总体时间初步估计为：

$$
M _ { 0 } = T _ { 0 } + \sum _ { i = 1 } ^ { n } T _ { i }
$$

公式（4-1）

(2)在项目的起始阶段0，项目并没有开始，需要展望第1到 $\pmb { n }$ 阶段，并给出第1到 $\pmb { n }$ 阶段具体任务分解 $\pmb { W _ { i } }$

(3）对于第 $\pmb { i }$ 阶段 $( i = 1 , 2 , \cdots , n - 1 )$ ，在完成本阶段任务的同时，需要展望第$\pmb { i } + \pmb { 1 }$ 到第 $\pmb { n }$ 阶段。若本阶段所产生的变动，能够在剩余的第 $i + 1$ 到第 $\pmb { n }$ 阶段以及项 $\mathbf { I I }$ 截止时间内调整，则根据达到本阶段完成标志的工作时间 $\pmb { T _ { i } }$ 给出第 $\pmb { i } + \pmb { 1 }$ 阶段能够达到的最佳具体任务分解 $\boldsymbol { W } _ { i + 1 }$ ，以及对第 $i + 1$ 到第 $\pmb { n }$ 阶段计划完成时间 $\pmb { T } _ { i k }$ 进行调整。 $\pmb { T } _ { i \star }$ 表示在阶段i对后续阶段 $\pmb { k }$ 做出的调整， $( k = i + 1 , \cdots , n )$ 。出此，项目总的时间调整为：

$$
M _ { i } = T _ { 0 } + \sum _ { k = 1 } ^ { i } T _ { k } + \sum _ { k = i + 1 } ^ { n } T _ { i k } \left( i = 1 , 2 , \cdots n - 1 \right)
$$

若出现不可调整的重大问题，即第 $_ i$ 阶段所产生的影响，已经无法在后续的阶段进行调整，则重新划分阶段，令 $\pmb { T _ { 0 } }$ 为： $T _ { 0 } = M _ { i - 1 } + T _ { k }$ 返回(1)

(4）在最后阶段 $\pmb { n }$ ，合并所有阶段时间，得出项目的最终进度时间为：$M _ { n } = T _ { 0 } + \sum ^ { n } T _ { i } ^ { \cdot }$ ·公式（4-3）其中， $\pmb { T _ { i } } ^ { \dagger }$ 裴示各个阶段实际的工作时间。

# 4.4引入马氏链预测软件项目进度

# 4.4.1网络图的绘制关键[38]

在引入马氏链预测软件项目之前，我们需要绘制出相应工作的网络图。绘制箭线网络图的关键是：一要弄清各工序间的逻辑关系，二要力争使虚工序的使用达到最少。虚工序产生的情况的情况一般有两种，一是当两个或两个以上的工序有完全相同的紧前工序和紧后工序时：二是当两个或两个以上的工序有部分相同(但不是完全相同)的紧后工序(紧前工序时)时。

(用顺推法绘制网络图)设 $E \big ( \boldsymbol { M } \big ) , E \big ( \boldsymbol { N } \big )$ 分别为 $M , N$ 的紧后工序集合，$e ( M ) _ { \ u { h } } e ( N )$ 分别为工序 $M , N$ 的紧前工序集合。

(1)当 $e ( M ) = e ( N )$ 时，且 $E { \big ( } M { \big ) } = E { \big ( } N { \big ) }$ 时，则工序 $\pmb { M }$ 和N的终节点之间存在一虚工序，方向任意(可以从 $\pmb { M }$ 的终节点指向 $\pmb { N }$ 的终节点，也可以从 $\pmb { N }$ 的终节

点指向 $\pmb { M }$ 的终节点)。

(2）若 $E { \big ( } N { \big ) } \ J = E { \big ( } M { \big ) }$ 且 $E \big ( N \big ) \subset E \big ( M \big ) \asymp \Phi$ 时， $E ( M )$ 里的工序数目 $\pmb { P } ( \pmb { M } )$ 大于$E ( N )$ 里工序数目 $P ( N )$ 时(或 $P ( M ) \leq P ( N ) )$ ，则工序 $\pmb { M }$ 和 $\pmb { N }$ 的终节点存在一道虚工序，方向应当从 $\pmb { M }$ 的终节点指向 $\pmb { N }$ 的终节点(或从 $\pmb { N }$ 的终节点指向 $M$ 的终节点)。

当两个或两个以上的工序有完全相同的紧后工序且紧前工序不同时,这两个或两个以上的工序的终节点重合，即产生交点。

# 4.4.2并行转化串行

由于软件项目阶段不同于其它工程项目，它很多时候都是多个阶段并行执行。如果直接将马氏链应用于软件项目进度的预测，则仅仅只能预测一条串行的线路。比如我们可以引入马氏链来预测关键路径上各阶段的执行情况，因为关键路径上的活动是关键活动，任何关键活动的延迟都会导致整个项目完成的延迟。

![](images/b3df6b9c7fa4bed83238f3fa957bc97d0489b62a8ce8f8f045e30a51528f81d7.jpg)  
图4-9软件并行开发阶段示意图

如图4-9所示，根据花费时间最大的路径为关键路径，所以，1经过3到2这条路径是关键路径。令从状态1经过状态3到状态2的概率为 $\pmb { \alpha } _ { \mathrm { I } }$ ，期望值为 ${ \pmb { \omega } } _ { 1 }$ 。但是，这样预测的结果是比较片面，并且关键路径是随时间的改变而改变的，并不是固定的。另外，实际上状态2还受到状态3和状态4的影响。如上图所示，当状态4完工的时候，状态5还没有完工。而3，4，5的后一个状态都是2。所以在将马氏链应用于软件项目进度预测之前，需要将并行的软件项目阶段转化为串行阶段，然后再引入马氏链来预测软件项目进度。并行状态转换成串行状态的过程中，引入DELPHI法。

我们根据状态结点的数量进行分类研究。

(1）两个状态。如图4-10所示。

![](images/f061a2fb5dfb2517d19bb13cb8d4ae9f2a5f2edb008900b810549bbe778e1d93.jpg)  
图4-10两个状态结点执行示意图

这种情况已经满足应用马尔可夫分析模型的并行状态要求，所以无需做任何转化。

(2)三个状态。这里我们只考虑三个状态的并行情况。如图4-11所示。

![](images/0dd1e6709bc9132643669ade666a99e506eab666cd5e8c0015bb263c86299d66.jpg)  
图4-11三个状态结点执行示意图

令从状态1经过状态3到状态2的概率为 $\pmb { \alpha } _ { 1 }$ ，期望值为 ${ \pmb { \omega } } _ { 1 }$ ，状态1直接到状态2的概率为 ${ \pmb { a } } _ { \pmb { z } }$ ，期望值为 ${ \pmb { \omega } } _ { 2 }$ ，则状态2的期望值为

$$
\boldsymbol { \varpi } _ { 1 } = \alpha _ { 1 } \omega _ { 1 } + \alpha _ { 2 } \omega _ { 2 } \big ( \alpha _ { 1 } + \alpha _ { 2 } = 1 \big )
$$

其中：ω,∈[-1,]， $\left\{ \begin{array} { l l } { { \pmb { \varpi } } < { \pmb { \beta } } } \\ { { \pmb { \varpi } } \in \left[ - { \pmb { \beta } } , { \pmb { \beta } } \right] } \\ { { \pmb { \varpi } } > { \pmb { \beta } } } \end{array} \right.$ 推后 持平，这里 ${ \pmb \beta } { \in } [ - 1 , 1 ]$ 是由专家给定的值。提前

(3)四个状态。这里我们只考虑四个状态的并行情况。该部分又细分为3种情况。

$\textcircled{1}$ ，第一种情况，如图4-12所示。

![](images/****************************************de9907be86fbe27a9f2f2627.jpg)  
图4-12四个状态结点执行示意图情况一

令从状态1经过状态3到状态2的概率为 $\pmb { \alpha _ { \uparrow } }$ ，期望值为 ${ \pmb { \omega } } _ { 1 }$ ，状态1经过状态4到状态2的概率为 $\pmb { \alpha } _ { 2 }$ ，期望值为 ${ \pmb { \omega } } _ { \pmb { z } }$ ，状态1直接到状态2的概率为 $\pmb { \alpha _ { 3 } }$ ，期望值为 ${ \pmb { \omega } } _ { \mathbf { 3 } }$ 。则有状态1经过状态5到达状态2的期望值为：

$$
{ \pmb { \varpi } } _ { 2 } = \alpha _ { 1 } \omega _ { 1 } + \alpha _ { 2 } \omega _ { 2 } + \alpha _ { 3 } \omega _ { 3 } \big ( \alpha _ { 1 } + \alpha _ { 2 } + \alpha _ { 3 } = 1 \big )
$$

$\textcircled{2}$ ，第二种情况，如图4-13所示。

![](images/****************************************99100b2a05d6a2e54b6d36a3.jpg)  
图4-13四个状态结点执行示意图情况二

状态1、3、4，状态2、3、4分别可以作为三个状态结点的情况处理，则转化为两个状态结点的情况。令从状态1经过状态3到状态4的概率为 $\pmb { \alpha } _ { 1 }$ ，期望值为 $\pmb { \omega } _ { 1 }$ ，状态1直接到状态4的概率为 $\pmb { \alpha _ { 2 } }$ ，期望值为 ${ \pmb { \omega } } _ { \pmb { 2 } }$ ，状态3经过状念4到状态2的概率为 ${ \pmb { \alpha } } _ { 3 }$ ，期望值 $\omega _ { 3 }$ ，为状态3直接到状态2的概率为 $\pmb { \alpha _ { 4 } }$ ，期望值为 ${ \pmb { \omega } } _ { \pmb { 4 } }$ 。则状态5的期望值为

$$
{ \boldsymbol { \varpi } } _ { 3 } = \alpha _ { 1 } { \boldsymbol { \omega } } _ { 1 } + \alpha _ { 2 } { \boldsymbol { \omega } } _ { 2 } ( \alpha _ { 1 } + \alpha _ { 2 } = 1 )
$$

状态6的期望值为

$$
\boldsymbol { \varpi } _ { 4 } = \alpha _ { 1 } \omega _ { 1 } + \alpha _ { 2 } \omega _ { 2 } \big ( \alpha _ { 1 } + \alpha _ { 2 } = 1 \big )
$$

$\textcircled{3}$ ，第三种情况，如图4-14所示。

![](images/****************************************6e47c74cb01d731b04798580.jpg)  
图4-14四个状态结点执行示意图情况三

该种情况按照虚结点的画法，转化成了串行状态，则可以直接应用马尔可夫分析模型。

(4）五个状态。这里我们只考虑五个状态的并行情况。如图4-13所示。  
$\textcircled{1}$ 、第一种情况，如图4-15所示。

![](images/****************************************3e51ebb1e468520e6fed7b67.jpg)  
图4-15五个状态结点执行示意图情况一

令从状态1经过状态3到状态2的概率为 $\pmb { \alpha _ { \uparrow } }$ ，期望值为 ${ \pmb \omega } _ { 1 }$ ，状态1经过状态4到状态2的概率为 $\pmb { \alpha } _ { 2 }$ ，期望值为 ${ \pmb { \omega } } _ { 2 }$ ，状态1经过状态5到状态2的概率为$\pmb { \alpha _ { 3 } }$ ，期望值为 ${ \pmb { \omega } } _ { 3 }$ ，则有状态1经过状态6到达状态2的期望值为：

$$
{ \pmb { \varpi } } = \alpha _ { 1 } \omega _ { 1 } + \alpha _ { 2 } \omega _ { 2 } + \alpha _ { 3 } \omega _ { 3 } \quad \left( \alpha _ { 1 } + \alpha _ { 2 } + \alpha _ { 3 } = 1 \right)
$$

公式（4-8)

$\textcircled{2}$ ，第二种情况，如图4-16所示。

![](images/****************************************15f17ab0c7ee1ad86452df69.jpg)  
图4-16五个状态结点执行示意图情况二

状态1、3、4、5可以作为(3) $\textcircled{1}$ 情况来考虑，可以转化为串行状态，如图4-16所示。

$\textcircled{3}$ ：第三种情况，如图4-17所示

![](images/****************************************40b27ad4426fe5ff47393a19.jpg)  
图4-17五个状态结点执行示意图情况三

状态1、3、5、6可以作为(3) $\textcircled{1}$ 情况来考虑。这里主要分析了五个状态结点中几个比较典型的情况。当状态结点大于5的时候，我们都可以采用以上方法，把复杂问题简单化。所有的分析均满足以下条件：

$$
{ \begin{array} { r l } { \cdot _ { i } \in \left[ - 1 , 1 \right] } & { \quad { \left\{ \begin{array} { l l } { \varpi < \beta } & { { \frac { \# } { \beta } } \mathbb { E } { \overline { { \Xi } } } } \\ { \varpi \in \left[ - \beta , \beta \right] } & { { \frac { \# \mathbb { E } } { \beta \mathbb { P } } } ^ { * } \mathbb { P } ^ { * } } \end{array} \right. } , \quad \quad { \dot { \mathbb { X } } } \mathbb { H } _ { \cdot } \beta \in \left[ - 1 , 1 \right] \mathbb { R } \oplus \not \equiv { \frac { \div } { \mathscr { X } } } \not \subseteq { \frac { \cdot } { \mathscr { X } } } } \\ & { \quad \cdot \left\{ \begin{array} { l l } { \varpi > \beta } & { { \frac { \# } { \beta \mathbb { P } } } ^ { * } \mathbb { H } } \end{array} \right. } \end{array} }
$$

通过上文的分析，我们可以得出，预测某个状态执行情况处于提前、持平还是推后，我们可以看有几条路径可以到达这个状态，这些路径就是并行的路径，通过引入DELPHI法计算出该状态的期望值，从而推出其执行情况的记过。也就是说任何一个软件项目中的并行过程都可以转化为串行过程。那么IT项目进度执行过程最终可以完全看作是一个串行的过程，则可以用马氏链来预测每个子阶段的状态。

# 4.4.3引入马氏链预测进度的一般步骤

引入马氏链预测软件项目进度，可以充分利用马氏链的特点。马氏链预测的对象是一个随机变化的动态系统，它适用于随机波动性较大的预测问题。预测下一个时期系统变化最可能出现的状态是马尔可夫链预测应用中最简单的类型。对于甘特图、PERT等直接应用于软件项目进度管理中的不足指出，马氏链能够有效的避免。所以，引入马氏链预测软件项目进度，有其优势以及可应

用性。

通常，我们按照以下步骤预测系统变化最可能出现的状态[25]：

(1）划分预测对象所出现的状态。从预测的目的出发，并考虑决策者的需要来划分现象的状态。

(2）计算初始概率。在实际问题中，分析历史资料所得的状态概率称为初始概率。设有 $\pmb { \mathsf { N } }$ 个状态 $E _ { 1 } , E _ { 2 } , \cdots E _ { N }$ ，共观察了 $\mathbf { M }$ 时期，其中状态 $E _ { i } \left( i = 1 , 2 , \cdots , N \right)$ 共出现了 $M _ { i }$ 次，于是得

$$
f _ { i } = \frac { \mathbf { M } _ { i } } { \mathbf { M } }
$$

公式（4-9)

这里， $f _ { i }$ 就是 $\pmb { { \cal E } } _ { i }$ 出现的概率，一般我们用它来近似地表示 $\pmb { { \cal E } } _ { i }$ 的概率，即

$$
f _ { i } \sim P _ { i } \left( i = 1 , 2 , \cdots , N \right)
$$

(3）计算状态转移概率。仍然以频率近似地表示概率来进行计算。首先计算状态 $E _ { i } \to E _ { j }$ （出 $\pmb { { \cal E } } _ { i }$ 转移到 $\pmb { { \cal E } } _ { j }$ ）的频率。从第(2)步知道 $\pmb { { \cal E } } _ { i }$ 出现了 $M _ { i }$ 次，接着从 $M _ { i }$ 个 $E _ { i }$ 出发，计算下一步转移状态 $E _ { i }$ 的个数 $\pmb { M } _ { y }$ ，于是得到：

$$
f \left( E _ { j } \left| E _ { i } \right. \right) = \frac { M _ { i j } } { M _ { i } } \ J \simeq P _ { i j }
$$

公式（4-11）

(4）根据转移概率进行预测。由上一步可得状态转移概率矩阵 $\pmb { P }$ 。如果目前预测对象处于状态 $\pmb { { \cal E } } _ { i }$ ，这时， $P _ { i j }$ 就描述了目前状态 $E _ { i }$ 在未来将转向状态$E _ { \boldsymbol { j } } \left( \boldsymbol { j } = 1 , 2 , \cdots , { N } \right)$ 的可能性。一般按最大可能性作为选择的原则，我们选择$\left( P _ { i 1 } , P _ { i 2 } , \cdots P _ { i N } \right)$ 中最大者作为我们的预测结果。

(5）用状态转移递推方程计算趋势。

$$
\left\{ \begin{array} { c } { { P ^ { ( 0 ) } = P } } \\ { { P ^ { ( n ) } = P ^ { ( n - 1 ) } P } } \end{array} \right.
$$

公式（4-12)

若 $P _ { i k } ^ { ( n ) } = \operatorname* { m a x } \left\{ P _ { i 1 } ^ { ( n ) } , P _ { i 2 } ^ { ( n ) } , \cdots , P _ { i N } ^ { ( n ) } \right\}$ ，则下一阶段出现状态 $E _ { \star }$ 的可能性最大。

# 第5章基于马氏链的软件项目进度预测应用

# 5.1项目简介

三环海通汽车有限公司是由上海通用汽车有限公司经过严格筛选后授权的湖北地区销售服务中心。是由三环集团销售有限公司投资，根据目前国际上先进的整车销售、售后服务、零部件供应、信息反馈的四位一体管理模式兴建的别克汽车专营店，专营店遍布武汉、黄石等地。多年来，公司内部运作仍然停留在传统流程的基础上，所以迫切希望通过开发办公自动化系统，实施企业全面信息化来加快企业发展的步伐，减少人力、财力、物力的浪费，提高信息资源的综合利用和共享。

具体来说，该办公自动化系统设计目标是开发以公文管理一体化为核心的自动化系统，要求出行政管理系统，销售管理系统，售后管理系统，客服中心管理系统，交车中心管理系统，绩效及薪酬管理系统，维护系统组成，建成后期望实现如下目标：

(1）提供面向所有工作人员的符合三环海通工作实际的具有对公文的收、发、办、管为一体的公文管理系统，使办公人员能动态实时跟踪文件的办理情况。

(2）为企业内人员提供快速方便的检索手段，用户只需在各办公室的电脑上就能检索所需文件。

(3）提供日常信息的多种发布方式，实现信息的编辑、制作、发布一体化。

(4）提供面向单位内人员的在线交流功能。

(5）提供主动服务功能，分析海通客户的维修数据（如：更换机油频率、做保养的频率等)，按照标准服务规范来预测客户下一次服务时间，并主动向客户发出提醒。

(6)提供工资核算功能,定期对员工工资的编辑、计算、工资的发放，帮助企业的行政部门处理日常工资的维护和发放工作，记录工资数据、打印工资单，提供灵活的工资项目计算。

(7）提供考核管理功能,针对企业中每个职工所承担的工作，应用各种科学的定性和定量的方法，对职工行为的实际效果及其对企业的贡献或价值进行考核和评价，对员工工作表现进行一种周期性检讨与评估。

(8）提供潜在客户挖掘功能，对潜在客户的信息进行收集、统计、分析来识别、挖掘、寻找具有购买力的客户，培育具有潜力的客户群，来增强三环海通公司对市场快速反应能力。

整个项目主要分为五大模块：行政管理系统、销售管理系统、交车管理系统、售后管理系统、装潢管理系统。

# 5.2项目的进度计划过程

# 5.2.1项目工作分解

以行政管理系统为例，图5-1为该部分的工作分解结构图。各责任分配如表5-1所示。

![](images/908a316e08a23117e8b11baf8bcedd96dd2b4ee96284ed6b538b65f1feae81a6.jpg)  
图5-1工作分解结构图

表5-1行政管理系统项日责任分配表  

<table><tr><td colspan="3">WBS</td><td rowspan="2">项 日 领导 小组</td><td rowspan="2">王庆 国</td><td rowspan="2">陆建 飞</td><td rowspan="2">小</td><td rowspan="2">石慧</td><td rowspan="2">张婧</td></tr><tr><td>主要任务</td><td>任务名称</td><td>子任务名称</td></tr><tr><td>系统分析与 设计11000</td><td>结构化功能 定义11100</td><td></td><td>C</td><td>F</td><td>f</td><td>C</td><td>C</td><td>C</td></tr><tr><td></td><td>数据处理分 析11200</td><td></td><td>C</td><td>F</td><td>f</td><td>C</td><td>C</td><td>C</td></tr><tr><td></td><td>界面定义 11300</td><td></td><td>C</td><td>J</td><td>f</td><td>F</td><td>C</td><td>C</td></tr><tr><td></td><td>用户和权限 定义11400</td><td></td><td>C</td><td>F</td><td>J</td><td>C</td><td>C</td><td>C</td></tr><tr><td></td><td>系统分析设 计文档11500</td><td></td><td>C</td><td>C</td><td>F</td><td>C</td><td>C</td><td>C</td></tr><tr><td>系统开发 12000</td><td>功能开发与 实现12100</td><td>人事管理 12110</td><td></td><td>c</td><td>f</td><td>F</td><td>C</td><td>C</td></tr><tr><td></td><td></td><td>固定资产管 理12120</td><td>J</td><td>C</td><td>f</td><td>F</td><td>C</td><td>C</td></tr><tr><td></td><td></td><td>会议管理 12130</td><td>J</td><td>C</td><td>C ：</td><td>C</td><td>F</td><td>C</td></tr><tr><td></td><td></td><td>票据管理 12140</td><td>J</td><td>C</td><td>C</td><td>C</td><td>F</td><td>C</td></tr><tr><td></td><td></td><td>车辆管理 12150</td><td>J</td><td>C</td><td>C</td><td>C</td><td>F</td><td>C</td></tr><tr><td></td><td></td><td>电子邮件 12160</td><td>J</td><td>C</td><td>C</td><td>C</td><td>C</td><td>F</td></tr><tr><td></td><td></td><td>信息发布管 理12170</td><td>J</td><td>C</td><td>C</td><td>C</td><td>C</td><td>F</td></tr><tr><td></td><td></td><td>档案管理 12180</td><td>J</td><td>C</td><td>C</td><td>C</td><td>C</td><td>F</td></tr><tr><td></td><td></td><td>绩效考核管 理12190</td><td>J</td><td>F</td><td>f</td><td>C</td><td>C</td><td>C</td></tr><tr><td></td><td></td><td>资核算管 121A0</td><td>J</td><td>F</td><td>f</td><td>C</td><td>C</td><td>C</td></tr><tr><td>系统测试 13000</td><td>用户运行环 境测试13100</td><td></td><td>C</td><td>C</td><td>C</td><td>F</td><td>C</td><td>C</td></tr><tr><td></td><td>业务功能测 试13200</td><td></td><td>C</td><td>C</td><td>C</td><td>F</td><td>C</td><td>C</td></tr><tr><td></td><td>回归测试及 文档13300</td><td></td><td>C</td><td>C</td><td>F</td><td>C</td><td>C</td><td>C</td></tr></table>

续表5-1

<table><tr><td>初始化14000</td><td>系统运行环 境软硬件安 装配置14100</td><td></td><td>C</td><td>C</td><td>C</td><td>C</td><td>C</td><td>C</td></tr><tr><td></td><td>系统安装 14200</td><td></td><td>C</td><td>C</td><td>C</td><td>C</td><td>F</td><td>C</td></tr><tr><td></td><td>系统初始化 数据整理转 换14300</td><td></td><td>C</td><td>C</td><td>C</td><td>C</td><td>C</td><td>F</td></tr><tr><td></td><td>用户权限设 置，初始化数 据设置14400</td><td></td><td>C</td><td>C</td><td>F</td><td>C</td><td>C</td><td>C</td></tr><tr><td>系统正式实 施15000</td><td>系统试运行 15100</td><td></td><td>C</td><td>C</td><td>F</td><td>C</td><td>C</td><td>C</td></tr><tr><td></td><td>用户培训 15200</td><td></td><td>P</td><td>F</td><td>C</td><td>C</td><td>C</td><td>C</td></tr><tr><td></td><td>系统验收正 式上线15300</td><td></td><td>F</td><td>C</td><td>C</td><td>C</td><td>C</td><td>C</td></tr></table>

注：F负责C参与J监督P批准f协同负责

在项目分解完成后，为了使项目团队成员更准确的理解项目所包括的各项工具的具体内容和要求，项目组对WBS分解所得的所有工作进行了描述。工作描述的依据是项目目标、项目描述和项目工作分解结构。工作描述如表5-2所示。

表5-2工作描述表  

<table><tr><td>工作名称</td><td>用户需求确认</td><td>备注</td></tr><tr><td>工作交付物</td><td>用户需求确认表</td><td></td></tr><tr><td>验收标准</td><td>项日经理签字，确定项日方案</td><td></td></tr><tr><td>技术条件</td><td>建设公司人力资源管理现状需求分析</td><td></td></tr><tr><td>工作描述</td><td>根据项日要求和设计规范，进行用户需求报批</td><td></td></tr><tr><td>假设条件</td><td>建设行政管理系统</td><td></td></tr><tr><td>信息源</td><td>针对行政管理系统所收集的信息</td><td></td></tr><tr><td>约束条件</td><td>初步分析确定的需求</td><td></td></tr><tr><td>其他需要描述的问题</td><td>风险：初步分析用户需求不准确 防范计划：分析工作要详细、准确，以保证用户需求确</td><td></td></tr><tr><td>签名及日期</td><td>认的正确性 签名：xxx 日期：2004年8月1日</td><td></td></tr></table>

# 5.2.3进度计划制定

项目组根据项目分解结构和各种限制约束条件等，编制出项目工作先后关系表和出网络图和甘特图表示的项目进度计划。

下面以行政管理系统为例，如图5-2，图5-3所示，分别表示行政管理系统编程计划甘特图和行政总体甘特图。

<table><tr><td></td><td>任务名称</td><td>开始时间</td><td>完成时间</td><td>持续时间</td><td>2006</td></tr><tr><td></td><td>1人事管理系统</td><td>2006-07-25</td><td>2008-09-15</td><td>7周4天</td><td>八月九月十月</td></tr><tr><td></td><td>2固定资产管理系统</td><td></td><td>2006-07-25|2006-09-15</td><td>7周4天</td><td></td></tr><tr><td></td><td>3会议管理系统</td><td>2006-07-252006-09-15</td><td></td><td>7周4天</td><td></td></tr><tr><td></td><td>4票据管理系统</td><td>2006-09-162006-09-30</td><td></td><td>2周1天</td><td></td></tr><tr><td></td><td>5车辆管理系统</td><td>2006-09-162006-09-30</td><td></td><td>2周1天</td><td></td></tr><tr><td></td><td>6绩效考核管理系统</td><td>2006-10-072008-11-15</td><td></td><td>5周5天</td><td></td></tr><tr><td></td><td>7工资核算管理系统</td><td>2006-10-072008-11-15</td><td></td><td>5周5天</td><td></td></tr><tr><td></td><td>8电子邮件系统</td><td>2006-10-072006-11-15</td><td></td><td>5周5天</td><td></td></tr><tr><td></td><td>9信息发布管理系统</td><td>2006-11-182008-11-30</td><td></td><td>2周1天</td><td></td></tr><tr><td></td><td>10档案管理系统</td><td>2008-11-162006-11-30</td><td></td><td>2周1天</td><td></td></tr><tr><td></td><td>系统集成</td><td>2006-12-01</td><td>2006-12-15</td><td>2周1天</td><td></td></tr></table>

<table><tr><td rowspan="2">1D</td><td rowspan="2">任务名称</td><td rowspan="2">开始时间</td><td rowspan="2">完成时间</td><td rowspan="2">持续时间</td><td colspan="6">2000</td><td colspan="6"></td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td>2007</td></tr><tr><td>1</td><td>调研</td><td>2008-03-102008-04-30</td><td></td><td>73天</td><td colspan="7"></td><td></td><td></td><td></td><td></td></tr><tr><td>2</td><td>系统分析与设计</td><td>2006-05-01</td><td>2000-07-15</td><td>10.8天</td><td colspan="7"></td><td></td><td></td><td></td><td></td></tr><tr><td>3</td><td>系统分析设计报告</td><td></td><td>2006-07-152006-07-15</td><td>调</td><td colspan="7"></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td>4界面设计</td><td>2008-07-152006-07-24</td><td></td><td>1.3天</td><td colspan="7">■</td><td></td><td></td><td></td><td></td></tr><tr><td></td><td>5系统界面</td><td>2006-07-242000-07-24</td><td></td><td></td><td colspan="7">+</td><td></td><td></td><td></td><td></td></tr><tr><td>8</td><td>编写代码及单元测试</td><td></td><td>2000-07-252008-12-15</td><td>20周4天</td><td colspan="7"></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td>7系统软件</td><td></td><td>2006-12-152006-12-15</td><td></td><td colspan="7"></td><td></td><td></td><td></td><td></td></tr><tr><td>8</td><td>系统测试和系统修改</td><td></td><td>2006-12-102006-12-30</td><td>2.天</td><td colspan="7"></td><td>■</td><td></td><td></td><td></td></tr><tr><td></td><td>系统测试报告</td><td>2006-12-302006-12-30</td><td></td><td></td><td colspan="7"></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td>10系统安装及更正</td><td></td><td>2007-01-012007-01-30</td><td>4周2天</td><td colspan="7"></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td>11交付系统</td><td>2007-02-012007-04-30</td><td></td><td>12周5天</td><td colspan="7"></td><td></td><td></td><td></td><td></td></tr></table>

根据WBS图，我们可以得到项目工作的先后关系，如表5-3所示。

表5-3项目「作先后关系表  

<table><tr><td>任务编码</td><td>任务名称</td><td>紧前序</td><td>备注</td></tr><tr><td>11100</td><td>结构化功能定义</td><td></td><td></td></tr><tr><td>11200</td><td>数据处理分析</td><td>11100</td><td></td></tr><tr><td>11300</td><td>界面定义</td><td>11200</td><td></td></tr><tr><td>11400</td><td>用户和权限定义</td><td>11300</td><td></td></tr><tr><td>11500</td><td>系统分析设计文档</td><td>11400</td><td></td></tr><tr><td>12110</td><td>人事管理</td><td>11500</td><td></td></tr><tr><td>12111</td><td>人事档案管理</td><td>12110</td><td></td></tr><tr><td>12112</td><td>人员调动管理</td><td>12110</td><td></td></tr><tr><td>12113</td><td>考勤管理</td><td>12110</td><td></td></tr><tr><td>12114</td><td>人事权限</td><td>12110</td><td></td></tr><tr><td>12120</td><td>固定资产管理</td><td>11500</td><td></td></tr><tr><td>12121</td><td>办公设备管理</td><td>12120</td><td></td></tr><tr><td>12122</td><td>低值易耗品管理</td><td>12120</td><td></td></tr><tr><td>12130</td><td>会议管理</td><td>12111,12112,12113,12114,12121,12</td><td></td></tr><tr><td>12131</td><td>会议室管理</td><td>12130</td><td></td></tr><tr><td>12132</td><td>会议计划管理</td><td>12130</td><td></td></tr><tr><td>12140</td><td>票据管理</td><td>12111,12112,12113,12114,12121,12122</td><td></td></tr><tr><td>12141</td><td>油票管理</td><td>12140</td><td></td></tr><tr><td>12142</td><td>餐票管理</td><td>12140</td><td></td></tr><tr><td>12150</td><td>车辆管理</td><td>12131,12132,12141,12142</td><td></td></tr><tr><td>12151</td><td>车辆基本信息管理</td><td>12150</td><td></td></tr><tr><td>12152</td><td>车辆调度管理</td><td>12150</td><td></td></tr><tr><td>12153</td><td>车辆维护管理</td><td>12150</td><td></td></tr><tr><td>12160</td><td>电子邮件</td><td>12131,12132,12141,12142</td><td></td></tr><tr><td>12161</td><td>接受邮件</td><td>12160</td><td></td></tr><tr><td>12162</td><td>发送邮件</td><td>12160</td><td></td></tr><tr><td>12163</td><td>查询邮件</td><td>12160</td><td></td></tr><tr><td>12170</td><td>信息发布管理</td><td>12151,12152,12153,12161,12162,12163</td><td></td></tr><tr><td>12171</td><td>电子公告管理</td><td>12170</td><td></td></tr><tr><td>12172</td><td>电子论坛管理</td><td>12170</td><td></td></tr><tr><td>12173</td><td>组织文化管理</td><td>12170</td><td></td></tr><tr><td>12180</td><td>档案管理</td><td>12151,12152,12153,12161,12162,12163</td><td></td></tr><tr><td>12181</td><td>纸质档案管理</td><td>12180</td><td></td></tr><tr><td>12182</td><td>电子档案管理</td><td>12180</td><td></td></tr><tr><td>12183</td><td>借阅管理</td><td>12180</td><td></td></tr><tr><td>12184</td><td>杏询管理</td><td>12180</td><td></td></tr><tr><td>12190</td><td>绩效考核管理</td><td>12171,12172,217,181,182,</td><td></td></tr><tr><td>12191</td><td>部门考核</td><td>12190</td><td></td></tr><tr><td>12192</td><td>员I考核</td><td>12190</td><td></td></tr></table>

续表5-3   

<table><tr><td>12193</td><td>考核成绩公布</td><td>12190</td><td></td></tr><tr><td>121A0</td><td>资核算管理</td><td>12171,12172,12173,12181,12182,12183,12184</td><td></td></tr><tr><td>121A1</td><td>资数据处理</td><td>121A0</td><td></td></tr><tr><td>121A2</td><td>统计报表输出</td><td>121A0</td><td></td></tr><tr><td>12200</td><td>界面设计</td><td>12191,12192,12193,121A1,121A2</td><td></td></tr><tr><td>12300</td><td>系统集成</td><td>12200</td><td></td></tr><tr><td>13100</td><td>用户运行环境测试</td><td>12300</td><td></td></tr><tr><td>13200</td><td>业务功能测试</td><td>13100</td><td></td></tr><tr><td>13300</td><td>回归测试及文档</td><td>13200</td><td></td></tr><tr><td>14100</td><td>系统运行环境，软硬 件安装配置</td><td>13300</td><td></td></tr><tr><td>14200</td><td>系统安装</td><td>14100</td><td></td></tr><tr><td>14300</td><td>系统初始化，数据整 理转换</td><td>14200</td><td></td></tr><tr><td>14400</td><td>用户权限设置，初始 化数据设置</td><td>14300</td><td></td></tr><tr><td>15100</td><td>系统试运行</td><td>14400</td><td></td></tr><tr><td>15200</td><td>用户培训</td><td>15100</td><td></td></tr><tr><td>15300</td><td>系统验收正式上线</td><td>15200</td><td></td></tr></table>

# 根据表5-3，我们可以画出相应的网络计划图，如图5.5所示。

![](images/71285a26765621a5be1b1545c065102177e95955b4188c89d3ea9e96dc35a8fe.jpg)  
图5-5网络计划图

在这个行政管理系统开发过程中，严格划分阶段，并将每个阶段的执行情况，提前、持平或推后进行详细的记载。根据图5-5，根据前面讲到的方法转化为串行阶段。表5-4记录的是20个阶段的执行情况，用马尔可夫链进行预测[39]。

(1）划分状态：每项过程并不是一定在期望时间准时完成的，将各阶段的完成情况分为提前，持平和推后三种状态进行分析和预测。

表5-4IT项目管理进度状态统计表  

<table><tr><td>序号</td><td>1</td><td>2</td><td>3</td><td>4</td><td>5</td><td>6</td><td>7</td><td>8</td><td>9</td><td>10</td></tr><tr><td>状态</td><td>提前</td><td>推后</td><td>持平</td><td>推后</td><td>持平</td><td>持平</td><td>推后</td><td>持平</td><td>推后</td><td>持平</td></tr><tr><td>序号</td><td>11</td><td>12</td><td>13</td><td>14</td><td>15</td><td>16</td><td>17</td><td>18</td><td>19</td><td>20</td></tr><tr><td>状态</td><td>推后</td><td>持平</td><td>推后</td><td>持平</td><td>推后</td><td>提前</td><td>推后</td><td>推后</td><td>持平</td><td>推后</td></tr></table>

(2)计算初始概率：以表1中每个阶段作为离散的时间单位，各个阶段的完成情况分为提前，持平和推后三种状态，并且取 $M _ { \parallel } =$ 提前， ${ { M } _ { 2 } } =$ 持平， ${ \cal M } _ { 3 } =$ 推后。则状念空间为 $E \left( { M _ { 1 } , M _ { 2 } , M _ { 3 } } \right)$

状态概率是各种状态出现的可能性的大小，用状态向量 ${ \pmb G } ( { \pmb N } )$ 表示， $P _ { i }$ 表示$M _ { i }$ 的概率， $\left( i = 1 , 2 , \cdots , N \right)$

表 5-4中共20个数据，其中， $ { M _ { 1 } } = 2 ,  { M _ { 2 } } = 8 ,  { M _ { 3 } } = 1 0$ ，所以各个状态的概率分别为P= $P _ { 1 } = \frac { 2 } { 2 0 } = 0 . 1 , P _ { 2 } = \frac { 8 } { 2 0 } = 0 . 4 , P _ { 3 } = \frac { 1 0 } { 2 0 } = 0 . 5$ ，状态向量 $G \big ( 0 \big ) = \big ( 0 . 1 , 0 . 4 , 0 . 5 \big )$ （204称为初始状态向量。

(3)计算状态转移矩阵：在计算状态转移矩阵时，最后一个数据不参加计算，因为它究竟转至哪个状态尚不清楚。由表 $\mathsf { 5 } \mathrm { - } \mathsf { 4 }$ ，可以得到：

$$
\begin{array} { r c l } { { } } & { { M _ { _ { 1 1 } } = 0 } } & { { M _ { _ { 1 2 } } = 0 } } & { { M _ { _ { 1 3 } } = 2 } } \\ { { } } & { { } } & { { M _ { _ { 2 1 } } = 0 } } & { { M _ { _ { 2 2 } } = 1 } } & { { M _ { _ { 2 3 } } = 7 } } \\ { { } } & { { } } & { { M _ { _ { 3 1 } } = 1 } } & { { M _ { _ { 3 2 } } = 7 } } & { { M _ { _ { 3 3 } } = 1 } } \end{array}
$$

从而有：

$$
\begin{array} { c c c } { { P _ { 1 1 } = 0 } } & { { P _ { 1 2 } = 0 } } & { { P _ { 1 3 } = 1 } } \\ { { } } & { { } } & { { } } \\ { { P _ { 2 1 } = 0 } } & { { P _ { 2 2 } = \displaystyle \frac { 1 } { 8 } } } & { { P _ { 2 3 } = \displaystyle \frac { 7 } { 8 } } } \\ { { } } & { { } } & { { } } \\ { { P _ { 3 1 } = \displaystyle \frac { 1 } { 9 } } } & { { P _ { 3 2 } = \displaystyle \frac { 7 } { 9 } } } & { { P _ { 3 3 } = \displaystyle \frac { 1 } { 9 } } } \end{array}
$$

于是得到状态转移概率矩阵为：

(4)预测第21阶段任务完成情况的状态：

经过一次转移的概率矩阵为：

$$
P ^ { ( 1 ) } = P ^ { ( 0 ) } P = { \left[ \begin{array} { l l l } { 0 } & { 0 } & { 1 } \\ { 0 } & { { \frac { 1 } { 8 } } } & { { \frac { 7 } { 8 } } } \\ { { \frac { 1 } { 9 } } } & { { \frac { 7 } { 9 } } } & { { \frac { 1 } { 9 } } } \end{array} \right] } { \left[ \begin{array} { l l l } { 0 } & { 0 } & { 1 } \\ { 0 } & { { \frac { 1 } { 8 } } } & { { \frac { 7 } { 8 } } } \\ { 0 } & { { \frac { 7 } { 9 } } } & { { \frac { 1 } { 9 } } } \end{array} \right] } = { \left[ \begin{array} { l l l } { { \frac { 1 } { 9 } } } & { { \frac { 7 } { 9 } } } & { { \frac { 1 } { 9 } } } \\ { { \frac { 7 } { 7 2 } } } & { { \frac { 4 0 1 } { 5 7 6 } } } & { { \frac { 1 1 9 } { 5 7 6 } } } \\ { { \frac { 1 } { 8 1 } } } & { { \frac { 1 1 9 } { 6 4 8 } } } & { { \frac { 5 2 1 } { 6 4 8 } } } \end{array} \right] } = { \left[ \begin{array} { l l l } { 0 . 1 1 1 } & { 0 . 7 7 8 } & { 0 . 1 1 1 } \\ { 0 . 0 9 7 } & { 0 . 6 9 6 } & { 0 . 2 0 7 } \\ { 0 . 0 1 2 } & { 0 . 1 8 4 } & { 0 . 8 0 4 } \end{array} \right] }
$$

由表1知，20阶段的状态为3。 $P _ { 3 1 } = 0 . 0 1 2 , P _ { 3 2 } = 0 . 1 8 4 , P _ { 3 3 } = 0 . 8 0 4$

因为： $P _ { 3 3 } > P _ { 3 2 } > P _ { 3 1 }$ ，即下一步转移至 $M _ { 3 }$ 状态的可能性最大，所以，可以预测第21阶段状态属于推后。

（5）用状态转移递推方程计算趋势。

g: 82 83 设 0 1-8 7-9 1 7-81-9 且 g，=1，则有 $\sum _ { i = 1 } ^ { 3 } P _ { i j } g _ { i } = g _ { j } \left( j = 1 , 2 , 3 \right)$ 01- $\left\{ P _ { 1 1 } g _ { 1 } + P _ { 2 1 } g _ { 2 } + P _ { 3 1 } g _ { 3 } = g _ { 1 } \right.$ $\begin{array}{c} \left\{ { { g _ { 1 } } = 1 / 1 8 } \atop { { \left\{ { \begin{array} { l l } { { \displaystyle { g _ { 1 } } = 4 / 9 } } \\ { { \displaystyle { g _ { 3 } } = 1 / 2 } } \end{array} } \right. } }   \end{array}  \right.$ 即有 $\left\{ P _ { 1 2 } g _ { 1 } + P _ { 2 2 } g _ { 2 } + P _ { 3 2 } g _ { 3 } \right. \approx g _ { 2 }$ ，根据上面的初始值，可得： （202 $\lfloor P _ { 1 3 } g _ { 1 } + P _ { 2 3 } g _ { 2 } + P _ { 3 3 } g _ { 3 } = g _ { 3 }$ 即 $g _ { 3 } = \operatorname* { m a x } \left\{ g _ { 1 } , g _ { 2 } , g _ { 3 } \right\} = 1 / 2$

故该软件项目的进度趋势是由推后转移到推后，与后期结果是吻合的。整个开发过程中，这个方法给项目组控制项目进度提供了很好的参考。

# 第6章全文总结与展望

# 6.1全文总结

美国学者戴维克·兰德[4指出：在应付全球化的市场变动中，战略管理和项目管理将起到关键性的作用。战略管理立足于长远和宏观，考虑的是企业的核心竞争力。而项目管理则着眼于实际，是一种科学的管理方式。

科学的管理方式强调科学的方法。本文首先介绍了论文的选题背景及依据、研究现状及意义、研究内容及框架。然后对项目管理及软件项目管理基本理论进行了阐述和分析，结合项目，仔细研究了软件项目管理过程中的计划和控制方法。对软件开发项目进度计划中的工作量估算、工作结构分解、制定计划的三种常用技术（Gantt、PERT、CPM）和软件项目进度控制中的常用技术（里程碑进度、人为设定活动进度、工作单元进展和挣值法）进行分析和研究。接着，介绍了马尔可夫分析法的基本原理和软件开发经历阶段及开发方法。对软件项目开发中的并行工程进行分析和研究。最后引入马氏链和DELPHI法来预测软件项目进度。

最后，以某汽车4S店办公自动化系统为例，证实基于马氏链的软件项目进度预测的可行性。

通过本文理论和实践两方面的分析论述不难得出如下结论：基于马氏链的进度计划和控制框架对于实施软件项目管理有一定的参考价值，按照该方法对软件项目过程实施管理，能够制定出良好的计划，并且能够使项目按照预定的计划顺利执行，对软件项目成功有极大的促进作用。

本文是依据软件工程和项目管理的特点，对当前软件项目管理的计划控制方法进行的一次尝试性研究，需要在实践中不断完善。由于笔者水平有限，文中有很多不足，恳请各位师长批评指正。

# 6.2研究展望

马尔可夫链预测是建立在对历史数据的分析统计之上的。历史数据越多越精确，预测也就越可靠。进度完成状态的马尔可夫链预测是一种概率预报，其

准确性和可靠性要通过大量的预测检验才能显示出来。

本文中马尔可夫链是有限期的预测，也可以将之扩展为无限期的。这一点在生命周期法中有很强的实用价值。

# 参考文献

[1]Roger S.Pressman 著黄柏素，梅宏译.软件工程一实践者的研究方法.北京:机械工业出版社.2001年，417-429

[2]Dolado J.A Validation of the Component-Based Method for Software Size

Estimation[J].IEEE Transactions on software engineering,20o0,26(1O):1006-1021[3]张一弛.人力资源管理教程.北京:北京大学出版社.1999年，5162[4]葛世伦，代逸生.企业管理信息系统开发的理论与方法.北京:清华大学出版社.

1998年，263-282 {5]Dr.Soheil Khajenoori Mr.Frank Gutcher. Software Requirement Enginering Process.

Embry-Riddle Aeronautical University.1999, 35-41[6]白思俊.现代项日管理(上、中、下).北京:机械.I业出版社.2002年，23-37[7]Kits.Shepperd著.王金玉等译.IT项目管理.北京:机械I业出版社.2002，534-546[8]Shepperd M, Schofield C. Estimating Software Project Effort Using Analogies [J.IEEE

Transactions on software engineering, 1997, 23(12):736-743 [9]Cynthia Krohmal,Robert McKeeman. IT Project Management Oversight, VA CIO Conference

Farming TT Success. October 29, 2001, 59-61 [10]Rogeer S. Pressman software engineering A Practitioner's Approach》 McGraw-Hill 1999 [11]Daniel J. Paulish, Architecture-Centric Software Project Management: A Practical Guide,1st

Edition,2003.3,Peatson Education, Inc.,18-20[12]Koontz. H., Wihrich. H. Management. (10th Edition).1998.7,179-181[13]陈和兰,龚少文.大型软件项目中的组织环境.项日管理技术,2004年,2期:15-18[14]覃征，杨利英，高勇民，贺升平，韩毅.软件项日管理.北京:清华大学出版社.2004年，

91-94 [15]GGordon Schulmeyer JamesI.McManus等著.李怀璋，武占春，王青等译.软件质量保证

(原书第三版).北京：机械[:业出版社.2003,3-6[16]邱苑华，沈建民，杨爱华等荞.现代项日管理导轮.北京:机械工业出版社,2002年,261-267[17]郑人杰，殷人昆，陶永雷。实用软件[程.北京:清华人学出版社.2001年，205-210[18]黄智辉.浅谈.L作分解结构(WBS）在项日管理控制中的应用.小型微型计算机

系统，2005年，4期:8-11[19]Shard Lawrence Pfleeger:软件工程—理论与实践，影印第二版.北京:高等教育出版

社.2001,276-284   
[20]Hughes B, Cottrell M.IT project management [M.   
[21]韩万江.姜立新.软件项目管理案例教程[M].北京:机械T业出版社.2005-02.   
[22]Chatzoglou P D,Macaulay L A. A review of existing models for project planning and

estimation and the need for a new approach [J]. International Journal of Project Management,1996,14:173-183

[23]郑人杰.王纬.王方德.等.基于软件能力成熟度模型(CMM)的软件过程改进[M].北京:清华

大学出版社.2003-03.

[24]刘次华.随机过程[M].2版.武汉：华中科技人学出版社，2001:56-84.

25]李坤.用马尔可大链预测上证综指走势.经济理论研究[26]祝士明,管理信息系统,机械I.业出版社,2005.1[27]AOYAMA .Concurent Development processmodel [J]EEE Sofware,1993,7: 46-55.

[28]AOYAMA M.Concurrent Development of software systems: A new development paradign

[J].ACM SIGSPFT Software Engineering Notes,1987,12( 2):52-60.  
[29]李彤，王黎霞.支持第四代语言的并行进化式软件开发模型CESD[J].计算机科学,1996,23(5): 79-81  
[30]汪成为，郑小军，彭木昌.面向对象分析、设计和应用[M.北京:国防I业出版社，1992.1-124.  
[31]孔兵、李彤、王伟、柳青，并行开发的控制模型，云南大学学报(白然科学版)2000,

22(6):402- 406[32]工珉,汤幼宁,吴广茂,张学宏.软件开发的并行工程方法研究.2006年3月中国制造业

信息化第32卷第3期

[33]许维胜，吴启迪.并行工程过程研究「J].同济大学学报,2001,29(2):185-190.  
[34]陈国权.并行T程管理力一法与应用[M].北京:清华大学出版社，1998.

5JKanmra John M Anumba Chinmy J,Evbuomwan Nosa F O.Assessing the suitability of

Cuent briefing practices inconstruction withaconcurrent engineering framework[J].

International Journal of Project Management,2001， 19:337-351. [36]李鹏飞，李涛.可重用概念构件的实现[J].计算机工程与应用，1999,35(2):48-50 [37]Kruskal C P and Weiss A. Allcating independent subtasks on parallel processor [J].IEE

Transaction Software Engineering 1985 11:1001-1016 [38]吴樟给，最优箭线网络图的绘制规则[J]，系统.I.程理论与实践,1991,(3):54-57 [39]Rogers LCG and Williams D. Diffusions,Markov Processes, and Martingales.Vol.1.、

Foundation.Wiley， 1994. [40]Walker Royce. Sofware Project Management: A Unified Framework,1st Edition. Prentice Education,I.2002,NC,208-211

# 致谢

本文的完成，首先要感谢我的导师王虎教授。论文从选题、写作、修改到最终定稿，倾注了王老师大量的心血。王老师渊博的知识、谦和的学者风范永远是我学习的榜样。

感谢管理学院给我们创造的良好学习条件，感谢教导我们的各位师长，由于我们基础较差，给老师们增添了不少的麻烦和不便。从你们身上，我深深体会到什么是学问，怎样做学问，怎样做人。

最后还要感谢我的父母和男友，正是他们在背后默默的支持，使得我一直在人生的路上执着前进。

# 攻读硕士学位期间发表论文及参加科研的情况

# 1、在读期间发表的学术论文

Hu Wang, Hui Shi,The application and research of Markov Chain in schedule prediction of IT project. Proceedings of the $4 ^ { \pi ^ { \alpha } }$ International Conference on Innovation & Management, 2007.

2、在读期间参加的科研项目

（1）2005年9月－2005年12月，湖北武汉人才市场管理信息系统（2）2006年3月－2007年10月，湖北三环集团海通公司办公自动化系统
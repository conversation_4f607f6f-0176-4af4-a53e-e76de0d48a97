# 浙江大學

专业硕士学位论文

中文论文题目：基于Scrum敏捷开发方法的A公司PVoC业务数字平台项目进度管理研究英文论文题目：Research on Progress Management of

PVoC Business Digital Platform Project of Company A

# Based on Scrum Agile Development Method

申请人姓名： 王聪聪  
指导教师： 卢吾  
合作导师：  
专业学位类别：工程管理（专业学位）  
专业学位领域： 工程管理  
所在学院： 工程师学院

# 摘要

近十年来，中国测试、检验和认证（TIC）行业以年增长两位数速率快速增长。但随着网络化、数字化、智慧化对整个贸易形态的改变，TIC行业还没有发生根本性变革。我国“十四五”以来，陆续发布的《数据安全法》、《个人信息保护法》等对进出口商品检验机构许可制度的改革，对 TIC行业有着深刻的影响。要想在瞬息万变的数字化浪潮中占领先机，就要对数字化项目做好项目进度管理。将项目进度管理知识作为理论基础，吸收和运用国内外的相关数字化项目进度管理经验，运用Scrum敏捷开发方法代替传统开发方式，充分应用其快速迭代特性解决数字化业务频繁变化需求导致的进度延期问题，为整个行业的数字化建设的加速追赶提供一定帮助。

本文以A公司PVoC业务数字平台项目作为研究对象，在软件项目管理、软件项目进度管理以及Scrum 敏捷开发方法相关文献研究基础上，分析了Scrum敏捷开发方法在软件项目进度管理中的应用优势。通过案例分析，采用德尔菲法对 A 公司PVoC业务数字平台项目一期的进度延期问题进行客观分析，得出其存在开发流程固化、需求变更频繁、需求优先级不明以及进度监控工具缺失等问题。结合A公司组织架构和项目一期存在的进度管理问题，采用基于Scrum敏捷开发方法改进的开发流程替代原有的瀑布开发流程，并辅以加权决策矩阵对需求的优先级进行排列，敏捷估算扑克对用户故事的工时进行预估，以及关键链、接驳缓冲区和燃尽图对进度进行监控，从而保障项目进度的不滞后。

最终，从项目燃尽图、代码缺陷率以及问卷调查中可以得出，采用的以Scrum敏捷开发方法为核心的进度管理方法能有效地控制项目进度，并提高团队的积极性。Scrum敏捷开发方法的便于上手和可塑性，也注定其在项目管理中的应用会很广泛。

关键词：进度管理；敏捷开发；Scrum；软件项目管理

# Abstract

In the past decade, the testing, inspection and certification (TIC) industry in Chia has grown rapidly at a double-digit annual growth rate. However, with the change of networking, digitalization and intelligence to the whole trade pattern, TIC industry has not undergone fundamental change. Since the "the 14th Five-Year Plan", the Data Security Law, Personal Information Protection Law and other reforms on the licensing system of import and export commodity inspection agencies have had a profound impact on the TIC industry. If you want to take the lead in the rapidly changing digital tide, you should do a good job in project schedule management of digital projects. Take the project schedule management knowledge as the theoretical basis, absorb and apply the relevant digital project schedule management experience at home and abroad, use the Scrum agile development method to replace the traditional development method, and fully apply its fast iteration characteristics to solve the schedule delay problem caused by the frequent changes of digital business requirements, providing some help for the acceleration of digital construction in the entire industry.

This paper takes the PVoC business digital platform project of Company A as the research object, and analyzes the application advantages of Scrum agile development method in software project schedule management on the basis of relevant literature research on software project management, software project schedule management and Scrum agile development method. Through case analysis, the Delphi method is used to objectively analyze the schedule delay of the first phase of the PVoC business digital platform project of Company A, and it is concluded that there are problems such as solidified development process, frequent demand changes, unclear demand priority, and lack of progress monitoring tools. Combined with the organizational structure of Company A and the progress management problems in Phase I of the project, the original waterfall development process was replaced by the improved development process based on the Scrum agile development method,and the priority of requirements was ranked by the weighted decision matrix. The agile estimation poker estimated the working hours of user accidents, and the critical chain, connection buffer zone and burn down chart monitored the progress, so as to ensure that the project progress was not delayed.

Finally, from the burn down chart of the project, code defect rate and questionnaire survey, it can be concluded that the progress management method with Scrum agile development method as the core can effectively control the project progress and improve the enthusiasm of the team. Scrum's agile development method is easy to use and flexible, and it is destined to be widely used in project management.

Keywords: progress management, agile development, scrum, software project management

# 目录

第1章绪论..  
1.1 研究背景和意义.  
1.2 研究内容和方法. .2  
1.2.1 研究内容.. .2  
1.2.2 研究方法.. .2  
1.3 论文组织结构. ..3  
第2章 文献综述与基础理论 ..5  
2.1 软件项目管理的相关理论 ..5  
2.2 软件项目进度管理的发展和理论 ..6  
2.3 传统瀑布式开发模型概述 .9  
2.4 Scrum敏捷开发方法. .10  
2.4.1 Scrum 敏捷开发的发展 ..10  
2.4.2 Scrum 敏捷开发的内容 ..11  
2.4.3Scrum敏捷开发的研究现状. ..13  
2.4.4Scrum敏捷开发对比瀑布开发模型的优势 .14  
2.5 基于 Scrum 敏捷开发的软件项目进度管理优化实践 ..14  
2.6 本章小结 .15  
第3章A 公司.PVoC业务数字平台项目管理现状及问题分析....16  
3.1A公司概况 ..16  
3.1.1A 公司简介.. ..16  
3.1.2A公司组织架构！ ..16  
3.2 PVoC业务数字平台项目管理现状， ..17  
3.2.1PVoC业务数字平台项目简介 ..17  
3.2.2 PVoC业务数字平台项目特点 ..18  
3.2.3PVoC业务数字平台项目一期开发流程概述， ..18  
3.3 PVoC业务数字平台项目一期进度延期问题分析. ..20  
3.4 本章小结.. ..24  
第 4章 基于 Scrum 的 PVoC 业务数字平台项目进度管理方案改进  
...25  
4.1 Scrum 在PVoC 业务数字平台项目进度管理的适用性分析...25  
4.1.1 Scrum 在 PVoC 业务数字平台项目开发流程中的适用性..25  
4.1.2 Scrum. 在 PVoC 业.务数字平台项目管理中的适用性....26  
4.2 基于 Scrum 的开发流程构建.. ..27  
4.3 基于 Scrum 的组织结构改进. ..31  
4.4 基于 Scrum 的需求分析改进 .33  
4.5 基于 Scrum 的进度控制改进 .34  
4.6 本章小结.... .37  
第5章PVoC 业务数字平台项目二期进度管理实践 .38  
5.1 项目简介... .38  
5.2项目进度计划.. .38  
5.2.1 需求分析.. .38  
5.2.2 需求优先级.. ..41  
5.2.3 迭代计划. ..41  
5.3 项目进度计划实施 .42  
5.3.1团队管理.. .42  
5.3.2迭代进度跟踪. .43  
5.3.3 迭代评审回顾. ..44  
5.4 项目进度控制.. ..45  
5.5 项目实施效果评价. .49  
5.5.1 技术能力增长. .49  
5.5.2 需求响应变快. .50  
5.5.3客户满意度提升. .50  
5.5.4项目进度提前. .51  
5.6 本章小结. .52  
第6章结论与展望 .53  
6.1 结论... .53  
6.2展望. .54  
参考文献... ..56  
附录... .61

# 第1章绪论

# 1.1研究背景和意义

# 1.1.1 研究背景

近十年来，中国测试、检验和认证（TIC）行业以年增长两位数速率快速增长。但随着网络化、数字化、智慧化对整个贸易形态的改变，TIC行业还没有发生根本性变革。尽管逐渐意识到数字化的重要性，许多相关企业也开始采取了多层面的数字化举措来满足市场需求，但基本很多停留在表面，而整个TIC行业的数字化程度更是大大落后于其他行业。我国“十四五”以来，陆续发布的《数据安全法》、《个人信息保护法》等对进出口商品检验机构许可制度的改革，对 TIC行业有着深刻的影响[1]。

A公司是一家第三方检验检测C集团公司下属的宁波子公司。C集团公司是一家以检验、鉴定、认证、测试等为主营业务的综合质量咨询与服务组织，是一家由国务院国资委进行管理的央企。A公司作为一家子公司，虽然主业是检验检测方面内容，但从企业发展战略意义上，A公司早在2008年就组建了自己的信息技术部门。由于之前承接的基本都是公司内部软件开发项目，针对项目的延期问题也属于内部问题，有一定的容忍度。所以之前项目进度管理问题也没有明显暴露。但在一项C集团指派的PVoC业务数字平台项目一期建设中，出现了严重的进度延期，A公司名誉受到影响。其中PVoC业务是出口前产品与标准的符合性验证（Pre-Export Verification of Conformity to Standards，简称 PVoC）计划[2]。PVoC业务是由肯尼亚标准局提出的，其主要目的是通过对出口国的相应商品进行检查评定来保障进口产品的质量和安全，同时对肯尼亚人民的健康和环境保护进行法规上的强制标准实施。PVoC业务数字平台项目正是对PVoC业务数字化的系统支持。

A公司决定以PVoC业务数字平台项目二期作为实践进行研究，旨在解决公司内部存在的项目进度管理问题，已达到公司项目进度管理能力的提升。

# 1.1.2研究意义

在各种项目管理中，其中最重要的三要素是质量、进度、成本[3]。并且项目进度管理在项目管理中所占的重要性比重相当大。项目进度管理是指为了对其项目开展的行为进行有效监管，从而运用科学工具，以时间作为主要的约束条件进行资源的整合优化。项目进度管理的成败影响着企业的经济效益，在信息技术高速发展的今天，项目能否按时交付甚至会影响到企业的市场占有率。

由于PVoC业务数字平台项目面临需求复杂不确定、市场变化不断、沟通跨度大和交付时间急等问题，这是传统瀑布开发方法所无法胜任的。而敏捷开发方法其迭代核心，强调了拥抱需求变化，主要是将完整的项目分割成一个个独立的，可视化的，可以单独运行的子项目。敏捷开发不仅继承了传统软件开发方法优势，还弥补了传统开发方法的不足。它允许在开发过程中对源源不断的需求和反馈进行调整。其中Scrum 敏捷开发方法就是运用了上述敏捷开发的理念而形成的一个具体开发流程，在整个项目全生命周期进度管理中运用该开发流程形成一套适应于本单位的进度管理模式，来加强进度管理。

运用 Scrum敏捷开发方法代替传统开发方式，优化软件开发流程，研究并改进出一套适合本单位的项目进度管理模式。将项目进度管理知识作为理论基础，吸收和运用国内外的相关数字化项目进度管理经验以及结合Scrum 敏捷开发方法，在每一个软件开发的迭代周期中进行项目进度计划和控制，实现项目的按期交付。由于C集团在TIC 行业中一直占据领先地位，这次研究中体现的管理方法和流程也可以为整个行业的数字化建设的加速追赶提供一定的参考价值。

# 1.2研究内容和方法

# 1.2.1 研究内容

本文研究了A公司在PVoC业务数字平台项目一期开发过程中存在的进度管理问题。以A公司软件项目开发过程中在的进度管理问题作为切入点，分析PVoC业务数字平台项目一期建设过程中存在的进度管理问题，引入了Scrum敏捷开发方法，从根本上拥抱需求变化，在项目过程中改进优化项目进度管理方案。以PVoC业务数字平台项目二期为试点项目，来确定新项目进度管理方案的可行性，为A公司的其他同类型项目的开发提供理论支持和事实依据。

# 1.2.2研究方法

本论文通过A公司的特点和业务数字平台项目一期存在的项目管理问题，通过德尔菲法对业务数字平台项目一期存在的进度延期问题进行主要原因总结，基于 Scrum敏捷开发方法设计了A公司的项目进度管理方案。其中采用加权决策矩阵对产品需求列表的优先级进行排序，采用敏捷估算扑克对用户故事工期进行估算，以及关键链、接驳缓冲区和燃尽图对进度进行监控。最终通过问卷调查对项目的实践效果进行分析。

# 1.3 论文组织结构

根据具体的研究内容，本论文的组织结构分为以下几章节：

第一章：绪论。介绍了A公司PVoC业务数字平台项目的背景、研究意义、研究内容、研究方法以及论文的组织结构等。对PVoC业务数字平台项目的重要性和对其进度管理的紧迫性就行了阐述，同时指出的本文的研究方向。

第二章：文献综述与基础理论。介绍本文所围绕的主要思想，从项目管理到软件项目管理到软件项目进度管理的层层递进，通过相关文献综述描述了相关概念。并对软件开发中应用的开发模式，从传统开发模式到敏捷开发模式，分别进行介绍分析。梳理相关Scrum敏捷开发，项目管理以及项目进度管理相关文献，科学分析项目现状。

第三章：A公司业务数字平台项目管理现状及问题分析。介绍了A公司组织架构以及业务数字平台项目特点和开发流程。通过德尔菲法对业务数字平台项目进度延期问题进行分析总结。

第四章：基于Scrum的业务数字平台项目进度管理方案改进。结合A公司的目前实际情况，针对第三章中分析得出的项目进度延期原因，运用Scrum敏捷开发方法进行新的开发流程的建立以及引入加权决策矩阵、进度缓冲区、燃尽图加强需求管理、进度监控，形成适用于当前项目特质的解决项目进度管理问题的方案。

第五章：业务数字平台项目二期进度管理实践。应用第四章的进度管理方案，首先运用新的流程进行了业务数字平台项目二期的开发，并在开发过程辅以项目进度管理改进措施。最终业务数字平台项目二期取得了良好的实践效果。

第六章：结论与展望。对本文的研究成果做出了总结，并提出后续A公司在进度管理方向可以再提升的方向。

![](images/dcaca1622b4992275f59174ec0f2c9f861fd92ea79ccb1dd5e6626b3ac983db1.jpg)  
本文的组织结构如下图1.1所示：  
图1.1论文组织结构图

# 第2章文献综述与基础理论

# 2.1软件项目管理的相关理论

项目管理这一概念最早起源于美国，作为当时重要的新型管理技术主要是为了第二次世界大战之后的国防建设项目重建[4]。

上世纪六十年代,项目管理的使用范围并不普遍,只在电子工程、航空技术等少数领域中有使用。但由于当时美国国防部发现美国众多软件交付不及时、质量不达标的很大部分原因在于缺乏有效的管理手段，再加上美国同期阿波罗登月项目所获得的巨大成功最主要原因就是项目管理的广泛应用,于是软件项目管理概念就在那一时期应运而生。逐步发展到七十年代，软件行业开始步入正规化，软件生命周期概念在那个时期被提出。同时该时期的代表是瀑布模型”理论。进一步发展到八十年代，人们开始明白确定软件开发管理方法需要考虑软件开发过程中的总成本和总价值。因此该时期的重要理论是软件能力成熟度（CMM)[5]。到了上世纪九十年代，软件开发已经不是单一区域、单一部门的活动，开始需要涉及协同工作。该时期的主要管理方法是统一软件过程（RUP）。

和国外的项目管理发展相对比，我国在项目管理方面的理论研究和实践上稍显逊色。上世纪六十年代，我国著名数学家华罗庚研究并引入了国外项目管理中的网络计划技术，重新将其命名为“统筹法”。该方法在中国的工农业以及军事上都起到了重要的管理作用。在我国的第一代战略导弹系统研制中，就充分应用了计划评审技术（PERT）进行管理，并取得了较大成功。到上世纪七十年代，我国引入了全寿命管理概念，并应用于上海宝钢等项目。直到八十年代，我国才全民推广了项目管理概念，并在很多领域取得了成功，包括实行项目管理的鲁布革水电站，工期提前了106天。到了九十年代，项目管理在我国建设部开始风靡，其中著名的三峡工程就是采用了项目管理方法，并最终顺利完工。在中国，项目管理也逐步在全球的影响下渗透到各行各业。2002年，随着“中国项目管理师”资格认证在中国的开展，也预示着项目管理在我国的地位突显。2006年，敏捷中国大会将敏捷开发引入了中国软件项目管理中，但整体由于接触时间较短，国内的敏捷开发仍滞后于国外，国内还是以瀑布开发模式作为主流开发模式进行软件

项目管理。

# 2.2软件项目进度管理的发展和理论

上世纪五十年代，迅速发展的生产力带来了大量的工程以及科研项目，而如何平衡项目中的人、财以及物成为了值得研究的课题。其中关键路径法（CPM)和项目计划评审技术（PERT）作为两种优秀方法在里面得到了广泛应用，使得在有限的资源下，完成项目可以用最少的时间和最低的费用[]。

PERT和CPM的主要区别在于活动是否可以预测[7]。在美国杜邦公司和兰德公司提出了CPM后，美国海军北极星计划在CPM基础上又开发了PERT技术，利用网络关系图表现出活动之间的关系，从而得出关键路径。经过PERT技术的应用，北极星导弹研制项目整体缩短了两年，也因此这两项技术在往后被经常结合使用，用来求得时间和花费的最佳控制。不同于传统的PERT和CPM方法，基于高德拉特博士的限制理论衍生出来的关键链项目管理（CCPM），通过灵活转换任务链之间的任务，替代传统方法的严格遵循进程，以保障项目的如期执行[8]。CCPM和传统的 PERT、CPM技术相比，项目实施速度增快至少五分之一到二分之一之间，并且成本降低了至少五分之一到二分之一之间[9]。蔡晨，万伟（2003）在传统PERT/CPM基础上，引入TOC 缺陷理论改善PERT三点估计存在的缺陷，通过实例论证关键链项目管理的可行性[10]。赵之友（2008）根据关键链项目管理技术的基础理论，从缓冲估计以及并行任务等问题出发，通过启发式算法来确定关键链，明确关键链是个不断调整的动态过程[I]。张善从，马丽丛（2016）通过熵权模糊评价法，结合风险管理，重新定义了缓冲区测度模型以针对高新技术项目的项目进度管理[12]。叶雷宏（2017）等人针对敏捷软件开发中的资源受限问题，将关键链应用到多项目敏捷开发中，通过设计选择算法和调度算法，使得工期缩短近五分之一[13]。

1983年PMI首次将项目管理知识体系作为一项标准进行发表。其中PMI将项目进度管理过程分为了6个子过程[14]。简而言之，项目进度管理就是保证项目及时完成以上各子过程，同时各子过程也并不是要严格按照以上顺序进行。我们需要遵循渐进明细规律，把各子过程穿插在项目管理全流程中，已达成项目进度管理。整个过程如图2.1所示，图中实线框代表了该流程的实际产出，而虚线框

代表配合的内容。

活动定义：是为了识别为实现工作包所必须采取的具体工作的过程。在明确了项目的大致范围基础上，将WBS分解成颗粒更小，更为具体的工作的最基本单元一一活动。此过程通过运用分解以及专家判断工具来输出活动清单，里程碑清单等内容。完成此过程需要通过详细规划近期工作，粗略规划远期工作，一步步渐进明细，逐步细化，而不是一蹴而就。其中里程碑指的是某些活动的完成取得了阶段性的进展。一般可以用里程碑图清晰明了的展示各个里程碑事件，便于进行直观沟通[15]。

活动排序：此过程是为了识别和记录活动之间相互的逻辑关系。项目中的活动存在一定的逻辑关系，需要理清活动之间的关系，进行排序，生成清晰简明的进度网络图。一般活动之间的逻辑关系分为四种，其中用A活动表示前面的活动，B活动表示后一活动：

（1）完成到开始：A活动完成后，B活动才能开始；  
（2）完成到完成：B活动需要等A活动完成才完成；  
（3）开始到开始：A活动开始后，B活动才能开始；  
（4）开始到完成：B活动需要等A活动开始才完成。

此过程，一般会利用前导图（PDM）以及箭线图（ADM）作为工具进行进度的编制。同时，需要明确活动资源之间存在的依赖关系，分别包括强制性、选择性以及外部依赖关系[16]。强制性依赖顾名思义两者活动之间是一定要满足的；选择性依赖关系指活动关系之间是存在优先排序的；而外部依赖关系是指活动不在项目内的控制关系。此过程的重要输出是项目计划网络图，其中紧前活动和紧后活动的确立，是重点。

活动资源估算：该过程主要是明确在项目实施过程中需要哪些资源（包括财力、物力以及物资等），以及各个资源使用的配备情况。项目经理需要考虑清楚资源对历时的影响，但同时又受活动的属性等客观因素的限制[17]。需要通过资源日历等的输入，利用专家判断或自下而上的估算，细化活动，输出相关的活动资源需求。这个过程输出的准确性就与项目成员的相关经验密不可分。

活动历时估算：根据资源估算结果，估算完成每个活动所需的时间。这个过程也是同样属于渐进明细的过程。过程输出的准确程度与项目相关信息的准确程度正相关。一般采用三点估算的方法，也称为计划评审技术（PERT），通过估算最可能时间，最悲观时间，最乐观时间，利用加权平均计算活动历时。这些估算会受到一些因素影响，包括项目人员的熟练程度以及突发事件的风险等。

制定项目进度计划：该过程主要是利用分析项目中活动的逻辑顺序、每个活动需要的时间、每个活动需要的资源以及相关的约束条件，从而输出项目进度计划。一旦项目进度计划得到批准，就是整个项目的基准，可以跟踪相关项目绩效。一旦制定了项目进度计划，也就明确了项目中里程碑的开始完工时间以及各类资源的估算。在制定计划过程中主要的技术工具有进度网络分析、关键路径法等。其中关键路径法是最为常用的方法。所谓的关键路径代表了项目所需要完成的最短时间。所以如果关键活动延误就会导致项目的整体延误。

控制项目进度：此过程主要用来监控项目进展，通过进度基准，比较项目实际与计划之间的偏差，并分析控制偏差促使项目向健康可控方向进行发展。项目经理在项目进度控制过程中的作用至关重要，需要随时监控项目进度以及项目中的变更，并对变更的缘由进行研究和管理。在此过程中可以借用优秀的项目管理软件，来进行偏差分析等，提供较为人性的接口来达到控制进度的目的。

整个过程中项目进度计划作为整个的基础，需要对其进行严格控制，全程以其作为指导标准。在进度控制中，要时刻对比计划进度和实际进度，及时对偏差纠因，并找出合理解决方案，保证整个项目如期完工。

![](images/74f4f2f7a44574a314edb17b00c4f01c3b39c7698779bc553258dcbf60071465.jpg)  
图2.1进度管理流程

# 2.3传统瀑布式开发模型概述

瀑布模型是目前我国最常用的开发模型，如图2.2瀑布模型图所示，该模型强调前一环节的输出是后一环节的输入，每个阶段只有结束才能进入下一个阶段，直线型模式使得开发过程简单直观，便于项目管理。瀑布开发模式有其特定的应用场合，针对需求频繁变动的项目，会造成高昂的项目变更成本[18]。不变更就会造成用户需求不满足。直线型模型一旦每个阶段没有得到落实，隐藏的问题只会在最后的测试阶段才能显现，试错成本太高。并且此模式在阶段终止前几乎没有反馈的时间。

![](images/8e3ad677111261d238b3c7d674b9b8b6467e0f1528cacf25d5d3345415b069e4.jpg)  
图2.2瀑布模型图

# 2.4 Scrum 敏捷开发方法

# 2.4.1 Scrum敏捷开发的发展

21世纪以来，各行业的信息化、数字化的研究加速发展。面对各类多变繁复的用户需求，企业为了快速抢占市场，开始逐渐从繁复的传统开发方式面向敏捷开发方法。常用的敏捷开发方法有XP开发方法，RUP开发方法和SCRUM开发方法。极限编程（XP）的思想是把软件开发过程中好的方法发挥到极限。其偏重于工程实践，但缺乏系统框架，不能有效指导项目先做什么，再做什么。统一过程（RUP），尝试着对软件开发过程中好的实践过程进行总结。但其框架过于繁琐，9个核心工作流程每一项都能写满一本书。SCRUM一词最早出现于橄榄球的争球，它能根据目标和价值驱动来选择最佳实践[19]。其关键在于对人的重视，从而应对不停的变化，而不是看中具体的过程，整个应对调整方式放弃了对过程规则的遵守，而是主动拥抱变化。

一般一种软件开发方法从出现到流行中间需要历时大概十几、二十年的时间，而 Scrum的诞生到现在已经二十余载，可以看出它正在朝着良好趋势发展，敏捷开发正逐步扩大广泛应用。

在众多敏捷方法中 Scrum 方法相对而言最为悠久。1986年，竹内弘高和野中郁次郎发布的文章《The New ProductDevelopment》，其中描述了灵活、自组织团队进行产品开发的流程[20]。其中提及的众多概念促成了 Scrum 方法，并且包括 Scrum一词。1993年，Easel公司将 Scrum过程应用到公司设计的面向对象设计和分析工具中，从此创立了软件项目开发管理中的 Scrum过程。1995年，Ken Schwabeer和 Jeff Sutherland合作，根据多年经验教训总结出一套新的增量迭代式敏捷开发方法，即 Scrum，并将成果发表在OOPSLA1995。在2002年KenSchwabeer 和 Mike Cohn一起创立了一个联盟——Scrum 联盟。

2001年，在Uath的Snowbird，十多位软件领悟专家成功发布了敏捷开发者宣言，并给以上极限编程（XP），动态系统开发方法（DSDM），特征开发驱动（FDD）的几种方式定了一个名字，称为敏捷[21]。在敏捷开发者宣言中透露了关键信息：个体与交互，可工作的软件，客户合作以及响应变化分别优于过程与工具，面面俱到的文档，合同谈判以及计划的遵照[22]。随后2011年开始，精益，Scrum 等基于敏捷框架且与敏捷原则相匹配的方法和技术都被称作是敏捷开发的技术。敏捷宣言距今已然20年，敏捷开发已然成为了主流。

在国外，很多知名企业例如谷歌、微软都已经正式使用了敏捷开发，特别是Scrum敏捷开发。Scrum敏捷开发包含了各类子迭代的过程框架。

# 2.4.2 Scrum 敏捷开发的内容

1）Scrum敏捷开发的主要流程如下[23]：

（1）将产品待办列表分解成迭代待办列表，每个迭代待办列表是指该迭代中应用现有的资源可以完成的；（2）迭代计划会议召开：标注相关任务的优先级并分配给团队成员；（3）迭代过程：每日进行简会；（4）迭代周期结束：开展迭代审核会议，像相关方演示成果；（5）迭代评审会召开：总结经验教训；（6）重复迭代过程。

# 整个过程如图2.3所示：

![](images/84ab9d0082a57e724658e01da9b15c85242ff1a931e26cf346127e39d4783051.jpg)  
图2.3 Scrum 过程流程图

2）Scrum敏捷开发的核心要素

敏捷开发的核心要素是所谓的三三五五要素，分别指的是：三个核心角色，三个工件，五个关键事件和五个价值观[24]。

三个核心角色：

（1）产品负责人。产品负责人顾名思义是对产品进行负责的人员，其主要职责是根据用户需求整理出一份产品待办列表，并对产品待办列表中的任务的优先级进行排序。由于需求可能存在时刻地更新，产品负责人需要在长期的迭代过程中维护好产品待办列表。

（2）敏捷教练。有点类型传统项目中的项目经理，但也不仅仅是个项目经理。其主要作用就是引导整个开发团队在敏捷道路上顺利前进，协助解决任何有碍于敏捷开发的事情，从而达成整个团队的商业价值，是一个服务型角色[25]。其主要责任是帮助团队成员在敏捷状态下完成工作，不受打扰。

（3）敏捷团队。共同开发产品负责人所指定的产品。对交付结果负责。

三个工件：

（1）产品待办列表。主要又产品负责人进行全程维护。是整个项目的基础。（2）迭代需求列表。来源于产品需求列表。由团队评估和选择进行一起“完成定义”。（3）可交付产品增量。可以在迭代评审会上进行展示的一个迭代周期的产品新增部分。

五个关键事件：

（1）迭代。迭代是一个容器事件，后续四个事件包含在其中。

（2）迭代计划会。是整个迭代周期的开始，用来确定下一个迭代周期需要完成的目标。将在产品待办列表中选取相关优先级高的任务进入迭代，对于不清楚的需求，将在会上进行提问和产品负责人进行解答。在会议上会对其中用户故事进行团体估时。

（3）每日站会。主要在每天固定时间进行了为期大约15分钟的会议，主要是促进团队内信息共享，但不会对问题进行长时间讨论。

（4）迭代评审会。在迭代末期，由开发团队向相关干系人进行此次迭代的成果展示。此次会议需要全员参与，并对迭代成果进行验收。

（5）迭代回顾会。在一次迭代结束后，全体成员对整个迭代过程进行回顾，总结经验教训。对过程中的问题进行提炼，包括已解决和未解决的。

五个价值观：分别为开放、尊重、勇气、专注以及承诺。

# 3）Scrum敏捷开发的相关工具

（1）白板。白板是最直接地进行跟踪汇报的工具。可以每天把相关的任务点写在白板上，进行直观展示，但由于白板的板面的有限性，无法容纳所有的产品待办列表，所以也就无法跟踪历史记录。

（2）燃尽图。是由横轴和纵轴组成的简单二维图表表达剩余工作量的图标[26]。其中横轴表示时间，纵轴表示工作量。这种图表可以直观的预测何时工作将全部完成。

# 2.4.3 Scrum敏捷开发的研究现状

敏捷开发距离成熟还很远，但一直在发展的道路中成长。经过CollabNetVersionOne2022年最新的敏捷状态调查，其中 $9 7 \%$ 的受访者都在实践敏捷方法，而其中 $65 \%$ 的受访者更是实践超过3年或者更多。而敏捷方法中Scrum敏捷开发是最为流行的，其加速软件交付的特性倍受企业青睐。

众多学者从不同角度对Scrum敏捷开发方法进行了研究和应用。孙杰成,颜锦奎[27]遵循 Scrum 敏捷开发核心思想基础上实行演进式设计，坚持多层测试、持续集成和测试驱动开发,重视重构和简洁设计，高效推进跨境电商平台的开发实施；严晶[28]基于Scrum敏捷开发思想，通过迭代增量式开发模式加强沟通提高效率，并在迭代增量式过程中对迭代目标进行控制从而把控全周期进度；窦淑宝[29]通过应用Scrum中用户故事的估算来解决项目范围管理中存在的各类问题，应用Scrum挑战管理范围边界，在不影响现有产品的情况下，还能以最快的时间成功上线新项目。

# 2.4.4 Scrum敏捷开发对比瀑布开发模型的优势

相较于传统的软件开发方法，特别是瀑布式软件开发，敏捷开发存在以下多个优点：

1）收益早

敏捷开发是增量迭代过程，就意味着功能可以尽早抢占市场，迎来收益。研究表明，近 $8 0 \%$ 的市场领先者是第一批进入市场的产品。敏捷开发就做到了，尽早向市场推出产品，且后续还能不断根据用户需求进行改进发展，时刻在第一刻满足用户。

# 2）质量高，风险低

敏捷开发并不像传统软件开发方式，是按照软件开发再软件测试的逐步顺序进行，它是软件测试和软件开发同时进行，时刻确保软件产品的质量。越早介入测试工作，产品能越快进行调整，所承受的风险代价也更小，同时交付的产品质量也更高。

# 3）敏捷灵活性高

传统的瀑布式开发方法，由于其瀑布模型的递推进行，导致任何环节的需求变更都会造成严重的影响，需要在项目开发过程中成立需求变更委员会，以控制需求的严格变更[30]。软件行业不同于一般行业，在项目的开发周期中，需求肯定存在变化，而敏捷开发拥抱需求的特性，使得团队可以及时对需求变化进行响应。

# 4）项目干系人满意度高

敏捷开发主张的是项目的干系人可以参与到整个项目开发过程，不停通过需求变化讨论，提高干系人满意度。

# 5）工作环境好

敏捷开发倡导的是积极合作，项目团队有权决定项目需求，帮助团队集体融入项目。

# 2.5基于Scrum敏捷开发的软件项目进度管理优化实践

Scrum敏捷开发在进度管理中主要有以下几块优化实践：

（1）项目计划优化：Scrum敏捷开发中强调的是“小批量，频繁交付”[31],所以每次迭代涉及的项目计划比较容易，易于掌握。

（2）任务并行缩时：可以尽可能让各个任务并行实现优化。当存在一个功能A依赖于另一个功能B，可以利用测试桩模拟B功能实现，使得A功能开发测试可以独立于B功能，使得任务并行，缩短工时。

（3）重复工作减少：在以用户故事为驱动的迭代过程中，后期的迭代会涉及多个用户故事的实现架构于同一个代码模块，这就需要增加资源。如果在资源固定的情况下，就有可能造成误工。所以可以在一开始并行开发这些用户故事，再统一进行测试，可以避免工作重复，资源浪费。

（4）进度信息及时获取：Scrum敏捷开发的每日简会会介绍各自完成了什么，计划做什么，可以充分发布和获取进度信息。

# 2.6本章小结

通过相关国内外文献研究分析，发现经过这么多年的发展，软件行业的项目管理方法日益丰富且各有特点。项目管理中的重要一环一一项目进度管理，其传统模式是按照活动的定义、排序以及活动资源、活动历时的估算，最后进行项目进度计划的制定和控制展开的。作为进度管理工作必不可少的流程，任何软件开发模式都需要围绕这几项内容展开对项目的进度管理。Scrum敏捷开发已经为欧美的大多数软件企业所应用，例如微软、英特尔等此类驰名中外的大企业，在项目进度管理中也起到了一定的优势。近几年随着国内软件行业的发展和深受外包影响，开始逐渐普及敏捷开发模式。敏捷开发模式利用若干个短的迭代周期从而实现快速交付高质量产品，它允许在开发过程中对源源不断的需求和反馈进行调整。但其对IT从业人员的要求较高，企业内，特别是非专业软件企业对于其应用存在心有余而力不足。并且敏捷开发重人才轻文档的方式也对许多企业不友好。在A公司软件项目中，需求复杂不明确、需求变更频繁、沟通跨度大和交付时间急等问题，可以应用敏捷开发迭代核心，强调拥抱需求变化[32]，主要是将完整的项目分割成一个个独立的，可视化的，可以单独运行的子项目，运用了Scrum敏捷开发的理念而形成的一个具体开发流程，在整个项目全生命周期进度管理中运用该开发流程形成一套适应于本单位的进度管理模式，来加强进度管理。

# 第3章A公司PVoC业务数字平台项目管理现状及问题分析

本章通过对A公司概况、组织架构以及目前应用的开发流程等内容介绍，结合上一章的理论知识，运用德尔菲法对其业务数字平台一期项目存在的进度延期问题进行分析，并归纳总结出其进度延期原因。通过提炼业务数字平台一期项目进度管理不足的关键因素，为后续A公司相关项目进度管理优化方案提供可靠支撑。

# 3.1A公司概况

# 3.1.1A公司简介

A公司是一家第三方检验检测C集团公司下属的宁波子公司。C集团公司是一家以检验、鉴定、认证、测试等为主营业务的综合质量咨询与服务组织，是一家由国务院国资委进行管理的央企。C集团自2018年启动信息化建设以来，始终以贯之大格局信息化建设理念，努力向先进的理念和技术靠近，致力于搭建起科学先进的底层架构，得以有力支撑产品线建设和现代化管理体系建设。A公司作为C集团的核心下属公司，是一个在业务领域内享有盛名的检验认证机构。A公司的信息部门，作为C集团下面信息化程度较高的信息中心，在集团的整体信息化推进中，也起到密不可分的作用。

# 3.1.2A公司组织架构

A公司的组织架构图如图3.1所示：

![](images/e4d38b36a6a6a4984098768b11c53705648b76f16c4cfe98dca891165176dca0.jpg)  
图3.1A公司组织架构图

A公司主要由职能部门和业务部门组成，属于传统的职能式组织结构，各部门按职能划分。其下设的信息技术部负责软件开发项目的需求调研以及开发并进行最后的运维。目前信息技术部有1名部门经理，3名软件开发工程师，2名软件测试工程师和1名软件运维工程师。部门经理兼任需求分析人员。

# 3.2PVoC业务数字平台项目管理现状

# 3.2.1PVoC业务数字平台项目简介

其中PVoC业务是指出口前产品与标准的符合性验证(Pre-ExportVerificationof Conformity to Standards，简称PVoC）计划。PVoC业务是由肯尼亚标准局提出的，其主要目的是通过对出口国的相应商品进行检查评定来保障进口产品的质量。一般KEBS会授权专门组织机构出具符合性证书（CoC），所有货物必须要获得相应证书才能运送到肯尼亚。这份符合性证书带有强制属性，没有证书就没法入关。随后尼日利亚、埃及等国标准局也有了同等规定。C集团分别被肯尼亚标准局(KEBS)授予中国、台湾、香港、蒙古和阿联酋地区开展PVoC项目，被尼日利亚标准局(SON）授予全球范围内的运营 SONCAP项目，被埃及进出口监管总局（GOEIC）授予全球范围内的运营VoC项目，被博茨瓦纳标准局（BoBs）授予全球范围内的运营 SIIR项目，对经检验合格的产品签发COC证书。C集团下的各子公司也分别承担各地的PVoC业务执行。

为适应业务不断扩大和发展需要，优质、高效、快捷地开展货物装船前检验及符合性验证业务（即PSI、PVoC），并完善业务各环节的数字化以及实施过程数据化保存的过程，C集团决定开发PVoC业务数字平台。该业务数字平台不仅涉及多个国家不同业务需求，而且功能繁杂，包括建立全流程的业务管理系统，建立和各国标准局的数据对接，落实权责发生制以及推广电子证书使用范围等。由于业务数字平台项目的功能繁杂性，整个项目实施将分为三期：一期为基础功能需求开发，包括业务涉及的汇率管理、标准库管理、标准局数据传输接口等；二期为具体功能需求开发，包括业务受理、派单分包、出证发证以及计费收费等；三期为相关报表、表单功能模块的开发。

由于A公司的信息部门，作为C集团下面信息化程度较高的信息中心，承接了PVoC业务数字平台项目的开发任务。

# 3.2.2PVoC业务数字平台项目特点

PVoC业务数字平台项目的开发任务作为A公司集团公司的重中之重项目，不同于之前的公司内部项目，对整个TIC行业的业务数字化转型也有重要影响。该项目有以下几个特点：

1）苛刻的交付时间。为了抢占肯尼亚等非洲市场的符合性验证业务，需要在较短的时间内完成PVoC业务数字平台项目，以获取相应的业务资格，一旦延期将面临大单业务流失。

2）频繁的需求变更。此块业务涉及的客户已经不是先前公司内部人员，涉及多国客户之间的沟通，此块业务需求也没有先例可寻，非洲相关负责人对软件知识知之甚少，对于整个项目具体的内容缺乏详细规划，种种这些因素就导致了项目初期，需求变化频繁。需求不明确就仿佛是建筑的蓝图不明确，后期的设计开发就缺乏蓝图的指导，会导致最后成果出现偏离需求的结果，同时部门经理作为需求调研人员也会不断调整需求，引起整个项目需求频繁改变。

3）紧缺的开发人员。由于A公司并非传统的软件开发公司，仅依靠其下信息技术部的开发人员，也会变相加重项目延期的可能性。

3.2.3PVoC业务数字平台项目一期开发流程概述

A公司虽然已然是C集团下具有较高软件项目开发水平的公司，但由于整个公司并非一个传统软件开发公司，其受限于信息技术部门的能力，一直采用的软件开发模式是传统的瀑布开发模式，整个开发流程采取的是传统又广泛应用的模式，分为如下几个阶段，大致如下图所示。

![](images/4ffc7ef9f3e3fb6e0760936733a6bd19e3a84a167971a9c6da928fa9b71e7116.jpg)  
图 $3 . 2 \mathrm { ~ A ~ }$ 公司开发流程图

下面具体从以上这几个阶段详细说明具体的内容：

1）需求分析阶段：基于瀑布模型“每个后置阶段都会依赖于前置阶段的输出”特点，作为软件项目第一步的需求分析至关重要。A公司的开发需求大多数来源于内部业务部门的需求以及上级集团所下达的需求，但内部需求挖掘并不如看起来的这么轻松，越是内部需求越有后期变动的可能性，所以我们会通过会议和头脑风暴模式收集需求，并进行分析、整理以及挖掘，最后生成《需求规格说明书》。瀑布模式的开发流程，这一步尤为关键，需求定义错误，就会导致后期的开发方向错误，进而整个过程都往错误方向行进，就将导致项目的失败。

2）架构设计阶段：部门的架构师会根据前个阶段的《需求规格说明书》进行相关分析，明确用户角色、使用场景和流程等，最后形成软件产品架构。

3）详细设计阶段：在这个阶段一般会形成《详细设计说明书》，其中需要清晰定义每个接口、模块等，并明确列出相关操作步骤和呈现结果。

4）编码阶段：软件开发人员将通过上个阶段的输出文档，开始进行代码的编写。这个阶段通常就是不合理需求的暴露阶段，也是各个文档的实时性维护的失效阶段。软件开发人员可能会根据实际修改代码，而未及时更新到文档上，造成团队的信息不对称问题。这些问题都将在下面的单元测试等测试阶段才会发现，因此有可能会延误项目工期。

5）测试阶段：软件测试人员编写相应的测试用例进行软件测试，同时向开发人员反馈软件缺陷，并在相应软件中进行跟踪记录。在瀑布开发模式下，各个接口的测试，模块之间的单元测试，都是在项目的最后进行测试，不仅任务重，而且返工代价极大，在紧迫的交付时间重压之下，也容易造成最后的交付质量极差。

PVoC业务数字平台项目一期就是按照以上的开发流程进行开发。

# 3.3PVoC业务数字平台项目一期进度延期问题分析

A 公司信息技术部由于之前承接的软件开发项目基本都是来自公司的内部需求，针对项目的进度延期问题也属于公司内部问题，所以具有一定的容忍度。但业务数字平台项目这项外部任务存在功能点多且需求变化频繁、完工时间紧等问题。业务数字平台项目一期建设就在严重的延期问题下结束。并且项目延期问题在A公司的前期各个软件开发项目管理中均有体现。根据A公司信息技术部的项目文档有效数据统计，自2015年至今，部门共承接10个检验检测认证相关的信息系统项目，其中9个为公司内部需求项目，9个项目及时交付率仅为 $4 4 . 4 \%$ ，换言之至少有一半项目存在不同程度的延期。并且针对延期项目仅仅提供了赶工措施进行纠偏，并未妥善解决进度延期问题。作为TIC行业的龙头企业，信息技术化水平相较于行业其他企业已经较高，但在信息化这块仍属于短板。也由此可见A公司在软件项目开发过程中存在较大的进度管理问题。

为了业务数字平台项目二期的顺利推进，攻克一直以来的进度延期难题，就需要用科学的方法对项目一期出现的进度问题进行分析，找出其根本原因。根据前文对业务数字平台项目的特点分析和A公司一直以来应用的开发流程概述，通过采用德尔菲调研法来对PVoC业务数字平台项目一期的进度延期问题进行调研。

德尔菲法又名专家调查法，顾名思义是向专家针对特定的问题进行咨询，并进行整理归纳，通过反复整理咨询，直至各专家给出一致意见，其实质上是一种反馈匿名函询发。德尔菲法具有以下特点：

1）结论的充分性。该方法是充分吸收不同专家的意见、经验和学识，集思广益，能取各个专家之长，避他们之短。其结论存在一定的专业性、充分性。2）结论的可靠性。由于采用匿名不见面的方式，各个专家都会做出独立而忠于自己的判断，不会因为权威而左右自己的意见，也不会因为情面，而不敢发表不同的意见。因此结论存在一定的可靠度。3）结论的统一性。该法需要经过几轮的反馈，在每次的反馈中对专家的输出进行分析和深入研究，最终得出趋同的意见。

德尔菲法在项目管理中也被广泛应用，例如收集需求，估算进度等。本文应用德尔菲法进行进度问题的分析，以下是具体的实施步骤：

1）专家选取。一般来说，专家的选择要全面且精通于相关理论的人。根据本文的研究方向，函询专家入选标准定为：（1）参与过本公司项目的技术专家；（2）项目管理方面的技术专家或业务相关专家；（3）学历本科或以上。最终专家团队由10人组成，其中包括A公司资深软件开发2人、资深软件测试2人、A公司相关业务专家4人，集团业务专家1人，信息技术专家1人。其中A公司8人参与过业务数字平台项目一期建设，而集团的2位专家熟悉PVoC业务，在项目一期中有过协助。同时为了让各位专家对项目一期有更全面的了解，将项目的全过程文档以及进度时间表和客户反馈进行整理，以供专家们参考。

2）第一轮调查。通过公司内同事头脑风暴和文献研究设计了相关进度影响因素的调查表格（调查表格详见附录1)，再通过发函分发调查表格给各位专家，让他们各自站在自身的专业角度，对项目一期延期影响因素进行勾选，并站在自己的专业角度，进行分析补充。最后再对回收回来的调查表格，进行整理合并，其中团队缺乏自驱力，团队组织结构不合理以及项目开发流程不合理是专家进行补充说明的影响因素，最终得出如表3.3问题频次表。

表3.3问题频次表  

<table><tr><td>序号</td><td>类型</td><td>影响因素</td><td>出现频次</td></tr><tr><td>1</td><td>范围</td><td>需求变更频繁</td><td>10</td></tr><tr><td>2</td><td>资源</td><td>团队缺乏自驱力</td><td>10</td></tr><tr><td>3</td><td>沟通</td><td>团队与干系人之间缺乏有效沟通</td><td>9</td></tr><tr><td>4</td><td>整体</td><td>项目计划变更调整缺乏及时性</td><td>8</td></tr><tr><td>5</td><td>沟通</td><td>团队之间缺乏有效沟通</td><td>6</td></tr><tr><td>6</td><td>整体</td><td>项目开发流程不合理</td><td>5</td></tr><tr><td>7</td><td>整体</td><td>团队组织结构不合理</td><td>5</td></tr><tr><td>8</td><td>范围</td><td>需求优先级不明确</td><td>5</td></tr><tr><td>9</td><td>整体</td><td>项目缺乏进度监控手段</td><td>5</td></tr><tr><td>10</td><td>资源</td><td>文档过多，相关工作量过大</td><td>4</td></tr><tr><td>11</td><td>范围</td><td>需求范围蔓延</td><td>4</td></tr><tr><td>12</td><td>质量</td><td>开发成果多次返工</td><td>2</td></tr><tr><td>13</td><td>资源</td><td>团队开发人员技术不佳</td><td>1</td></tr><tr><td>14</td><td>资源</td><td>开发设备资源紧缺</td><td>0</td></tr></table>

对以上问题频次表的结果进行整理，筛选出出现频次超过调查人数半数5人的进度延期问题影响因素。由于14项影响因素中文档过多、需求范围蔓延、开发成果多次返工、团队开发人员技术不佳以及开发设备资源紧缺这5项出现频次过低，从影响因素中进行剔除，整合成专家评价表。

3）第二轮调查。通过上述专家评价表，编制第二轮调查表格（调查表格详见附录2），然后对同一批专家进行二轮调查，确定导致业务数字平台项目一期进度延期的主要因素。

4）调查结果分析。最终结果如表3.4汇总表所示。

表3.4汇总表  

<table><tr><td>类型</td><td>序号</td><td>影响因素</td><td>出现频次</td></tr><tr><td rowspan="2">范围</td><td>1</td><td>需求变更频繁</td><td>7</td></tr><tr><td>2</td><td>需求优先级不明确</td><td>5</td></tr><tr><td rowspan="2">沟通</td><td>3</td><td>团队与干系人之间缺乏有效沟通</td><td>3</td></tr><tr><td>4</td><td>团队之间缺乏有效沟通</td><td>4</td></tr><tr><td>资源</td><td>5</td><td>团队缺乏自驱力</td><td>4</td></tr><tr><td>整体</td><td>6</td><td>项目计划变更调整缺乏及时性</td><td>2</td></tr></table>

续表3.4汇总表  

<table><tr><td>类型 序号</td><td>影响因素</td><td>出现频次</td></tr><tr><td>7</td><td>项目开发流程不合理</td><td>9</td></tr><tr><td>8</td><td>团队组织结构不合理</td><td>7</td></tr><tr><td>9</td><td>项目缺乏进度监控手段</td><td>5</td></tr></table>

根据上述汇总表，其中出现频次超过5的是分析得出的主要影响因素，即项目开发流程不合理、需求变更频繁、需求优先级不明确、团队组织结构不合理以及项目缺乏进度监控手段。据此得出这四项是影响A公司业务数字平台项目一期进度的主要因素。

通过对业务数字平台项目一期进度延期因素的专家调查得出了以上5个相关重要因素，此些因素可以划分为与开发流程相关的项目开发流程不合理以及与项目管理方面相关的需求变更频繁、需求优先级不明确、团队组织结构不合理和项目缺乏进度监控手段。所以从上述相关最重要影响因素中进一步分类分析得出其项目进度延期的原因，主要分为以下两大类：

1）项目开发流程方面。固化老旧的开发流程难以推进产品质量和效率提升。软件开发团队或者项目经理会选择一套适合新项目的软件开发流程展开整个软件项目。软件开发流程能完整地体现整个活动需要完成的主要任务。适合的开发流程和项目管理可谓是相辅相成的作用。开发流程是项目管理的基础，开发流程如果是一条高速公路的话，项目管理就是其中的交通规则。规则固然重要，但重中之重还是所谓的地基。作为基础的开发流程不随着公司业务发展而发展，不随着整个环境发展而发展，固守成规，再好的项目管理也永远有效率以及质量的最高限制。A公司近些年来项目的进度延期，包括业务数字平台项目一期反应出来需求变更频繁、文档过多以及缺乏沟通等问题，都显示出一直使用的瀑布开发流程已经不太适应现在项目的需求。

2）项目管理方面。

（1）项目团队组织结构不合理。A公司的软件开发一直延用的是瀑布开发流程，由于瀑布开发流程的直线型特性，每个后置阶段都依赖于前置阶段的输出，团队内的各阶段的员工彼此缺乏过程内沟通。整个项目的整体进展只有项目经理才完全掌握，团队内各个成员无法及时分享、及时沟通项目过程中存在的问题，从而造成问题可能在较后期才发现，改进成本陡增，且会造成进度延误。而且，项目经理作为项目需求的调研人，而项目需求的执行人是其他开发人员，那项目过程中遇到需求变更等问题，又要层层沟通，增加沟通中噪音的存在，导致不仅无法实时响应客户的需求，且需求执行最终不到位，影响最终效率。整个组织结构的不合理，其一严重阻碍了团队内的沟通，导致团队自驱力和自主性都有所压制；其二阻碍了团队和项目干系人的沟通，导致无法积极响应需求的变更。

（2）需求管理不到位。A公司业务数字平台项目一期中面对需求的取舍缺少一定的决策管理，且缺少辅助工具。面对项目需求变更频繁，整个团队没办法做出及时响应。A公司仅仅使用WBS任务分解结构对需求进行细分，但没法直观体现需求点的优先级。业务数字平台项目是一个大型的功能繁杂的且需求不明确，可能随时进行变更的软件开发项目，对于需求的变更需要投入更多的管理，不然就会直接影响到整个项目的进度交付。而需求的优先度也间接影响了进度计划，从而也会导致进度的失控。

（3）项目进度监控措施缺乏有效工具。在瀑布开发流程下的业务数字平台项目一期，进度计划主要由项目经理个人经验主义进行任务的优先级排序和工时估计。个人经验主义的精确性暂且不论，但一旦遇到需求的频繁变更，项目进度肯定会落后于计划时间。在一期开发中，主要应用的进度管理工具是甘特图，它虽然直观显示了项目计划进度和实际进度信息，但对于各项活动之间的依赖关系没法系统地反映，没法进行计划的优化。项目进度管理中比较重要的两块就是项目进度计划和项目进度监控，而在业务数字平台项目一期的开发过程中，并没有给与一定的有效工具协助，项目的进度延期也确实在所难免。

# 3.4本章小结

本章对A公司的概况进行了介绍，并阐述了A公司PVoC业务数字平台项目管理现状。通过德尔菲法对业务数字平台项目一期进度延期问题进行了归纳总结，得出了项目流程和项目管理两块内容的进度延期原因，包括固化老旧的开发流程、不合理的项目团队组织结构、不到位的需求管理以及项目进度监控措施缺乏有效工具，为后续新的进度管理方案提出，奠定了坚实的基础。

# 第4章基于Scrum的PVoC业务数字平台项目进度管理方案改进

在第三章中，对A公司PVoC业务数字平台项目一期的延期原因进行了总结，其延期原因主要包括固化老旧的开发流程、不合理的项目团队组织结构、不到位的需求管理以及项目进度监控措施缺乏有效工具。A公司意识到必须整理出一套适合自己公司的项目进度管理方案，才能使得接下来的业务数字平台项目二期能有效进行进度控制。基于Scrum敏捷开发方法注重流程、组织文化以及工具结合的特性，其迭代的本质能快速响应市场，A公司决定从Scrum敏捷开发方法入手，结合公司实际，做出适合公司的项目进度管理方案。

因此，基于理论和实践的深入研究，结合A公司业务数字平台项目一期进度管理的主要问题，本章将引入Scrum敏捷开发方法到A公司项目进度管理中，以期解决目前无法解决的进度问题。

# 4.1Scrum在PVoC业务数字平台项目进度管理的适用性分析

第三章中提及了A公司PVoC业务数字平台项目一期的进度延期主要原因分为两大块，包括固化老旧的开发流程以及项目管理方面的不合理的项目团队组织结构、不到位的需求管理以及项目进度监控措施缺乏有效工具。

4.1.1 Scrum在PVoC业务数字平台项目开发流程中的适用性

A公司在往期的项目开发中一直应用的是传统的瀑布开发模型，包括这次业务数字平台项目一期。直线型开发模型一一瀑布开发模型，在第二章的相关基本理论中就有描述，它的特性是后一个阶段的输入是前一个阶段的输出，一旦过程中某个阶段没有得到落实，隐藏的问题只会在最后的测试阶段才能显现。而第三章中描述的业务数字平台项目特点显示，项目具有频繁的需求变更。由于业务需求端到端交付周期长，已经无法满足快速响应客户的需求。客户作为需求方在项目的前期很多时候对自己的需求并不了解，或者在前期的需求挖掘中，项目经理对需求的挖掘也不够深入，所以在项目过程中会出现反复变更，从而影响进度。瀑布模式开发流程具有较低的自由度，对后期需求的变化难以调整，且需要付出非常高昂的代价。而Scrum敏捷开发的本质是迭代和增量，恰恰针对的就是通过小增量，严防出现严重的客户需求偏离，通过每次迭代的产出，给客户最直观的感受。所以A公司引入Scrum敏捷开发方法，替代原先的瀑布模型开发流程具有适用性。

4.1.2 Scrum在PVoC业务数字平台项目管理中的适用性

第三章中提及的不合理的项目团队组织结构、不到位的需求管理以及项目进度监控措施缺乏有效工具这几个主要原因，都能在Scrum敏捷开发方法中得到解决方案。Scrum敏捷开发方法项目管理的核心要素就是三三五五要素。所谓三三五五分别指的是角色，工件，活动以及核心价值观。三个角色分别是产品负责人、敏捷教练以及敏捷团队。三个工件是产品需求列表、迭代代办列表和可交付产品增量。五个活动是迭代、迭代计划会、每日站会、迭代评审会和迭代回顾会。五个核心价值观是专注、勇气、开放、承诺和尊重。这些核心要素都能在A公司业务数字平台项目的项目管理中得到适用。

首先A公司的组织结构是职能式，信息技术部的开发成员接受部门经理的领导，团队成员属于被动型接受，缺乏自主性，整个团队逐渐丧失活力，仅仅是按部就班完成任务，团队内的沟通交流也一度缺乏。而Scrum敏捷开发方法强调的是人的作用，以人为导向，它的三个角色要素就强调了团队成员一起共同完成目标，可以激发团队的内驱力，同时A公司的开发人员符合敏捷方法要求的高素质人才，人数也满足敏捷方法中的小团队。

其次需求管理方面，Scrum敏捷开发方法中产品需求列表、迭代需求列表以及每日站会、迭代评审会都能保障频繁变更需求的快速响应。Scrum的小迭代能时刻关注需求变化。

最后进度监控管理方面，Scrum敏捷开发方法提供的每日站会、燃尽图、进度缓冲区方法，都能做到及时监控项目的进度，同时让项目管理者能及时对实际进度的偏离做出响应，控制进度。

综上所述，Scrum敏捷开发方法在业务数字平台项目的进度管理研究中具有适用性。

# 4.2基于 Scrum 的开发流程构建

Scrum敏捷开发方法只是一种指导思想，具体的开发流程实践步骤，还是要根据公司项目的业务特点和自身组织结构的实际情况进行制定。所以基于Scrum敏捷开发方法，建立了一套适合A公司业务数字平台项目的开发流程，新的开发流程如图4.1所示。

![](images/3870127520aee8310966997beaba6c766b923154f93bfd9f6cb6427532d45945.jpg)  
图4.1A公司新开发流程图

新的开发流程图是基于图2.3Scrum过程流程图，根据A公司实际情况和业务数字平台项目暴露出来的进度延期关键问题优化调整而来，具体的流程如下：

1）项目团队组建。

每个项目的开始都需要成立一个项目团队，这一步骤主要根据 Scrum敏捷开发方法三个角色的要素以及A公司实际情况，对整个项目团队进行产品负责人、敏捷教练以及敏捷团队的角色分配。同时为了使整个新的开发流程有序有质进行，针对新的敏捷思想引入团队培训。通过团队培训消除新方法带来的焦虑抵触情绪，促进敏捷转型的成功以及整个开发流程的顺畅。

2）需求范围确定。

引入Scrum敏捷开发方法中的产品需求列表，并用用户故事替代原始需求文档进行需求管理。由产品负责人对需求进行梳理。产品负责人会收集相关干系人的需求，然后根据业务角度，评定优先级，整理出产品需求列表。由于业务数字平台的需求在一开始并不明确，是需要在过程中不断完善，所以需要更为轻便的工具对需求进行管理。作为敏捷开发模型下的用户故事需求管理方法，刚好适用于这种特性。敏捷开发方法中一般需求会被分为史诗、特性以及用户故事三大类，此三类是逐层往下，颗粒度逐层变小。在进入下一阶段的迭代中，是必须以用户故事的颗粒大小才能得以进行。颗粒越小的用户故事表达，越能使得需求方、开发人员和测试人员等团队成员保持对需求的一致理解，越不容易造成进度延误。用户故事的格式将按照“作为_ (角色)，我可以_(功能或行动)，以便(价值或目标)”进行。我们应用了BillWake在2003年提出INVEST特性来规定用户故事，其五大特性分别为独立性(Independent)、可协商性(Negotiable)、有价值的（Valuable)、可估算的(Estimable)、简略的(Small)、可测试的(Testable)。由于A公司一直采用的是传统的需求规格说明书形式，为了敏捷转化的顺利过度，需求就按照“史诗-用户故事”两层进行分解。

3）迭代计划会召开。

Scrum敏捷开发的精髓就在于少量多次的迭代。每次迭代前，需要有一个迭代计划会。迭代计划会是每个迭代周期的开端，是整个开发流程中比较重要的活动。在计划会上，开发可以根据实际情况，根据需求资源的需要情况，对需求进行删减。同样，一旦需求优先级发生改变，产品负责人可以根据实际情况在会上对需求的优先级进行重新排序。计划会后，正式进入新的一个开发周期。整个会议需要产品负责人、敏捷教练以及团队成员共同参加，其会议的主要目的是安排迭代工作中需要完成的用户故事。会议中，产品负责人需要带着事先准备好的产品需求列表，向团队进行讲解，并需要告诉团队产品需求列表中用户故事的优先级顺序。一旦进入迭代，任何人都不能单方面变更内容，所以迭代的前期活动迭代计划会更显得尤为关键。在迭代计划会上，团队需要承诺完成多少个用户故事，这就涉及到了工时估算。由于敏捷开发的特性，每个用户故事的最终执行者是不固定的，由于团队每个成员的工作能力也各不相同，那此时工时估算即使耗时耗力也很难精确估算，而故事点估算确是一个行之有效的方法。故事点估算指的不是一个精确的时间，而是一个用来度量用户故事规模的模糊单位。用以衡量故事点的数字并不重要，但每个故事点之间的数字大小对比才是其意义所在。比如一个故事点为2的用户故事所需的工时就是一个故事点为1的用户故事的两倍。故事点估算无所谓单个用户故事的数值，意义在于每个用户故事之间的大小比较。在敏捷开发中常用的故事点估算方式是敏捷估算扑克方法，其主要步骤为：

（1）分牌，给每个参会人员分发估算扑克牌，包含分数为0，1，2，3，5，8，13，21，34，8;（2）定基准，产品经理拿出一份用户故事清单，并共同选出一个故事点为3的用户故事，达成共识；（3）提问讲解，由产品负责人进行讲解下一个需要估算的用户故事，参会人员可以提问了解；（4）放牌，团队成员根据基准选择一张自认为与该用户故事所需工作量一致的扑克牌，并不能给彼此看到；（5）翻牌，团队成员同时明牌，如果有不同的故事点则进行讨论，直到确定。

敏捷估算扑克方法遵循的原则为：

（1）估算结果相近，则取平均值；（2）估算结果差距较大，则两端分数的成员进行观点阐述，沟通后进行再次投票；（3）同个用户故事最多进行3轮估算，若不成功由产品负责人最终确定。

虽然敏捷估算扑克方法十分有效，但每一轮进行的时间过于冗长，对用户故事较多的项目不太友好。特此又引入了亲和估算。一般软件开发中，很多用户故事都十分相似，我们只需要快速先对用户故事分类，再抽选一部分分类中的用户故事进行故事点估算，那就会事半功倍。所以改良后的故事点估算步骤为：

（1）定基准，按照较小，小，中，大，极大，过大以及需解释分类，团队成员共同从用户故事列表中挑出各一条满足分类的用户故事；（2）分类，分发所有用户故事卡片给每个团队成员，直接进行分类；（3）调整，团队成员共同对上一步分类的用户故事位置进行评审并达成共识；

（4）讨论，当产品负责人和团队成员的估值相差一个故事点时，对该用户故事进行讨论，而团队成员最终确认；

（5）敏捷估算扑克，对过大和需解释的用户故事用上述的敏捷估算扑克方 式进行估算。

其中分类的故事点就对应斐波那契表，较小为1个点，小为2个点，中为3个点，大为5个点，极大为8个点。

此种工时估算方法不仅让团队成员加强沟通，摒弃原来自行阅读需求文档导致的理解片面性，还能从不同工作岗位角度来准确衡量工期，从源头遏制了进度延期问题。

4）迭代需求执行。

上个阶段已经明确了迭代的目标，这个阶段就进入了迭代冲刺。迭代周期一般是2-4周，相较于长的冲刺周期，短的可以更便于掌控，如果整个项目作为一个冲刺周期，就和传统的瀑布模式无异了。但过于短的冲刺周期也没有项目的意义，就仿佛还没开始加速就已经结束。迭代周期确定后，整个执行阶段就是团队的全力冲刺阶段。每个迭代周期都由每日站会、开发、测试、迭代评审会以及发布组成。每日站会，是迭代周期中每天必备的会议。每日站会可以使团队成员及时交流，快速寻求解决方案，得到帮助。依据Scrum敏捷开发方法和A公司实际情况，制定如下原则：

（1）固定时间、地点；  
（2）全员参与，人均开口；  
（3）话题围绕昨日已完成，今日欲完成以及需要的帮助；  
（4）设立奖惩。

由于迭代冲刺过程中，先前先开发再测试的流程会导致开发阶段测试人员空闲，测试阶段开发人员空闲，为改变这一状况A公司决定引入自动化测试。测试人员可以和开发人员同步进行自动化测试工作，以防止在开发的进行中测试人员有过多的资源空闲。迭代评审会议，会邀请项目干系人一起参加，主要是用于总结和展示，针对本次迭代中的情况进行汇总，并向项目干系人进行成果增量的展示。对于没有完成的任务，需要调整安排到下一个迭代冲刺任务表中。迭代评审会议可以根据环境变化及时评估影响，进行调整。迭代评审会不是一个单纯展示的会议，目的是为了获取反馈并促进及时调整。

5）迭代回顾会召开。

迭代回顾会议安排在每个迭代结束前，由敏捷教练安排召开。其目的就是思考如何提高质量和时间。一个迭代冲刺周期完成的当天下午，只有项目组成员参加。会上每个成员都需要针对周期中各种完成情况以及分享遇到的解决或未解决的问题，为下一次迭代储能。

以上3）至5）是一个迭代周期，并不同于传统瀑布模式下的意义，需要重复进行，不断迭代冲刺，直到完成任务。

# 4.3基于 Scrum 的组织结构改进

面对瞬息万变的市场，面对频繁变化的需求，传统的职能式的组织职能结构已经无法适应。一个固定的组织结构往往由于层层传递，具有较慢的响应速度，根本无法满足业务需求的多变特性。在A公司原先的职能式的组织结构，实际的开发人员和需求人员是两个部门，存在部门壁垒。而且原先的组织机构虽然集权，但丧失活力，缺乏主观能动性，缺少效率，基本不会主动响应需求的变化。而Scrum敏捷开发方法的宗旨就是打破职能束缚，达成全员参与。敏捷团队相较于职能式部门有更强的行动力，它是由跨职能成员形成的团队，全队都需要对整个项目进行负责，所以能更快地对需求变化进行响应，并且整个团队有一致的目标。敏捷组织中的领导者决定了目标和战略，为团队成员赋能。根据第二章阐述的理论，Scrum敏捷开发模型中有至关重要的三种角色，包括产品负责人、敏捷教练以及敏捷团队。在敏捷开发中每一次小迭代都能得到及时反馈以及得到可发布的增量产品。敏捷教练主要是负责确保 Scrum敏捷开发被理解以及成功实施，帮助整个团队遵循 Scrum相关理论以及实践，通过改变团队内外向有益的交互方向奔去，以创造敏捷团队最大的价值。所以敏捷教练作为Scrum理论的贯彻监督者，对整个项目的敏捷化实施方向，显得尤为重要。对于A公司而言敏捷教练这个角色，可谓是一角难求，之前A公司并未有过任何Scrum敏捷开发经验，所以有一个经验丰富的敏捷教练将对整个敏捷化行动的成功起决定性作用。由于公司内并未匹配到相关人员，因此通过外聘方式引入。敏捷教练是种服务型的角色，不仅服务于产品负责人还有敏捷团队等相关组织，组织每日站会和各类评审会，及时纠正团队开发过程中的问题，按需推动每个冲刺任务。产品负责人作为整个团队的核心，对产品待办事项列表负有绝对责任。作为一个承上启下的角色，产品负责人对外需考虑需求方的诉求，挖掘客户的真实需求，能及时根据诉求调整待办事项的优先级。对于软件开发而言，需求的挖掘和理解就像是整个工程的基石，唯有基石堆积在了正确的地方，整个主体才不会越建越错。产品负责人对内肩负了帮助内部人员理解需求以及把控住整个方向。在A公司，原有的部门经理不仅通过Scrum联盟认证的CSPO（Certified ScrumProductOwner），而且从职责范围来看，就非常适合担任产品负责人一职。敏捷团队为了保持其敏捷性，其最佳规模3到9人之间，不宜过大，过大的组织会额外消耗不必要的沟通成本。A公司由原先的3位软件开发以及1位软件测试人员组成。如下图4.2为改进后的组织架构。

![](images/d623bbd57046c6bd9bf9773690e27c8c6c3b71b73d56b54637927b5f9598456f.jpg)  
图4.2改进后的组织架构

# 4.4基于 Scrum 的需求分析改进

在业务数字平台项目一期中较严重的问题是需求变更频繁和需求优先级不明确。软件项目的顺利执行倚仗于需求说明文档。但传统的需求说明文档，需求变更文档都过于严苛死板，面对频繁变更的需求，难以较快响应。而 Scrum敏捷开发方法核心就是频繁交付，每个迭代都能及时考虑新的需求变化。每一次迭代计划会中就能动态同时源自Scrum敏捷开发方法中的产品需求列表，也是需求管理的好工具。产品待办列表是产品负责人按照优先级排序的一系列用户故事，但其优先级的制定方式往往带有很强烈的个人意见，并没有什么科学工具的辅佐。为进一步优化需求优先级评定，以便在下阶段的迭代计划会上提出优先级高的用户故事进入迭代以及及时对客户的变更需求优先级进行评价加入产品需求列表，于是在A公司产品需求列表的优先级制定中引入了加权决策矩阵。具体操作步骤如下：

（1）列出所有用户故事。

（2）确定影响标准。根据业务数字平台项目的实际情况，公司管理层和项目团队进行了头脑风暴，提出影响因素分别为业务价值、客户影响、风险、需求复杂度以及关联度。其中业务价值指的是市场抢占率等。客户影响指频率高涉众广的。风险越高优先级越高，防止在项目后期造成总体风险的增加。需求复杂度越高也越应该在前期完成，可以及时寻求帮助或者降低用户需求。工作项之间的关联度越高优先级应该越高。

（3）给影响标准评分。按照1-5分代表影响从小到大。从业务价值、客户影响、风险、需求复杂度以及关联度分别给与4分、5分、4分、2分和3分。

（4）基于每个标准给不同用户故事打分，同样按照1-5分代表影响从低到高。

（5）计算权重得分。把每个用户故事的评分乘以标准的权重。

（6）计算总分。

（7）做出决策。按照总分高低排列产品需求列表的优先级。

# 4.5基于 Scrum 的进度控制改进

为了控制项目进度，不仅需要从以上几个方面进行改进，还需要制定基于Scrum敏捷开发方法下项目开发的进度计划以及监控。Scrum敏捷开发模式有别于之前的瀑布开发模式，并不是以计划驱动开发的，是以迭代为主。虽然迭代通过一个固定的短周期控制完成具体的目标让整个项目的整体时间可控，但每个小迭代的成败足以颠覆整个项目。所以每个小迭代的进度计划和监控显得尤为关键。

由于Scrum敏捷开发方法在这块内容中有所欠缺，特引入关键链法。关键链法是基于约束理论在项目进度管理中的优秀应用。关键链法的关键之处在于识别关键链和设置缓冲区。

所谓关键链，是基于各种资源约束的考量，而选出来的项目中那条历时最长的任务链。控制住最长的任务链也就能控制住整个项目的进度。本文前文提及的基于Scrum敏捷开发方法的开发流程，意味着将一整个软件项目分割成了多个子项目进行迭代开发。而将关键链应用于每次迭代中，而不同于往常瀑布开发模式应用于整个项目中，相比较由于迭代中的任务量远小于整个项目的任务量，也就能进行进度控制的同时，也便于关键链的识别和控制。识别关键链和非关键链是NP类问题，这边采用常用的启发式算法。整个算法的步骤如下：

第一步：生成调度计划。

（1）为迭代中的各项目任务划分状态，分别划分为四个状态集{ $\{ \mathrm { A } =$ 候选子集}、 $\{ \mathrm { B } =$ 执行子集}、 $\{ { \bf C } = \{$ 完成子集}以及 $\{ \mathrm { D } = \{ \ l _ { \mathrm { ~ \infty ~ } }$ 未执行子集 $\}$ 之一，项目中的各任务用 $T _ { i }$ ， $i { = } l , { \ldots } , n$ 表示,任务的紧后任务集为 $S _ { i }$

（2）活动初始状态， $T _ { 1 } { \in } \mathrm { A }$ ，其他 $T _ { i } \in \mathrm { D }$ ， $i { = } 2 , { \ldots } , n$ （3）第一次循环， $T _ { 1 } { \in } \mathrm { B }$ ， $S _ { 1 } { \in } \mathbf { A }$ 其他 $\in \mathrm { D }$

（4）第二次循环， $T _ { 1 } { \in } \mathrm { C }$ ，根据目前公认的较好的优先规则一一后序工作机动时间（LFS）： $m i n ( L S _ { j } - E S _ { j } )$ ，其中LSj表示任务 $j$ 的最晚开始时间，ESj表示任务 $j$ 的最早开始时间，从 $A$ 子集中选择符合条件的任务进入 $B$ 子集，再根据紧前关系把符合条件的任务从 $D$ 子集选入 $A$ 子集；

（5）重复循环（4）步骤，直到所有任务都进入 $C$ 子集。

第二步：基于生成的调度计划，在保持迭代总历时不变和满足资源约束以及紧前关系条件下，对计划中的任务进行后移，直到不能移动，生成新的调度计划。

（1）从最后第二个任务 $T _ { n - 1 }$ 倒推检查各个任务能否后移，不能后移的标记为4，可以的标记为3；

（2）对于标记为3的进行资源约束检查，对于满足条件的可以移动的记Y，不能的记N;

（3）对于记Y可以移动的任务重新计算开始时间；

（4）循环（1）－（3）过程，直到所有任务无法再后移。

第三步：输出关键链。

（1）关键链其实就是第二步中无法后移的任务组成；

（2）从 $T _ { 1 }$ 到 $T _ { n }$ 之间按照紧前关系按时间顺序排序，形成一条关键链；

（3）多次循环，直到属于关键链上的任务都找到自己的链路；

第四步：识别非关键链。

（1）计算关键链数量，如果数量大于1，则按照TOC思想选取其中一条，剩余的作为非关键链处理，并在其后设置汇入缓冲，如果影响到关键链的时间，则跳到（2）；

（2）从非关键链的第一个任务开始按照紧后关系查找不在关键链上的任务；

（3）循环（2），直到形成一条非关键链；

（4）返回（2），直到所有属于非关键链上的任务都能找到属于它的非关键链。

进度计划中关键链的长度可以和固定的迭代周期进行对比，从而及时对任务的优先级进行改变以及对迭代中的任务进行删减或是增加。

在进度监控方面，A公司一直缺乏进度监控工具，需要引入缓冲区监控以及 Scrum中燃尽图方法。

业务数字平台项目一期的实际工期远大于预估工期，除上文解释的开发流程和需求管理的不到位，发现预估工期过于乐观，面对项目的大量不确定性，常人根本无法在预估工期内完工。因为项目存在大量不确定性，且业务数字平台项目严苛的交付时间，考虑到接下来采用的开发流程是通过频繁迭代进行，所以团队需要为不确定性增加缓冲区域。进度缓冲区是比较常见的缓冲区，这个主要是在进度表中建立一定时间的缓冲。A公司从历史数据和项目完工时间紧张两方面考量，设置的估算进度缓冲区大小是按照每个用户故事的每对 $5 0 \%$ 和 $9 0 \%$ 的估算值的平方和的平方根。

A公司的业务数字平台项目一期以及前期项目，应用的都是瀑布开发模式，其进度管理工具用的是甘特图。甘特图主要通过任务项、时间轴以及开始时间和结束时间来监控项目。但一旦项目中关系过于繁杂，甘特图就不具备优良的阅读性。A公司在新开发流程下，将带入新的进度监控工具一一燃尽图。燃尽图顾名思义，表示的是剩余工作量的图。燃尽图通过X轴和Y轴组成，分别用X轴表示时间，Y轴表示工作量，比较直观地表现了迭代冲刺过程中还剩余多少故事点，直观地预测工作完成的时间。燃尽图的大概形态如下图4.3所示。

![](images/893aa51c5d6fa8aa3ce0aed43aac4fed2158dff3552c9e942713666b0d273983.jpg)  
图4.3燃尽图示例

通过图标中计划曲线和实际曲线的形态，可以一目了然此时的进度状态。燃

# 尽图解读规则如下：

（1）实际曲线低于计划曲线，则有较大几率能按时完工；  
（2）实际曲线高于计划曲线，则由较大几率进度延期。

# 4.6本章小结

根据第三章总结得出的A公司业务数字平台项目一期进度延期的主要问题，本章借助Scrum敏捷开发方法，并充分考虑A公司的现状，进行了A公司进度管理方案的改进。

根据A公司业务数字平台项目的特点，基于Scrum敏捷开发方法，建立了适应A公司特点的开发流程，为进度管理提供了良好基础。

同时通过组织结构的敏捷化调整和加权决策矩阵、进度缓冲区概念和燃尽图的引入，从组织结构、需求分析以及进度监控各方面进一步加强项目进度的管理。

# 第5章PVoC业务数字平台项目二期进度管理实践

根据第四章中为PVoC业务数字平台项目改进的进度管理方案，通过应用该进度方案在业务数字平台项目二期中进行实践，来验证该方案是否存在效果。

# 5.1项目简介

PVoC业务数字平台项目是为数不多的A公司面向外部需求的项目，所以对项目交付时间的要求也较为苛刻。PVoC业务数字平台是对业务数字化的构建，不仅需要建立全流程的业务管理系统，还需要与各国标准局建立数据对接并进行数据收集分析展现，其需求注定繁杂多变。由于项目一期的进度延期问题严峻，集团和标准局下放了压力，A公司领导相当重视，希望项目二期能保时保质完成。PVoC业务数字平台项目二期作为项目一期的功能扩展，其工作量基本与项目一期等同，在同等干系人以及开发团队的条件下，也便于后期数据的统计对比。根据第四章在开发流程、组织结构、需求管理以及进度监控上的敏捷化调整，下文将运用改进方案进行项目二期的实施，同时检验敏捷化调整的成果。

# 5.2项目进度计划

在进度管理中项目进度计划是整个进度得以管理的基础。而需求分析以及需求优先级的确认是制定一个合理的进度计划的前提。

# 5.2.1 需求分析

根据第四章4.2节建立的开发流程可知，需求范围的确定主要是由产品负责人确认产品需求列表，以及对列表中用户故事优先级排序，为下一阶段进行准备。产品负责人对业务数字平台项目二期涉及的各方需求和期望进行了全面收集，并先对其业务流程进行了梳理，项目二期的业务流程图如下图5.1所示。

![](images/27107be14bf84d5bb0cef87dd2385505d5f835dbd515857478695927eee3933c.jpg)  
图5.1业务数字平台项目二期业务流程表

由业务流程图所知，项目二期涉及了业务受理、派单分包、出证发证、计费收费以及电子证书五块内容。由于整个项目涉及的需求繁多，本章以其中的业务受理部分为例，来展示部分产品需求列表，按照4.2描述的“史诗-用户故事”来定义产品需求列表，其中重要性的分值按照的是4.4节描述的加权决策矩阵的总分，初始估值是产品负责人对用户故事的大概经验估值，其中业务数字平台项目二期的产品需求列表部分内容如表5.2所示。

表5.2业务数字平台项目二期产品需求列表（部分）  

<table><tr><td>序号</td><td>史诗</td><td>重要性</td><td>初始估值</td><td>用户故事</td><td>验收标准</td></tr><tr><td>A1</td><td>搜索业</td><td>26</td><td>1</td><td>作为用户，我可以通过</td><td>在页面搜索区域</td></tr><tr><td></td><td>务委托</td><td></td><td></td><td>业务委托号进行搜索，</td><td>输入委托号，点</td></tr><tr><td></td><td>单</td><td></td><td></td><td>以便我可以通过委托号击【搜索】进行</td><td></td></tr><tr><td></td><td></td><td></td><td></td><td>查看委托单详情。</td><td>搜索，列表显示</td></tr><tr><td></td><td></td><td></td><td></td><td></td><td>符合条件的对应</td></tr><tr><td></td><td></td><td></td><td></td><td></td><td>委托单</td></tr><tr><td>A2</td><td>搜索业</td><td>26</td><td>1</td><td>作为用户，我可以通过在页面搜索区域</td><td></td></tr><tr><td></td><td>务委托</td><td></td><td></td><td>委托日期进行搜索，以输入委托日期，</td><td></td></tr><tr><td></td><td>单</td><td></td><td></td><td>便我可以通过查看固定</td><td>点击【搜索】进</td></tr><tr><td></td><td></td><td></td><td></td><td>时间内申请的委托单。</td><td>行搜索，列表显</td></tr><tr><td></td><td></td><td></td><td></td><td></td><td>示符合条件的对</td></tr><tr><td></td><td></td><td></td><td></td><td></td><td>应委托单</td></tr><tr><td></td><td>A3新建委</td><td>90</td><td>6</td><td>作为用户，我可以在线填写内容，点击</td><td></td></tr><tr><td></td><td>托单</td><td></td><td></td><td>填写委托信息，以便我【保存】，页面</td><td></td></tr><tr><td>A4</td><td></td><td></td><td></td><td>可以进行业务委托。</td><td>显示保存成功</td></tr><tr><td></td><td>暂存委</td><td>27</td><td>2</td><td>作为用户，我可以在线填写内容，点击</td><td></td></tr><tr><td></td><td>托单</td><td></td><td></td><td>填写委托单后暂存不提</td><td>【暂存】，页面</td></tr><tr><td></td><td></td><td></td><td></td><td>交，以便我以后再申</td><td>显示暂存成功</td></tr><tr><td>A5修改委</td><td></td><td></td><td></td><td>请。</td><td></td></tr><tr><td></td><td></td><td>60</td><td>2</td><td>作为用户，我可以在线点击【修改】，</td><td></td></tr><tr><td></td><td>托单</td><td></td><td></td><td>对自己的委托单进行修弹出页面详情，</td><td></td></tr><tr><td></td><td></td><td></td><td></td><td>改，以便修正错误。</td><td>点击【保存】， 页面提示修改成</td></tr><tr><td></td><td></td><td></td><td></td><td></td><td>功</td></tr><tr><td>A6导出委</td><td></td><td>39</td><td>5</td><td>作为用户，我可以在详详情页点击【导</td><td></td></tr><tr><td></td><td>托详情</td><td></td><td></td><td>情页导出委托单详情，出】，可以导出</td><td></td></tr><tr><td></td><td>单</td><td></td><td></td><td>以便留底</td><td>正确内容</td></tr><tr><td>A7</td><td>查看用</td><td>25</td><td>3</td><td>作为管理员，我可以看</td><td>有委托单列表显</td></tr><tr><td></td><td>户的委</td><td></td><td></td><td>到用户申请的委托单列</td><td>示</td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td>托单</td><td></td><td></td><td>表，以便进行下一步审</td><td></td></tr></table>

# 5.2.2需求优先级

针对业务数字平台项目的需求频繁变化特性，产品负责人需要严格把控产品待办列表中的优先级排序。在产品负责人进行产品需求列表的优先级制定中引入了加权决策矩阵。产品需求列表的优先级关乎到每一次迭代任务是否能让干系人满意。同样以业务受理模块为例，进行加权决策矩阵进行优先级确认。根据第四章指导的步骤，其中权重按照第四章中列出的标准，总分按照用户故事评分乘以对应权重得出的总和（如A1的总分 $= 2 ^ { * } 4 + 2 ^ { * } 5 + 1 ^ { * } 4 + 1 ^ { * } 2 + 1 ^ { * } 3 = 2 7 )$ ，最终列出如下表5.3加权决策矩阵表。

表5.3业务数字平台项目二期业务受理部分优先级矩阵  

<table><tr><td></td><td>业务价值</td><td>客户影响</td><td>风险</td><td>需求复杂度</td><td>关联度</td><td rowspan="2">总分</td></tr><tr><td>权重</td><td>4</td><td>5</td><td>4</td><td>2</td><td>3</td></tr><tr><td>A1</td><td>1</td><td>1</td><td>2</td><td>3</td><td>1</td><td>26</td></tr><tr><td>A2</td><td>1</td><td>1</td><td>2</td><td>3</td><td>1</td><td>26</td></tr><tr><td>A3</td><td>5</td><td>5</td><td>5</td><td>5</td><td>5</td><td>90</td></tr><tr><td>A4</td><td>2</td><td>2</td><td>1</td><td>1</td><td>1</td><td>27</td></tr><tr><td>A5</td><td>4</td><td>5</td><td>2</td><td>4</td><td>1</td><td>60</td></tr><tr><td>A6</td><td>1</td><td>4</td><td>2</td><td>2</td><td>1</td><td>39</td></tr><tr><td>A7</td><td>1</td><td>1</td><td>2</td><td>2</td><td>1</td><td>25</td></tr></table>

上表总分高低即优先级高低，所以优先级从高到低分别是A3新建委托单、A5修改委托单、A6导出委托详情单、A4暂存委托单、A1、A2搜索委托单以及A7查看用户委托单。根据优先级高低从而确定每次迭代所需要完成的活动清单，是进度计划制定和执行非常重要的一步。

# 5.2.3迭代计划

随着产品负责人完善产品需求列表，随之就迎来了迭代周期。在实施的过程中，迭代周期的确立也至关重要。如果不固定迭代周期会存在很多弊端，包括产品迭代周期混乱导致产品交付日期不能确定，以及团队无法准确估算自己的开发速度造成估量容易出现偏差。PVoC业务数字平台项目二期的迭代周期是在进行3次迭代之后，固定下来为2周。因为此时团队已经了解在一个迭代能够完成故事点数，同时也清楚迭代速率，无论是迭代的进度，还是最后产品交付周期，团队都可以了然于胸。迭代开始的第一天上午，就召开了迭代计划会。第一次迭代计划会产品负责人、敏捷教练和敏捷团队会和相关项目干系人共同参与。产品负责人是整个会议的发起者，并向团队介绍自己整理完成的产品待办列表，并和团队共同讨论其中暂定的优先级是否合理。以上节业务受理模块为例，计划会中团队成员提出A7用户故事的优先级需要高于A1和A2，因为A1、A2的功能点需要放在A7的基础上，A7不先进入迭代的话A1和 A2也无法进行开发。产品负责人立马修改产品需求列表进行调节。迭代计划会不仅仅是产品负责人的讲解，更是确保需求的再次完善和优先级的确立。在会上同时需要确认接下来的迭代周期内需要完成的用户故事以及进行任务估算。任务估算运用的就是第四章提及的敏捷估算扑克，由于方法简单又高效，就不做赘述。项目团队会主动认领相关的高优先级用户故事，但有时候，有些用户故事可能会无人认领，此时作为软件部门经理的产品负责人就会根据实际情况做出自己的判断，对该任务进行分配。

# 5.3项目进度计划实施

# 5.3.1团队管理

根据第四章4.3组织结构敏捷化调整的说明。A公司在业务数字平台项目二期中打破原来的职能式组织结构的束缚，跨职能召集了一组团队，其中包括原有的信息技术部门的项目经理、开发人员3名以及集团开发人员1名。整个团队按照Scrum角色分工为产品负责人1人，敏捷教练1人，敏捷团队4人。由于部门经理拥有敏捷开发产品负责人认证以及原先的需求调研就是由他完成，所以产品负责人由他担任。敏捷教练需要指导整个开发流程敏捷化方向，专业性极高，所以进行外聘。由于集团开发人员对业务较为熟悉，所以也加入了敏捷团队。由于整个项目团队的成员之前只是稍听闻过敏捷开发的相关知识，但心里上并不排斥敏捷，有强烈的学习意愿。为了防止团队对新的开发流程不熟悉而导致最终进度管理的失败，公司邀请产品负责人对项目团队进行全员培训。由于项目需求方不仅包括跨市的集团公司业务部门还涉及跨国的非洲地区成员，为了保证对需求的及时响应，A公司决定赴集团所在办公大楼，组建一个“突击室”，并邀请一位集团的业务专家进入“突击室”，同时邀请一位跨国业务专家进行远程指导。全体项目成员在“突击室”集体办公，保证得以随时交流，增强项目团队的凝聚力。

# 5.3.2迭代进度跟踪

计划会上认领的用户故事需要立马消化理解展开迭代冲刺。用户故事转化成任务进行执行的过程依然沿用以前的模式，包括需求理解、界面设计、测试用例编写、编码以及单元测试。但不同于之前的直线模式，业务数字平台项目二期执行过程中，开发完成代码任务后会及时跟产品负责人和测试人员交流，做到是否需求理解一致。在项目二期执行过程中也减少了很多传统过程文档，把时间用在了真正对业务有利的地方。

迭代周期内，每天早上进行每日站会。根据第四章开发流程中的每日站会原则，敏捷教练把每日站会时间定在每天早上9：30-9：45之间，需要全体开发成员共同站立参加。团队成员对照着看板中最靠近部署点的用户故事，逐个阐述遇到的问题，昨天的工作和今天的预估工作。其中看板模式其实市面上有很多软件进行支持，但从显示屏上显示的平面化数据不够具有冲击性，A公司还是采用原始的白板模式，在实体白板上，划分出未开始、正在设计、正在开发、正在测试和已经完成区域，并把写有任务和负责人名字的便签贴分布上面，不仅有撕贴的操作感，还使团队更有积极性。看板的具体实现样式如下表5.4所示。每日站会为了效率，不会对具体技术问题进行过多讨论，通过团队成员交流达成信息同步和进度监控。在业务数字平台项目二期的每日站会中，曾遇到测试写自动化测试遇到问题，另一个有经验的开发立马承诺能帮忙解决，并在会后帮助解决。每日站会让团队间关系更加紧密，团队更有凝聚力，也能尽早发现开发过程中存在的问题，尽快与相关人员讨论解决。

表5.4业务数字平台项目二期看板示意图  

<table><tr><td>未开始</td><td>正在设计</td><td>正在开发</td><td>正在测试</td><td>已经完成</td></tr><tr><td>T1 XXXX 开发1号</td><td>T9 XXXX 开发4号</td><td>T2 XxXX 开发1号 T4</td><td>T3 XXXX 开发2号 T6</td><td>T8 XXXX 开发3号</td></tr></table>

# 5.3.3迭代评审回顾

在迭代周期2周的末尾，会召开迭代回顾会。回顾会上的任务也很明确，汇总该次迭代过程中产生的问题，根据需求和开发的实际情况对产品待办列表进行修改更新。在业务数字平台项目二期上，没有单独设立评审和回顾会议，而是将其合并，就叫做迭代评审回顾会议。经过两次回顾会的召开后，吸取了相关经验。由于开发流程提倡的是敏捷开发，也不能在会议上浪费过多时间。整个迭代评审会议控制在2小时内，在会议的前期就会准备好可以展示的软件版本，方便节省会议时间，在会议上可以立马进行展示。全体团队成员会对迭代中未完成的任务进行提问纠错，进行总结。整个会议的主要目的就是使项目干系人能迅速了解目前项目的实际开发情况，能及时把控进度，并增强团队的及时交付信心。会议上产品负责人会针对该次迭代中存在的问题及时进行调整，防止问题蔓延，影响下一次迭代。

业务数字平台项目二期总体而言顺畅很多，由其中经历了12次迭代，完成了最终版本的交付。

# 5.4项目进度控制

本项目由于采用了敏捷开发模式，把整个项目分割成十几个小迭代，其中的进度管理就需要渗透到每个迭代中，不仅需要根据加权决策矩阵对需求进行优先级区分，以划分每次迭代所需完成的活动清单，更需要严格控制每次迭代是否能准时完成相应的所有活动。以第一次迭代为例，在迭代计划会上，根据团队的讨论和耗时估算，形成了一张任务清单，如下表5.5迭代一任务列表所示。

表5.5迭代一任务列表  

<table><tr><td>序号</td><td>任务</td><td>估算时间 （小时）</td><td>开发 （人数）</td><td>测试</td><td>紧前优先级</td><td></td></tr><tr><td>1</td><td>迭代计划会议</td><td>4</td><td>2</td><td>（人数） 2</td><td>活动 无</td><td>P1</td></tr><tr><td>2</td><td>测试用例编写</td><td>32</td><td></td><td>2</td><td>1</td><td>P1</td></tr><tr><td>3</td><td>测试用例评审</td><td>8</td><td>1</td><td>2</td><td>2</td><td>P1</td></tr><tr><td>4</td><td>业务委托单新建服务端开发</td><td>32</td><td>1</td><td></td><td>1</td><td>P2</td></tr><tr><td>5</td><td>业务委托单新建服务端测试</td><td>8</td><td></td><td>1</td><td>3，4</td><td>P2</td></tr><tr><td>6</td><td>业务委托单新建UI设计</td><td>8</td><td>1</td><td></td><td>1</td><td>P2</td></tr><tr><td>7</td><td>业务委托单新建客户端开发</td><td>32</td><td>1</td><td></td><td>6</td><td>P2</td></tr><tr><td>8</td><td>业务委托单新建客户端测试</td><td>8</td><td></td><td>1</td><td>5，7</td><td>P2</td></tr><tr><td>9</td><td>业务委托单修改服务端开发</td><td>16</td><td>1</td><td></td><td>1</td><td>P2</td></tr><tr><td>10</td><td>业务委托单修改服务端测试</td><td>4</td><td></td><td>1</td><td>3,9</td><td>P2</td></tr><tr><td>11</td><td>业务委托单修改UI设计</td><td>8</td><td>1</td><td></td><td>1</td><td>P2</td></tr><tr><td>12</td><td>业务委托单修改客户端开发</td><td>16</td><td>1</td><td></td><td>11</td><td>P2</td></tr><tr><td>13</td><td>业务委托单修改客户端测试</td><td>4</td><td></td><td>1</td><td>10，12</td><td>P2</td></tr><tr><td>14</td><td>业务委托单列表服务端开发</td><td>16</td><td>1</td><td></td><td>1</td><td>P2</td></tr><tr><td>15</td><td>业务委托单列表服务端测试</td><td>4</td><td></td><td>1</td><td>3，14</td><td>P2</td></tr><tr><td>16</td><td>业务委托单列表UI设计</td><td>8</td><td>1</td><td></td><td>1</td><td>P2</td></tr><tr><td>17</td><td>业务委托单列表客户端开发</td><td>16</td><td>1</td><td></td><td>16</td><td>P2</td></tr><tr><td>18</td><td>业务委托单列表客户端测试</td><td>4</td><td></td><td>1</td><td>15,</td><td>P2</td></tr><tr><td></td><td></td><td></td><td></td><td></td><td>17</td><td></td></tr><tr><td>19</td><td>业务委托单导出服务端开发</td><td>8</td><td>1</td><td></td><td>1</td><td>P2</td></tr><tr><td>20</td><td>业务委托单导出服务端测试</td><td>2</td><td></td><td>1</td><td>3，</td><td>P2</td></tr><tr><td></td><td></td><td></td><td></td><td></td><td>19</td><td></td></tr></table>

续表5.5迭代一任务列表  

<table><tr><td>序号</td><td>任务</td><td>估算时间 （小时）</td><td>开发 （人数）</td><td>测试 （人数）</td><td>活动</td><td>紧前优先级</td></tr><tr><td>21</td><td>业务委托单导出UI设计</td><td>4</td><td>1</td><td></td><td>1</td><td>P2</td></tr><tr><td>22</td><td>业务委托单导出客户端开发</td><td>8</td><td>1</td><td></td><td>21</td><td>P2</td></tr><tr><td>23</td><td>业务委托单导出客户端测试</td><td>2</td><td></td><td>1</td><td>20,</td><td>P2</td></tr><tr><td></td><td></td><td></td><td></td><td></td><td>22</td><td></td></tr><tr><td>24</td><td>迭代评审会</td><td>4</td><td>2</td><td>2</td><td>8,</td><td>P1</td></tr><tr><td></td><td></td><td></td><td></td><td></td><td>13,</td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td><td>18,</td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td><td>23</td><td></td></tr></table>

根据4.5章中的算法，从以上的任务列表识别关键链和非关键链。第一步：生成调度计划。最终得到如下表5.6迭代一调度计划表。

表5.6迭代一调度计划表  

<table><tr><td>序号</td><td></td><td></td><td>任务持续时间任务开始时间任务结束时间</td><td>后置活动</td></tr><tr><td>1</td><td>4</td><td>0</td><td>4</td><td>2，4，6，9，11，14，16, 19，21</td></tr><tr><td>2</td><td>32</td><td>4</td><td>36</td><td>3</td></tr><tr><td>3</td><td>8</td><td>36</td><td>44</td><td>5，10，15，20</td></tr><tr><td>4</td><td>32</td><td>4</td><td>36</td><td>5</td></tr><tr><td>5</td><td>8</td><td>44</td><td>52</td><td>8</td></tr><tr><td>6</td><td>8</td><td>4</td><td>12</td><td>7</td></tr><tr><td>7</td><td>32</td><td>12</td><td>44</td><td>8</td></tr><tr><td>8</td><td>8</td><td>52</td><td>60</td><td>24</td></tr><tr><td>9</td><td>16</td><td>4</td><td>20</td><td>10</td></tr><tr><td>10</td><td>4</td><td>44</td><td>48</td><td>13</td></tr><tr><td>11</td><td>8</td><td>4</td><td>12</td><td>12</td></tr><tr><td>12</td><td>16</td><td>12</td><td>28</td><td>13</td></tr><tr><td>13</td><td>4</td><td>48</td><td>52</td><td>24</td></tr><tr><td>14</td><td>16</td><td>4</td><td>20</td><td>15</td></tr><tr><td>15</td><td>4</td><td>44</td><td>48</td><td>18</td></tr><tr><td>16</td><td>8</td><td>4</td><td>12</td><td>17</td></tr><tr><td>17</td><td>16</td><td>12</td><td>28</td><td>18</td></tr><tr><td>18</td><td>4</td><td>48</td><td>52</td><td>24</td></tr><tr><td>19</td><td>8</td><td>4</td><td>12</td><td>20</td></tr></table>

续表5.6迭代一调度计划表  

<table><tr><td>序号</td><td></td><td>任务持续时间任务开始时间</td><td>任务结束时间</td><td>后置活动</td></tr><tr><td>20</td><td>2</td><td>44</td><td>46</td><td>23</td></tr><tr><td>21</td><td>4</td><td>4</td><td>8</td><td>22</td></tr><tr><td>22</td><td>8</td><td>8</td><td>16</td><td>23</td></tr><tr><td>23</td><td>2</td><td>46</td><td>48</td><td>24</td></tr><tr><td>24</td><td>4</td><td>60</td><td>64</td><td></td></tr></table>

第二步：生成后移后的调度计划。最终得到如下表5.7后移后迭代一调度计划表。

表5.7后移后迭代一调度计划表  

<table><tr><td>序号</td><td></td><td>任务持续时间任务开始时间任务结束时间</td><td></td><td>后置活动</td><td>是否后移</td></tr><tr><td>1</td><td>4</td><td>0</td><td>4</td><td>2，4，6，9,</td><td>N</td></tr><tr><td></td><td></td><td></td><td></td><td>11，14，16, 19，21</td><td></td></tr><tr><td>2</td><td>32</td><td>4</td><td>36</td><td>3</td><td>N</td></tr><tr><td>3</td><td>8</td><td>36</td><td>44</td><td>5，10，15，20</td><td>N</td></tr><tr><td>4</td><td>32</td><td>12</td><td>44</td><td>5</td><td>Y</td></tr><tr><td>5</td><td>8</td><td>44</td><td>52</td><td>8</td><td>N</td></tr><tr><td>6號</td><td>8</td><td>4</td><td>12</td><td>7</td><td>N</td></tr><tr><td>7</td><td>32</td><td>12</td><td>44</td><td>8</td><td>N</td></tr><tr><td>8</td><td>8</td><td>52</td><td>60</td><td>24</td><td>N</td></tr><tr><td>9</td><td>16</td><td>36</td><td>52</td><td>10</td><td>Y</td></tr><tr><td>10</td><td>4</td><td>52</td><td>56</td><td>13</td><td>Y</td></tr><tr><td>11</td><td>8</td><td>12</td><td>20</td><td>12</td><td>Y</td></tr><tr><td>12</td><td>16</td><td>20</td><td>36</td><td>13</td><td>Y</td></tr><tr><td>13</td><td>4</td><td>56</td><td>60</td><td>24</td><td>Y</td></tr><tr><td>14</td><td>16</td><td>36</td><td>52</td><td>15</td><td>Y</td></tr><tr><td>15</td><td>4</td><td>52</td><td>56</td><td>18</td><td>Y</td></tr><tr><td>16</td><td>8</td><td>12</td><td>20</td><td>17</td><td>Y</td></tr><tr><td>17</td><td>16</td><td>20</td><td>36</td><td>18</td><td>Y</td></tr><tr><td>18</td><td>4</td><td>56</td><td>60</td><td>24</td><td>Y</td></tr><tr><td>19</td><td>8</td><td>48</td><td>56</td><td>20</td><td>Y</td></tr><tr><td>20</td><td>2</td><td>56</td><td>58</td><td>23</td><td>Y</td></tr><tr><td>21</td><td>4</td><td>44</td><td>48</td><td>22</td><td>Y</td></tr><tr><td>22</td><td>8</td><td>48</td><td>56</td><td>23</td><td>Y</td></tr><tr><td>23</td><td>2</td><td>58</td><td>60</td><td>24</td><td>Y</td></tr></table>

续表5.7后移后迭代一调度计划表  

<table><tr><td>序号</td><td>任务持续时间任务开始时间任务结束时间</td><td></td><td></td><td>后置活动</td><td>是否后移</td></tr><tr><td>24</td><td>4</td><td>60</td><td>64</td><td></td><td>N</td></tr></table>

第三步：输出关键链。按照第四章方法可以得出一条关键路径：

$1 { \cdot } > 2 { \cdot } > 3 { \cdot } > 5 { \cdot } > 8 { \cdot } > 2 4$

第四步：识别非关键链并设置缓冲区。由于存在多条非关键链，因此设置了接驳缓冲，在非关键路径和关键路径之间按照 $5 0 \%$ 和 $9 0 \%$ 的估算值的平方和的平方根作为缓冲时间，以防止非关键路径影响关键路径，在迭代中把控住关键路径的时间，从而保证项目进度。

项目中引入的燃尽图是敏捷进度管理的另一个重要的工具，燃尽图用简单的二维图表来表示迭代过程中的工作完成量，其中横轴代表相应时间，纵轴代表剩余工作量。二维表上的两条线分别代表了实际工作量和计划工作量，根据两线的走势可以清晰地判断工作进度是否延期或是提前。在业务数字平台项目二期中，项目的计划工期是142个工作日，对应4个团队成员，就是568人天工作量。每次完成一个活动任务就减去其计划最可能的人天数，如果由于需求变更导致任务增加或减少人天数，则对应在计划剩余工作量上进行增减。燃尽图就是一个直观的用来比较计划和实际的剩余人天数。A公司主要把燃尽图用于每日站会和评审回顾会两个阶段。在每日站会上通过燃尽图的显示预测和迭代可能面临的风险，可以借每日站会的沟通机会，及时消除隐患。在评审回顾会上用于回顾当前迭代的情况，为下一个迭代周期扬长避短做准备。业务数字平台项目二期第一次迭代的燃尽图如下图5.8所示，在评审回顾会中，全体成员对第一次迭代的燃尽图进行了分析总结。整个燃尽图在11、12号和13、14号之间出现明显异常。会议上经过成员沟通，发现11、12号曲线的上升是由于客户需求的变更，要求增加，产品经理经过了评价同意添加。团队成员为了追赶进度并在第二天进行了加班。而13、14号的曲线趋势放缓，发现是前一天的加班导致第二天团队效率下降。从17号开始实际的曲线斜率大于计划斜率，说明团队实际速度大于计划速度，团队的分析是由于逐步适应敏捷开发模式。而迭代最后一天的1个人天剩余数，则是由于测试反馈的bug需要处理。通过第一次迭代的燃尽图和回顾会议，其实就能及时查看进度上出现的问题以及问题产生的原因。

![](images/692615da0f1cf98b75681b54eb123cf6cc55dab7f28d9bc7ed449794f1bd3b7e.jpg)  
图5.8PVoC业务数字平台项目二期第一次迭代燃尽图

# 5.5项目实施效果评价

A公司是首次应用Scrum敏捷开发方法，并且把这个方法用在了交付时间苛刻且需求变更频繁的PVoC业务数字平台项目二期。虽然过程前期有一定的适应难度，但最后还是在项目进度管理中取得了一定的效果。为了评价A公司基于 Scrum进度管理方案的实施效果，本研究选取了与业务数字平台项目一期范围类似、需求方相同、开发团队人员相似的业务数字平台项目一期项目作为参照，进行比对。详细评价如下：

# 5.5.1技术能力增长

Scrum敏捷开发的核心是人和沟通，A公司在业务数字平台项目二期中运用的 Scrum敏捷思想和工具，使得团队成员在频繁沟通中互相取长补短。在整个敏捷化开发流程的应用实践中，每个成员都学到了敏捷思想以及各种新型技术工具，包括产品需求列表、敏捷估算扑克以及燃尽图等。在每日站会中随着问题的抛出和昨日今日工作的总结，项目成员表示技术能力大幅提高。敏捷教练也表示团队的积极性和自驱力明显有了上升。根据两个项目的数据，我们经过计算发现，项目一期6人在为期7个多月的工期内完成了大约55个故事点的工作量，项目二期在同等人数下为期不到6个月的工期完成了63个故事点的工作量，月人均产能从1.22提升到了1.75。同时，几乎差不多的工作量的前提下，实施Scrum方法后，将工期缩短了近一个月时间。并且其中项目一期和项目二期的千行代码缺陷率也有明显区别，如图5.9所示，从一期的 $1 . 2 \text{‰}$ 降到了$0 . 9 \text{‰}$ 。

![](images/070f8368101bfb29ab993a8ea99b228e8498d0093eaa1431d01ccd42e1c0584e.jpg)  
图5.9千行代码缺陷率

# 5.5.2需求响应变快

在 Scrum敏捷开发流程中，客户有别于其他形式的开发流程，它作为整个团队中的重要组成部分。在过程中每次小迭代都能直观地输出可交付增量，能让客户更早地融入团队，对自己的成果的进度有更为快速的了解，也可以尽早对需求是否符合条件进行反馈，在过程中就能及时根据客户的反馈而不断调整，以适应客户的需求。整个项目过程由2周一次的小迭代组成，每个迭代的计划会和评审回顾会都可以有客户的参与。就相当于每2周就能和客户来一次需求确认，不同于项目一期中的需求响应。随着与客户的交互活动增多，最终获得的需求也更为详细和精确，从而中间过程的需求改变也更为减少，从而使得项目开发过程更为顺畅。

# 5.5.3客户满意度提升

在业务数字平台项目一期的建设过程中，项目组7个月内累计收到3次有效客户投诉，从数据显示，客户对整个项目团队存在严重的不满情绪，甚至开始质疑项目团队整体的能力。而在项目二期进行的为期长达6个月的开发过程中，项目团队不仅加强了与客户的沟通，甚至未收到一条有效投诉，且有内部业务人员十分满意产品负责人全过程对需求的响应。团队分析其主要原因项目二期不但没有延期甚至提前完成项目发布以及团队对客户的需求响应时间提高，有了很好的用户体验。在项目运行的一个月后，对曾经涉及过需求提出的集团业务部门和外部人员进行了满意度调查问卷。因为调查问卷涉及的问题涉及需求，所以仅对了解需求的10位人员进行了问卷调查，问卷调查详见附件3。其中一致认为项目二期需求实现度更高，操作问题更少，并且基本对项目二期的运行速度、交付时间、稳定性以及功能实现表现出满意及更高的评价。

调查结果如下：

表5.10满意度调查结果表  

<table><tr><td></td><td>非常满意 （人数）</td><td>满意 （人数）</td><td>一般 （人数）</td><td>不满意 （人数）</td><td>极度不满意 （人数）</td></tr><tr><td>问题4</td><td>2</td><td>8</td><td>0</td><td>0</td><td>0</td></tr><tr><td>问题5</td><td>0</td><td>10</td><td></td><td>0</td><td></td></tr><tr><td>问题6</td><td>0</td><td>10</td><td>0</td><td>0</td><td>0</td></tr><tr><td>问题7</td><td>4</td><td>6</td><td></td><td>0</td><td></td></tr><tr><td>问题8</td><td>0</td><td>2</td><td>8</td><td>0</td><td>0</td></tr></table>

从表中数据可见与需求相关的客户还是对项目的需求体现表示满意。

# 5.5.4项目进度提前

上述几项实施效果是在进度管理下收获到的附加效果，本次实施在最终的进度问题解决上也颇有成效。从下表5.11数据统计表可以看到业务数字平台项目二期经历了12次迭代，最终在第122个工作日完成，比项目目标提前了20个工作日。由此直观地看出项目进度确实受到了控制，得到了提前。通过运用Scrum敏捷开发方法构建开发流程、改进组织机构、通过“史诗-用户故事”进行用户需求分解、运用加权决策矩阵划分用户需求的优先级、利用敏捷估算扑克估算用户故事工时，从而带动完善项目迭代计划，并进行了实施，同时应用关键链和燃尽图及时直观的反应问题，做到进度控制保障，最终得以使得项目提前完工。

表5.11数据统计表  

<table><tr><td></td><td>项目一期</td><td>项目二期计划</td><td>项目二期</td></tr><tr><td>迭代故事点</td><td>55</td><td>60</td><td>63</td></tr><tr><td>迭代周期</td><td>1</td><td>14</td><td>12</td></tr><tr><td>项目总人数</td><td>6</td><td>6</td><td>6號</td></tr><tr><td>项目总时间（天）</td><td>153</td><td>142</td><td>122</td></tr><tr><td>千行代码 bug率</td><td>1.2%</td><td></td><td>0.9%</td></tr><tr><td>月人均产能</td><td>1.22</td><td></td><td>1.75</td></tr></table>

# 5.6本章小结

随着PVoC业务数字平台项目二期的完工，不仅准时完工，甚至进度提前，同时员工的技能能力渐长，团队需求响应变快，客户满意度也提升，说明整个实践还是取得了胜利。在整个项目实施中，提炼了相关的经验教训，以期在后续过程中加以应用，达到项目进度管理的改进作用。

第一点，在项目二期的进度管理实践中首次使用了加权决策矩阵对产品需求列表的优先级进行排序，这也算对敏捷开发方法中优先级的排序方法的缺失进行弥补。由于需求的优先级是决定迭代周期需求选择的重要因素，也是进度计划制定的基础，所以至关重要。但优先级体现的因素可能各有侧重，后期对加权决策矩阵中的因素分解还需要更为详细。

第二点，由于项目是首次执行相关Scrum实践，迭代周期的初始阶段整个团队还是对开发流程稍显生疏，在迭代计划会、回顾评审会中存在放弃沟通的情况。在后期还是需要加强对团队的敏捷培训。

随着全企业信息化、数字化、智能化地逐渐普及，各个业务数字平台必定是拔地而起。而面对业务数字平台的需求多变且繁杂的特性，项目的进度管理也时刻遭受着考验。A公司基于Scrum开发方法的项目进度管理在PVoC业务数字平台项目二期的成功，为以后相关理论方法在A公司中全面推广奠定了重要意义，基于Scrum 敏捷开发方法的项目进度管理也为同行业的软件项目进度管理改进提供了一些参考。

# 第6章结论与展望

# 6.1结论

Scrum敏捷开发方法在软件项目中，已经被众多知名公司广泛应用，而且其适用范围也从软件项目向各行各业蔓延。相比于传统的直线型瀑布开发流程，迭代模式的灵活性开发流程更适合需求变更频繁、交付时间严苛的项目。本文从A公司PVoC业务数字平台项目一期的进度延期问题入手，从组织机构、开发流程、项目进度管理三个角度进行原因分析以及方案解决。PVoC 二期项目进行了新的敏捷组织的创建，以全新的敏捷开发流程，应用敏捷化需求、进度管理工具来共同完成项目进度管理。最后通过PVoC业务数字平台项目二期中的实践，证明基于Scrum敏捷开发方法的新进度管理方案确实对PVoC业务数字平台项目二期的进度管理有明显提升。

本文研究成果如下：

第一，基于Scrum敏捷开发方法的针对A公司的新的进度管理方案，有效地解决了A公司的项目进度延期问题。通过在组织机构、需求分析、进度监控等项目管理角度引入敏捷工具，加强了项目管理，保障了新的项目开发流程地顺利执行，从而进一步全方位保障项目进度管理。

第二，通过调整职能式组织机构，打造敏捷团队进行项目开发。提倡打破职能，全员参与。通过团队的团队力量共同促进项目的进度管理。并通过团队合作方式使整体团队氛围有所提升。

第三，通过“史诗-用户故事”进行需求的收集和变更，并在需求优先级这块引入科学决策方法加权决策矩阵。解决了传统瀑布开发模式下存在的需求不明确和需求变更频繁的问题，为进度计划的制定提供结实基础。

第四，通过引入关键链、接驳缓冲区、燃尽图这类可视化的项目进度管理工具，使得全体团队成员在第一时间明确项目的整体进度，便于进度管理的透明化和简洁化。也使得整个团队能组内互相监督，共同进步。

通过以上研究成果实践，在A公司业务数字平台项目二期中取得了较大的成效：

第一，团队技术能力增长。整个团队通过Scrum敏捷开发方法的学习以及新的工作模式加强了团队内沟通交流。团队内的成员在工作的同时也增长了工作技能。这也是新的进度管理模式下带来的额外的收获。

第二，需求响应变快。整个项目每2周一次的迭代都会在最后的评审回顾会上进行成果的展示。2周一次的频繁可交付产品的呈现可以迅速又直观地让客户看到需求的实体。同时，频繁的交流可以对客户的实时需求给予快速的响应。

第三，客户满意度提升。不仅从客户的调查问卷还是投诉反馈，都能直观地看到客户的满意程度。客户一致认为项目二期需求实现度更高，操作问题更少，并且对项目二期的运行速度、交付时间、稳定性以及功能实现表现出满意及更高的评价。

第四，项目进度提前。项目进度的提前在项目的完工时间上完美体现。整个项目的实际完工时间比苛刻的预计完工时间还提早了20个工作日。这也是项目进度严格把控住的最直观证明。

本项目通过从开发流程建立和项目进度计划、实施以及控制等几个方面改进入手解决A公司的进度管理问题。开发流程和项目进度管理是相互配合的两个关系，有效的项目进度管理是项目开发流程顺利落地的保障，而规范的项目开发流程更是项目进度管理的基础。而指导开发流程建立和项目进度管理优化的核心正是Scrum敏捷开发方法。Scrum敏捷开发方法的便于上手和可塑性，注定其在项目进度管理中的应用会很广泛。

# 6.2展望

随着科技发展，整个TIC行业和A公司的数字化战略调整必然在加速进行中，而A公司的业务重心也会放在相关的业务数字平台软件项目中。在PVoC业务数字平台项目二期研究的基础上得出来的经验教训将被广泛用于日后的类似项目中，并在不断应用归纳中总结出更具普适性的方案。

本文通过基于Scrum敏捷开发的进度管理模型解决了PVoC业务数字平台项目二期中遇到的需求频繁变化，交付时间紧张的问题。但由于仅在一个项目中进行验证，缺乏更大范围的效果验证。其次在小迭代过程中引入的关键路径方法，仅为了弥补敏捷开发迭代过程中任务安排的顺序，防止小迭代延期，并未把关键路径方法用到极致。然后本文的研究范围主要由德尔菲法框定在开发流程、需求相关等角度，未在沟通管理、风险管理等其余管理领域共同对进度管理的影响进行相应措施的改善。最后对于Scrum敏捷开发方法的更多特性并未完全挖掘以共同进行进度管理控制。

项目进度管理的发展已经将近70年，经历了从手工方式到科学方式的蜕变。基于Scrum敏捷开发方法的项目进度管理正融入软件项目进度管理中。随着A公司相关业务的增长，在更多的实践下，必然能总结出更有效果的基于Scrum敏捷开发的进度管理方法。

# 参考文献

[1]许欢.数字化开启TIC未来[J]．中国纤检，2021(10)：52-54.  
[2]敬黎.肯尼亚PVoC市场准入解析[J].进出口经理人,2012(04)：48-49.  
[3] 张蔚虹，刘冲，段利民．项目管理三要素与项目绩效的关系—一以利益相关方满意度为中介变量[J]．技术经济．Technology Economics，2014,33(6): 84-89.  
[4] 李忠起.Q公司基于Scrum敏捷开发的移动端软件项目流程优化研究[D].青岛大学，2022.  
[5] Nienaber R C， Smith E. Incorporating PMBOK2004 guidelines into thesoftware project management supported by software agents model[J].International journal of knowledge and learning， 2009， 5(5/6).  
[6]郑光健．F公司制造订单处理流程优化及应用研究[D]．天津大学，2019.  
[7] Martin R C. PERT: Precursor to Agility[J]. Software development，2004,12(2): 47-50.  
[8] Mckay K， Morton T. Review of: &#x201C; Critical Chain&#x201D; EliyahuM. Goldratt The North River Press Publishing Corporation，GreatBarrington，MA，1997．ISBN 0-88427-153-6[J].IIE Transactions，1998,30(8): 759-762.  
[9]徐哲，王黎黎．基于关键链技术的项目进度管理研究综述[J]．北京航空航天大学学报（社会科学版）.JOURNAL OFBEIJINGUNIVERSITYOFAERONAUTICS AND ASTRONAUTICS(SOCIAL SCIENCESEDITION)[J]，2011，24(2)： 54-59.  
[10]万伟，蔡晨，王长峰．在单资源约束项目中的关键链管理[J]．中国管理科学，2003(02): 71-76.  
[11]赵之友．关键链项目管理评价[J]．科技管理研究，2008(07)：496-499.  
[12]张善从，马丽丛．高新技术项目的关键链风险缓冲区讨论一一基于熵权模糊评价法[J]．现代管理科学，2016(10)：9-11.  
[13]綦方中，汪俊枫，胡丹，etal．基于关键链的敏捷软件开发多项目网络模型[J]．数学的实践与认识，2017，47(07)：11-20.  
[14]姜琳．Z新产品开发项目进度管理优化方案研究[D]．吉林大学，2017.  
[15]武红兰．敏捷开发在A公司软件项目进度管理中的应用研究[D]．北京交通大学，2019.  
[16]黄冕，李林．大数据背景下项目管理理论与模式创新研究[J]．湘潭大学学报（哲学社会科学版）.Journal of Xiangtan University （Philosophy andSocial Sciences)，2020，44(3): 25-30.  
[17] Jieyun Y. Application Analysis of Computer Technology in ConstructionProject Schedule Control in Information Age[J]. Journal of Physics:Conference Series，2021，1881(3).  
[18] Sangeeta， Sitender，Kapil S，et al. New failure rate model for iterativesoftware development life cycle process[J]. Automated SoftwareEngineering，2021，28(2).  
[19] Ben M，Pauline F，Caroline M，et al.‘Going agile’: Exploring theuse of project management tools in fostering psychological safety in groupwork within management discipline courses[J]. The International Journal ofManagement Education，2021，19(3).  
[20] 刘霞．基于SECI模型的高职教育实训教学体系建设探究[J]．中国职业技术教育[J]. Chinese Vocational and Technical Education，2016(26): 66-69.  
[21]龚兰兰，凌兴宏．基于敏捷开发的 SSMWeb应用开发实践[J].实验技术与管理．Experimental Technology and Management[J]，2020，37(2):160-163,167.  
[22]荣国平，张贺，邵栋，etal．软件过程与管理方法综述[J].软件学报，2019，30(01): 62-79.  
[23] Cmc P G S. Balancing Agility and Discipline: A Guide for thePerplexed[J]. Journal of Product Innovation Management， 2Oo5， 22（2).  
[24] Turk D，France R B，Rumpe B. Limitations of Agile SoftwareProcesses.[J]. CoRR，2014，abs/1409.6600.  
[25] Granulo A， Tanovic A. Comparison of SCRUM and KANBAN in theLearning Management System implementation process[C]. 2019 27thTelecommunications Forum: TELFOR 2019，Belgrade， Serbia，26-27November 2019: 640-643.  
[26] Ahmed A R， Tayyab M，Bhatti D S N，et al. Impact of Story PointEstimation on Product using Metrics in Scrum Development Process[J].International Journal of Advanced Computer Science and Applications(IJACSA)，2017，8(4).  
[27]孙杰成，颜锦奎．Scrum敏捷开发方法在跨境电商平台的实践[J]．计算机技术与发展．Computer Technology and Development[J]，2018，28（1) :159-163.  
[28]严晶．基于Scrum方法的软件项目管理过程度量研究[J]．移动通信,2014(14):62-66. DO1:10.3969/j.issn.1006-1010.2014.14.013.  
[29]窦淑宝．敏捷 scrum方法在项目范围管理的应用研究[J]．模型世界,2022(17):243-245. DOI:10.3969/j.issn.1008-8016.2022.17.081.  
[30] Lopez-Martinez J， Juarez-Ramirez R，Huertas C，et al. Problems in theAdoption of Agile-Scrum Methodologies: A Systematic LiteratureReview[C]. Software Engineering Research & Innovation， 2016.  
[31]张逸．在 Scrum中实施敏捷建模[J]．重庆文理学院学报（自然科学版）.JOURNAL OF CHONGQING UNIVERSITY OF ARTS AND SCIENCES,2009，28(5): 51-55.  
[32]张智海，周国祥．Scrum方法的研究与分析[J]．合肥工业大学学报（自然科学版）.JOURNAL OF HEFEI UNIVERSITY OFTECHNOLOGY(NATURAL SCIENCE)，2010，33(2) : 197-200.  
[33]毛雅．H银行数字银行业务线敏捷软件开发中的质量管理研究[D]．西安电子科技大学，2020.  
[34]隋毅，张曦，陈旭．信息化敏捷项目管理转型方案和应用研究[J]．石油规划设计，2020，31(01)： $4 9 - 5 3 + 6 0$   
[35]吴世佳．敏捷 Scrum 模式下的 SI 软件测试项目质量改进研究[D]．西南石油大学，2015.  
[36]张宗祥．基于 scrum 的工控安全软件进度管理研究[D]．北京邮电大学，2020.  
[37]张春环．信息化建设项目的进度管理[J]．科技经济导刊，2021，29(15)：238-239.  
[38]丛超．Scrum敏捷方法在G公司软件项目管理中的应用研究[D]．山东大学，2020.  
[39]孙刚，刘从越，仲里．Scrum与CMMI在软件项目管理领域的兼容性研究[J]．计算机工程与应用，2011，47(15)：61-63.  
[40]李锦，张玲玲．大型软件项目管理的流程设计及分析[J]．科技管理研究[J]，2010，30(15): 204-206.  
[41]乐云，胡毅，陈建国，etal．从复杂项目管理到复杂系统管理：北京大兴国际机场工程进度管理实践[J]．管理世界，2022，38(03)：212-228.  
[42]方雷．工程项目进度偏差量化分析实践[J]．石油化工建设，2021,43(05) : $2 3 \substack { - 2 6 + 8 5 }$ ：  
[43]黄远航，刘洪伟．信息交流模式对软件开发的影响—一软件项目管理的信息交流探索[J]．计算机应用与软件，2007(02)：62-64.  
[44]柯毓娴．大型综合外资项目进度控制与管理[J]．建筑经济，2014(05)：40-43.  
[45] Wang W, Rana ME . Software Project Schedule Management Using MachineLearning & Data Mining[J]. International Journal of Scientific & TechnologyResearch, 2019, 8(9):1385-1389.  
[46] Project Management Institute.A guide to the project management body ofknowledge (PMBOK $\textsuperscript { \textregistered }$ guide)[M]. Beijing $:$ Publishing House of ElectronicsIndustry, 2018  
[47]陶俐言，杜昊炜，赵鹏翡．关键链地铁多项目进度管理研究[J]．科技管理研究,2019,39(17):199-204. DO1:10.3969/j.issn.1000-7695.2019.17.026.

[48] Araszkiewicz, Krystyna. Application of Critical Chain Management in Construction Projects Schedules in a Multi-Project Environment: A Case Study[J]. Procedia Engineering, 2017, 182:33-41.

[49] Mahdi, Ghaffari, Margaret, et al. Current status and future potential of the research on Critical Chain Project Management[J]. Surveys in Operations Research & Management Science, 2015.

[50]周榕.A公司软件开发项目进度管理研究[D]．北京邮电大学,2019.

# 附录

附录1PVoC业务数字平台项目一期进度管理问题调查表

为提高本单位进度管理水平，调查本单位PVoC业务数字平台项目一期进度延期问题，特设计了本调查表。本次调查结果仅用于学术研究，请放心填写。

<table><tr><td></td><td>类型序号</td><td>影响的因素</td><td>评价</td><td>说明</td></tr><tr><td rowspan="3">范围</td><td>1</td><td>需求变更频繁</td><td>□是□否</td><td></td></tr><tr><td>2</td><td>需求范围蔓延</td><td>□是□否</td><td></td></tr><tr><td>3</td><td>需求优先级不明确</td><td>□是□否</td><td></td></tr><tr><td rowspan="2">沟通</td><td>4</td><td>团队之间缺乏有效沟通</td><td>□是□否</td><td></td></tr><tr><td>5</td><td>团队与干系人之间缺乏有效沟通</td><td>□是□否</td><td></td></tr><tr><td rowspan="3">资源</td><td>6</td><td>团队开发人员技术不佳</td><td>□是□否</td><td></td></tr><tr><td>7</td><td>团队缺乏自驱力</td><td>□是□否</td><td></td></tr><tr><td>8</td><td>开发设备资源缺乏</td><td>□是□否</td><td></td></tr><tr><td rowspan="2">质量</td><td>9</td><td>文档过多，相关工作量过大</td><td>□是□否</td><td></td></tr><tr><td>10</td><td>开发成果频繁返工</td><td>□是□否</td><td></td></tr><tr><td rowspan="2">整体</td><td>11</td><td>项目缺乏进度监控手段</td><td>□是□否</td><td></td></tr><tr><td>12</td><td>项目计划变更调整缺乏及时性</td><td>□是□否</td><td></td></tr></table>

备注：

附录2PVoC业务数字平台项目一期进度管理主要问题调查表

为提高本单位进度管理水平，调查本单位PVoC业务数字平台项目一期进度延期主要问题，特设计了本调查表，本调查表基于专家评价表编制而成，旨在确定其主要问题。本次调查结果仅用于学术研究，请放心填写。

<table><tr><td>序号</td><td>影响的因素</td><td>评价</td><td>说明</td></tr><tr><td>1</td><td>需求变更频繁</td><td>□是□否</td><td></td></tr><tr><td>2</td><td>团队缺乏自驱力</td><td>□是□否</td><td></td></tr><tr><td>3</td><td>团队与干系人之间缺乏有效沟通</td><td>□是□否</td><td></td></tr><tr><td>4</td><td>项目计划变更调整缺乏及时性</td><td>□是□否</td><td></td></tr><tr><td>5</td><td>团队之间缺乏有效沟通</td><td>□是□否</td><td></td></tr><tr><td>6</td><td>项目开发流程不合理</td><td>□是□否</td><td></td></tr><tr><td>7</td><td>团队组织结构不合理</td><td>□是□否</td><td></td></tr><tr><td>8</td><td>需求优先级不明确</td><td>□是□否</td><td></td></tr><tr><td>9</td><td>项目缺乏进度监控手段</td><td>□是□否</td><td></td></tr><tr><td colspan="4">备注： 1）请对认为属于主要影响因素的项目在评价栏勾选“是”。</td></tr></table>

附录3PVoC业务数字平台项目二期使用满意度调查表

首先非常感谢您使用我们的业务数字平台。请您抽出宝贵时间参与此次调查。本问卷主要了解您在PVoC业务数字平台项目二期使用过程中的用户体验。本次调查结果仅用于学术研究，请放心填写。

1．您是否在我们业务数字平台项目一期时进行过使用？

□是 □否（直接跳转至第4题）

2．您觉得业务数字平台项目前后两期哪期需求实现度更高？

□项目一期 □项目二期

3．您觉得业务数字平台项目前后两期哪期操作问题更少？□项目一期 □项目二期

4．您对业务数字平台项目二期的运行速度感到满意吗？

□非常满意 □满意 □一般 □不满意 □极度不满意

5．您对业务数字平台项目二期的操作难易满意吗？

□非常满意 □满意 □一般 □不满意 □极度不满意

6．您对业务数字平台项目二期的功能实现满意吗？□非常满意 □满意□一般 □不满意 □极度不满意

7．您对业务数字平台项目二期的交付时间满意吗？□非常满意 满意□一般 □不满意 □极度不满意

8．您对业务数字平台项目二期的稳定性满意吗？□非常满意 □满意□一般 □不满意 □极度不满意

9．您觉得业务数字平台项目二期还有哪些值得改进的地方？
---
type: "always_apply"
---

# 文件过滤规则

## 概述
本文档定义了项目中需要过滤的文件类型和处理方式，用于指导文件读取和处理过程。

## 过滤规则

### 1. 按文件扩展名过滤

#### 需要过滤的文件类型
- **PDF文件** (*.pdf)
  - 原因：PDF文件通常为二进制格式，不适合直接文本处理
  - 处理方式：在读取项目内容时自动跳过所有PDF文件

#### 其他可能需要过滤的文件类型
- **图片文件** (*.jpg, *.png, *.gif, *.bmp, *.svg)
  - 原因：二进制格式，无法进行文本分析
  - 处理方式：根据需要选择性过滤

- **音视频文件** (*.mp4, *.avi, *.mp3, *.wav)
  - 原因：多媒体文件，不包含可分析的文本内容
  - 处理方式：自动过滤

- **压缩文件** (*.zip, *.rar, *.7z, *.tar.gz)
  - 原因：压缩格式，需要解压后才能处理
  - 处理方式：自动过滤

### 3. 按文件大小过滤

#### 大文件过滤
- **超大文件** (>10MB)
  - 原因：可能影响处理性能
  - 处理方式：根据需要选择性过滤或分块处理

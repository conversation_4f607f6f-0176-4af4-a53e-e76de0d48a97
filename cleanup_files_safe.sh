#!/bin/bash

echo "开始扫描指定后缀的文件..."
echo

# 定义要删除的文件模式数组
file_patterns=(
    "*_content_list.json"
    "*_layout.pdf"
    "*_middle.json"
    "*_model.json"
    "*_origin.pdf"
    "*_span.pdf"
)

# 收集所有匹配的文件
all_files=()

# 遍历每个文件模式
for pattern in "${file_patterns[@]}"; do
    echo "扫描模式: $pattern"
    
    # 使用find命令递归查找匹配的文件
    while IFS= read -r -d '' file; do
        all_files+=("$file")
        echo "  找到: $file"
    done < <(find . -name "$pattern" -type f -print0)
done

echo
echo "总共找到 ${#all_files[@]} 个文件"

if [ ${#all_files[@]} -eq 0 ]; then
    echo "没有找到匹配的文件，退出。"
    exit 0
fi

echo
echo "将要删除的文件列表:"
printf '%s\n' "${all_files[@]}"

echo
read -p "确认删除这些文件吗？(y/N): " confirm

if [[ $confirm =~ ^[Yy]$ ]]; then
    echo
    echo "开始删除文件..."
    deleted_count=0
    
    for file in "${all_files[@]}"; do
        if [ -f "$file" ]; then
            echo "删除: $file"
            rm "$file"
            ((deleted_count++))
        fi
    done
    
    echo
    echo "删除完成！共删除了 $deleted_count 个文件。"
else
    echo "操作已取消。"
fi

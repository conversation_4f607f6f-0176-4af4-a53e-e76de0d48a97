申请上海交通大学工程硕士学位论文

# PERT技术在软件开发项目进度管理中的应用研究

院 系：电子信息与电气工程学院

工程领域：项目管理

交大导师：施亮教授

企业导师：徐鑫成

工程硕士：蔡春升

学 号：1130302001

上海交通大学电子信息与电气工程学院

# Thesis Submited to Shanghai Jiao Tong University for the Degree of Engineering Master

# APPLICATION RESEARCH OF PROGRAM EVALUATION AND REVIEW TECHNOLOGYIN SOFTWARE DEVELOPMENT MANAGEMENT

M.D. Candidate: Chunsheng Cai Supervisor (I): Liang Shi Supervisor (I): Xincheng Xu Speciality: IT Project Management

# 上海交通大学学位论文原创性声明

本人郑重声明：所呈交的学位论文，是本人在导师的指导下，独立进行研究工作所取得的成果。除文中已经注明引用的内容外，本论文不包含任何其他个人或集体已经发表或撰写过的作品成果。对本文的研究做出重要贡献的个人和集体，均已在文中以明确方式标明。本人完全意识到本声明的法律结果由本人承担。

![](images/80ca05aedcc1d314d8152db06084d8343e0a253e8284bcf2c401eb5f48f42560.jpg)

# 上海交通大学

# 学位论文版权使用授权书

本学位论文作者完全了解学校有关保留、使用学位论文的规定，同意学校保留并向国家有关部门或机构送交论文的复印件和电子版，允许论文被查阅和借阅。本人授权上海交通大学可以将本学位论文的全部或部分内容编入有关数据库进行检索，可以采用影印、缩印或扫描等复制手段保存和汇编本学位论文。

保密口，在年解密后适用本授权书。

本学位论文属于不保密

（请在以上方框内打“”）

![](images/3fe7103cbd72854a0d22c83a2d52b2b35e5c2ac3f0d941337c242a91fb6896b9.jpg)

# PERT技术在软件开发项目进度管理中的应用研究

# 摘要

软件开发项目与其他项目相比，特点在于开发任务工作量估算难度较大，当软件功能在实现之前，其所对应的风险和不确定性很高。作为一个智力密集型的行业，软件开发人员面对无形的产品概念、不断变化的需求和紧迫的开发时间，都会在开发过程中给相关人员带来不小的困难和无法预知的风险。目前项目管理者在面对这些问题时，采用的解决方法主要是通过完善需求的表现形式和改进软件开发流程，以更加敏捷的方式来开发软件，但是由此造成的代码质量下降和程序臭虫增多和程序维护难度增大，会在无形中增加企业的成本。目前在软件开发项目管理过程当中，相关企业主要是从技术角度进行管理，强调对软件需求定义全面、关键实现技术的量化和软件开发流程规范，但是在项目进度计划管理，特别是在开发任务的时间估算及开发任务和整体项目的时间估算和风险评估方面应用较少。

本文根据软件开发项目时间紧、需求变更频繁、随机性强、技术更新快，但是在总体工期进度、成本控制、风险控制等方面又有较高要求的特点，对企业软件开发项目进度管理特别是软件开发项目进度计划管理进行研究。基于经典项目进度计划管理理论(PERT 技术)，结合本公司在软件开发项目进度计划管理上的特点，从单项活动的工期估算、多路径汇入延迟、总体工期估算与关键路径预测等方面进行改进和修正。为了适应随机性网络在风险控制上的特点，利用Monte Carlo技术对项目计划进度进行仿真优化，模拟各路径成为关键路径的概率，从而产生了路径关键度指标和活动关键度指标，最终根据理论研究成果与实际项目结果进行比较，形成一套行之有效的进度管理方法，大幅度提高软件开发项目计划进度编制的准确率，对于实际的进度计划编制具有实际的指导意义。

关键词：软件开发，进度管理，PERT技术，随机性网络、工期修正，多路径汇入，当量概率法，蒙特卡罗仿真

# APPLICATION RESEARCH OF PROGRAM

# EVALUATION AND REVIEW TECHNOLOGY

# INSOFTWARE DEVELOPMENT MANAGEMENT

# ABSTRACT

Compared to other types of project, software development projects have some special features such as evaluating activity hardly. When software produces developed the first time, it corresponds to the high risks and uncertainties. At the same time, as an intellectual-intensive software industry, such features will bring a big problem to the\manager. Now in order to solve such problems, the software industry uses the methods including requirement refinements and development process enhancement. But it wil bring the quality degradation and bug quantity increase. Currently software development corporate R & D project management mainly focus on a technical perspective, the emphasis on research content， key technologies,critical path's evaluation and management, but in the project schedule and risk management， there are less research.

Based on software development projects tight sechdule, requirement changes quickly，technology update rapidly， but restriction in the overall progress of develpment period, cost control, and risk control. This paper do some research in demanding characteristics of enterprise software development project schedule management， particularly in software development project schedule management. Based on classical management model (PERT technique), this paper includes single process duration optimization, multi-node import optimization, overall duration of the critical path prediction enhancement. At last Monte Carlo simulation will be applied to study the activity's duration by project critical path. It will give some important advice to the actual project schedule management.

KEYWORDS: software development， schedule management， pert, stochastic networks, time optimization, multi path delay, equivalent probablitity, monte-carlo

# 目录

# 摘要..

# 第一章绪论

# 第二章项目进度管理简述

# 第三章单项活动的时间优.. .10

# 第四章总体路径的编排及工时修正.. ...18

# 第五章进度风险控制. .....25

# 第六章 基于MC方法编制软件开发项目 PERT计划的方法. .....28

# 第七章应用案. ....30

# 7.1概述. .30

# 第八章总结与展 .50

# 参考文献.. .52

# 致谢. .55

# 攻读硕士学位期间发表的学术论文. .56

# 第一章绪论

# 1.1论文研究的意义

软件开发管理过程中，作为三大管理目标之一的进度管理，由于对企业的经营效益有很大的影响，从而被大家重视。而且随着软件复杂度的提高，项目延期经常发生。据统计，软件开发过程中产生延期的项目比例超过三分之一，甚至有些项目延期程度超过了计划的两到三倍。

究其原因，影响软件开发进度的因素中，最主要是以下两方面：

第一是软件开发计划制定的是否合理有效，这其中包括进度计划是否涵盖了软件开发的所有相关任务，任务的分解和时间估计能否反映实际开发任务的状况。

第二是软件开发计划在实施过程中的风险能否及时预防和控制，即在软件项目的计划制定好以后，在开发过程中如何预防可能发生的风险，遇到风险之后风险应对是否及时。

为了使软件在开发过程中能够按时完工，相关的项目管理技术，特别是网络计划技术被引入到软件开发过程中,并对开发进度进行规划和控制，用以达到加快软件开发进度、提高软件开发的效率的目的。网络计划技术主要是采用网络图的技术，通过将项目中大的任务分解为较小的任务，并按照其前后顺序进行排序，同时由相关人员估计其完成时间，最终按照网络路径的长度计算相应项目的最长完工时间。

网络计划技术根据任务的工期特点，又分为确定性和随机性两种类型。确定型网络计划技术又叫关键路径法，该技术假定活动的逻辑关系和持续时间都是确定的，而整个网络图中只存在一条耗时最长的路径，该路径即为关键路径。活动是否在关键路径上，由其时差是否为零决定的。而在关键路径上的活动，由于其对整个工期会产生直接的影响，通常需要进行重点关注。

但对软件开发项目来说，由于软件开发活动时间较难通过唯一的时间估计确定，由此引入了另一种随机性的网络计划技术，即计划评审技术，其假设活动的逻辑关系确定但是活动时间服从一定的概率分布。该技术在实际的应用过程中通常是简化为三种不同情况下的估算，即最乐观时间，最可能时间和最悲观时间。

相比较与关键路径法，计划评审技术通过估计不同情况下的完工时间，可以较为全面地反映研发活动不确定性的特点。

之后又出现了图形评审技术，其假定活动之间的逻辑关系和时间都不确定。和计划评审技术不同，GERT允许网络中存在回路。

本文主要通过对技术评审技术进行研究，并将其应用到实际的软件开发项目中，对软件研发活动进行优化，试图提高计划的准确性，保证项目能够按期交付。

# 1.2国内外研究现状

计划评审技术由美国学者Malcolm 等人与1959 年提出的一种进度风险评价技术[38]，其假定活动的概率分布服从 Beta 分布，Beta 分布是[0,1]范围的单峰连续函数，可通过形式变换，转换称由悲观时间和乐观时间为界的函数[18]。由于其变换形式灵活，可以转换成正态分布、三角分布和γ分布等，美国控制论专家L.A.Zadeh 教授与1965 年提出了模糊 PERT，通过三角模糊数来进行活动的推算和排序[ [10][]

关键链技术也是近年来学者研究的重点之一，关键链技术由1997年以色列物理学家高德拉特提出，其主要思想是通过缓冲区来控制项目的进度和风险[15]。

计划评审技术模型的精度问题，也有学者对其进行了优化，主要是对三时估算的精度进行优化，包括 Moder-Roger 模型、改进的 Davison-Cooper模型和Person-Tukey模型。通过改变权重优化模型，相比较于经典的计划评审技术，精度上有了很大的提高[17]。

# 1.3论文研究的重点

本文拟根据企业软件开发项目管理的时间紧、任务多、技术难度不一等特点,对企业软件开发项目进度管理特别是软件开发项目的计划管理进行研究。基于项目进度计划理论(PERT技术)和风险管理理论,尝试对目前企业软件开发项目进度计划管理进行改进和修正，将上述非确定型问题最大程度地定量化，同时对随机性网络进行建模，将活动完工风险纳入到该模型之中，对项目进度的管理提供更加完善的建模分析能力，同时通过Monte-Carlo对其进行仿真优化，更加真实地反映软件开发过程当中遇到的进度问题，从而进一步加强软件开发项目所涉及到的进度计划的制定和风险管控能力，并根据相应的理论研究成果以及软件开发过程当中实际的软件项目进度操作情况，在实际软件开发过程当中，形成一套适合本企业软件开发项目的进度管理方法，确保软件开发项目能够在规定的时间内按时、保质保量地完成。

# 1.4论文框架

本文研究的内容是根据软件开发项目进度计划中的各项不确定性，对传统的计划评审技术进行改进并结合计算机仿真，得到一套适合于软件开发项目管理应用的改进PERT技术方案。在国内外最新的研究成果的基础上，通过分析软件开发过程当中的各项不确定性，对计划评审技术在单项活动的三时估算法进行修正，得到修正后的活动历时的均值和方差，然后利用计算机仿真技术对活动的持续时间进行模拟仿真。

本文首先对单项活动引入了风险估算系数，提高单项活动的估算准确度，并利用当量概率法对多节点汇入的活动的工期进行修正，最后通利用计算机仿真技术对整个工期的各条路径进行模拟仿真，得到各条路径的时间均值，按照每条路径的时长进行排序，取时间最长的为关键路径，并加总在每条路径上的活动的概率为活动关键度，活动值较大的活动其落在关键路径上的概率也最大，需对其进行管理。

在风险控制方面，针对单个活动在技术难度和完成时间上的不确定，在三时估算中针对每个活动的估计时间优化了完工概率值，而在整体的风险控制方面，引入了路径关键度和活动关键度指标，这些指标活动以及活动所在路径的风险值，从而使本企业在面对复杂项目时帮助项目管理者在复杂的网络图结构和众多的工期路径中识别关键活动和关键路径，合理安排工期，有效利用资源，并在项目实施过程当中对关键活动和关键路径进行重点监督和控制，及时发现实施过程中的问题并及时处理，从而保证项目在规定的时间内保质保量地完成。

本文结合多种知识理论，包括项目进度管理理论、项目的计划管理理论、微积分、统计学、风险管理、Monte-Carlo 模拟等等，采用定性和定量的分析方法对软件开发项目进行了实例研究，对软件开发项目在任务分解和排序、任务的时间估计、任务的风险控制等方面进行了深入细致的研究，最后形成了一套适合本企业软件开发项目管理的进度计划管理方法及风险控制方法。

目前企业中对软件开发项目管理主要针对项目的需求进行管理，在进度控制方面仅在任务的实现上有所控制，对活动的时间估算及其实现的风险和整体项目的整合及风险控制相对薄弱。传统的计划评审技术只体现了活动之间的前后逻辑关系，并未考虑实际软件开发过程中由于资源的冲突和技术难度对活动工期的影响，因此在时间概率分布参数的估算上和实际情况存在较大的误差，直接应用于软件开发项目会有较大偏差，实用性较差。本文首先利用数学模型对传统 PERT方法的单个活动的工期估算方法进行分析，找到其特点与研发项目的差异之处，并进一步提出修正方法；由于PERT网络图中，每条路径都可能成为关键路径，因此采用蒙特卡洛方法对支线工期及整体工期进行模拟仿真，得到每条路径成为关键路径的概率；在此基础上又引入了活动关键度，对单个活动的风险系数进行测算。经过对经典的PERT方法进行了改进，使得其更加适合于本企业的软件开发项目。

最后，本文通过实际的软件开发项目案例，对比分析了理论研究成果和实际软件开发项目的实施情况，印证了修正计算结果相对于传统的计划评审技术在单项活动的时间估算、多路径工期估算和整体工期估算在精确度方面有较大提升，为以后在进行相关的软件开发项目时在进度管理特别是进度计划管理方面提供实际的参考。

# 第二章项目进度管理简述

# 2.1项目管理概述

根据 PMBOK一书中对项目的解释，项目为了完成某个特定的目标，通过一个临时的组织和人员，以及相关的项目管理技巧以完成项目预先定义的可交付物。项目在时间上表现为临时性，在项目的最终交付物上表现为独特性，在目标的细分和进度的计划和控制上表现为渐进明细[1]。

项目的独特性，是指项目的人员、资源以及项目的最终产出而言都是独特的。其中最为重要的是每个项目都会创造出独特的产出，包括产品、服务或成果。软件开发项目，都有一个明确的目标，例如为解决企业在运营过程中的效率问题而进行的软件开发项目，虽然在这个过程中可以共用一些技术，比如有些软件开发的程序代码、组件、框架甚至包括团队成员，但是每个软件项目在最终的交付物上都是独一无二的。

项目的临时性，是指对项目而言有一个明确的开始时间和结束时间，所以为此而建立的组织形式也带有很强的临时性，当项目达到了项目干系人的目标和预期时，或当项目因为某些原因导致无法满足项目干系人的要求而被迫中止时，项目就结束了，项目人员也可能会被分配到其他项目组。项目在时间维度上表现出明确的区间性，即明确的启动时间和终止时间，是项目一个非常重要的特性。

正是因为项目的独特性，往往没有相关的经验可以借鉴，在计划阶段往往充满了很大的不确定性，而临时性，会使项目成员在时间估算上往往会趋于保守，使得本该被正常发布的时间被延后。

在项目管理过程中，存在一个“铁三角”，即时间、范围和成本。范围的变化会影响进度和成本，进度的变化又会影响成本和范围，成本的变化也会影响范围和进度，三者相互影响，相互牵制，如图1所示。

![](images/7e8a22d939b37a75da1d3efe05f9821461921394aeddcf08848a15432c807a20.jpg)  
图1项目管理铁三角  
Fig.1 Triangle in Project Management

进度作为三大项目管理的重点之一，面对软件复杂度的提高和多种因素的影响，如何在规定的工期内保质保量地完成项目，是摆在项目管理者面前一个很大的问题。

# 2.2项目进度管理概述

项目进度管理比较系统的解释是指在项目进行过程中，在一定的质量要求之下，在指定的预算范围内，项目所要做的工作进行分解，并将分解后的任务分配给相应的团队成员，使项目按照预定的时间完成，当计划出现偏差时，对项目的计划进行修正，相应地对工作的进度进行控制。

在项目管理过程当中，进度管理分为计划和控制两大块，计划阶段主要包含定义活动、排列活动顺序、估算活动资源和活动的持续时间，最后是制定进度计划，这里面主要是采用网络计划技术，通过将活动按照前后逻辑关系相连，形成一张单向的网络图。。

网络进度计划技术主要有以下优点：通过将任务进行分解和排序以及资源的估算，便于制定更加详细而准确的计划，为后期的执行也提供了基础，同时使得项目管理者能够对项目管理过程中可能遇到的瓶颈做到心中有数，及时准备应急方案进行应对，以便于项目目标的达成。通过将任务以网络图的形式表现，可以一目了然的查看到项目的整个工期以及各任务的工期，为项目在作出需求变更或者人员调整的时候提供参考。为了控制项目中的风险，项目管理者需要确定项目中的不确定因素，如项目中的活动的时间延误会怎样影响项目的完成，活动和活动之间在何处存在闲置，确定影响项目完成时间的关键影响因素，识别项目中的风险，制定各种风险应急计划，最终为管理者提供评估备选方案的方法，为决策提供了获取必要事实的基础。

通过使用时间网络分析法，确定每项任务的时间，找出时间最长的关键路径，根据项目的实际情况来协调和分配人力、物资和资本等需求，可以提高企业的经营效率，在项目的执行期间方便管理者经常性的检查项目的进展情况，在给利益相关人作报告的时候，可以提供诸如时间，资源的分配等信息，展现了项目活动间的相互关系，能够识别最长路径及关键路径，使我们能够实施进度风险分析。

常用的进度计划技术主要有甘特图、关键路径法、计划评审技术、图形评审技术等 图 。

关键路径法是由美国杜邦公司在1956年和蓝德公司合作的过程中发明的一种项目管理技术。两家公司利用计算机来描述任务之间的关系，相对于手工方式，无论在处理速度，还是在项目的规模方面，都有很大的提高，进而制定了项目进度管理的技术，从而为美国杜邦公司在建造化工厂的项目上，节省了100 万美元。此后关键路径法得到了迅速的传播和推广。关键路径法是一种肯定型的网络图技术，根据项目的目标，将项目分解成为各个相互独立功能完备的活动，确定每个活动的持续时间，然后用任务之间的逻辑关系将所有的项目中的所有任务连接起来，最后找出项目中持续时间最长的路径作为关键路径，该路径的工期也就是项目的工期。关键路径法通过计算各活动的时间，同时对有前后存在逻辑关系的路径的时间进行加总，总时间最长的路径作为关键路径。关键路径法的特点是活动的工期是唯一和确定的。

WBS 任务名称 工期 开始时间 完成时间 六四五六日四六日四五六日四五六四五  
1 □ 1 1.启动改进系统性能任务组 10个工作日？ 2013年5月6日 2013年5月17E销售部接口人，财务部接口人，运营部接口人，QA，产品经理，研发经理  
2 2 2.汇总改进系统性能的修改意见，得出需要 5个工作日？ 2013年5月20日 2013年5月24日 销售部接口人，财务部接口人，运营部接口人  
3 3 3.准备改进设计方案 5个工作日？ 2013年5月27日 2013年5月31日  
4 3. 3.1准备测试数据，细化性能改进方 1个工作日？ 2013年5月27日 2013年5月27日  
5 □ 3.b 3.2把方案提交给技术总监 2个工作日？ 2013年5月28日 2013年5月29日  
6 国 3.e 3.3确定系统性能改进点和性能指标 2个工作日？ 2013年5月30日 2013年5月31日  
7 4 4.实施改进方案 10个工作日？ 2013年6月3日 2013年6月14日 工程师，研发经理，QA  
4 4.1照优先级进行开现有功能是； 5个工作 2013年月1日 2013年月14日  
10 5 5.系统上线 1个工作日？ 2013年6月17日 2013年6月17日  
11 □ 6 6.获得反馈，持续改进系统 9个工作日？ 2013年6月18日 2013年6月28日

# 图2关键路径法

和关键路径法不同的是，计划评审技术是一种不确定性网络的进度风险评价技术。1958年美国海军特种计划局在进行一个名为“北极星”的导弹核潜艇的研发过程中所使用的一种进度管理技术，该项目最后比计划提前了两年完成。之后又被应用于“阿波罗登月计划”，也获得了成功。随后计划评审技术在全球范围内传播和推广，为了适应各种不同项目管理上的需要，又出现了图形评审技术和风险评审技术。

计划评审技术将活动工期分解为三个时间点，即最乐观时间、最可能时间和最悲观时间，假设活动工期服从β分布，三个估计时间中，最乐观时间的完工概率假定为 $100 \%$ ，最悲观时间的完工概率假定为 $0 \%$ ，最可能时间的完工概率为$50 \%$ ，相对与关键路径法，可以反映项目管理过程中的风险管理思想，可以更加准确地估计这些不确定性活动的时间，同时，最乐观时间和最悲观时间表示活动的变化范围，对活动工期的期望值的计算提供了参考价值。

由于软件开发的活动时间难以估计，这往往是由多种因素造成的，其中包括代码量难以估计，活动本身的难度、项目成员本身的经验不足，或者项目管理过程中不可预料的风险,通过将活动的不确定性转化为随机变量，且服从一定的概率分布，通过计算机模拟，用于拟合不同活动服从不同分布的特点。

# 2.3本公司项目管理现状

笔者所在公司是一家互联网广告公司，是从事互联网广告投放的技术性公司，在公司内部的项目管理过程中，目前采用的是一种自下而上的时间估算方式，即由工程师进行时间估算，然后项目经理对工程师估算的时间进行汇总，报给总监。

![](images/e8274a345c1aa72b86434e7f3a97043b07d2000262f56df13a5111796c48a451.jpg)  
图3项目人员结构图  
Fig.3 Project Member Structure

本公司目前的开发流程是由产品经理根据市场或者销售的要求以及在产品使用过程中的反馈进行汇总分析，形成下一个阶段的需求开发文档，然后和开发经理进行协商，按照任务的优先级排期，确定下一个阶段的开发任务，同时召集相关的开发人员进行需求的分析和任务的分配和时间的估算。

在项目管理过程中，目前采用的是关键路径法，由相关的工程师进行时间的估算，并给出唯一的时间估计。而在项目的开发过程中，由于时间估计依赖于工程师的经验，往往会由于一些意外的情况导致开发工程师没法及时完成任务，导致项目延期，另外估算过于乐观往往会导致代码质量下降，这些都是不可忽视的问题。为了更好地将软件产品开发出来并达到上线要求，同时鉴于研发型任务时间的不确定性，采用计划评审技术和定量风险分析来对项目进行计划和控制。

# 2.4本章小节

本章主要讲述了项目的特点以及进度管理发展历史，同时介绍了关键路径法和计划评审技术这两种目前常用的进度管理技术，由于软件开发项目具有不确定性，因此采用计划评审技术进行工时估算和风险控制，同时介绍了本公司的项目管理状况，目前公司采用关键路径法，缺少风险控制思想，所以引入计划评审技术进行研究。

# 第三章单项活动的时间优化

# 3.1概述

软件开发项目首先要进行任务分解，将整个项目分解为一个个相互独立的小任务，通过活动的逻辑先后顺序进行连接排序，并对任务进行时间估计，寻找工时最长的路径作为关键路径进行资源的管理。但是面对难度各异的软件开发任务，每个人都会根据自己的实际情况出发，估计任务的时间。同时软件开发过程本身有自己的特点，那就是代码的重用，在开发过程中积累下的通用模块，可以以相对固定的时间进行开发，而新模块的时间估计难度较大，带有很大的不确定性。

在经典的PERT方法中，通常采用三时估算法对单项活动的工期进行预测。三时估算包括最乐观时间、最悲观时间和最可能时间。最乐观时间估计是指在最顺利的情况下活动完成所需要的最早时间，最悲观时间是指在最不顺利的情况下，比如资源匮乏员工请假技术难度过大无法在短期内攻克等极端情况下完成活动所需要的时间，而最可能时间 $\mathsf { m }$ 是指在一般正常情况下完成相应活动的时间。

由于三时估算带有很大的主观性，，数据可能和实际存在较大偏差。当项目在进行过程中面临新的变化，如引入新技术和新的开发流程，任务能否按照估计时间完成就带有很大的不确定性。再次计划评审技术假设活动的耗时为一随机变量，假设服从β分布，但是实际的过程中，活动的实际完工时间可能会服从不同的分布，如三角分布，正态分布等，如何确定合适的分布状况，这个需要通过计算机模拟技术进行不断的模拟仿真，并和实际的工期进行比较，从而确定最符合实际的分布。

由于计划评审技术假定最乐观时间的完工概率为 $100 \%$ ，而最悲观时间的完工概率为 $0 \%$ ，但在实际的项目中，完工概率 $0 \%$ 和 $100 \%$ 都是很少出现的状况，需要结合实际情况，对这两个时间的完工概率进行相应的优化，使其准确地反映实际完工时间，以期能反映实际的工程实践活动，为后续的工程提供切实的指导。对于形态的研究，Malcolmetal假设乐观时间的完工概率是 $100 \%$ ，悲观时间的完工概率 $0 \%$ ，后来 Kamburowski 和 Herreri'as et al 分别在 1997 年和 2003 年证明了非对称的β分布[3-6]。

Sassi等人认为，参数 $\mathsf { p } { = } 4$ ， $\mathtt { q } = 4$ ，最可能时间的完工概率四倍于乐观时间的完工概率和悲观时间的完工概率[]。 但是实际项目活动过程中，最可能时间的完工概率和乐观时间以及悲观时间的倍率可能是变化的，既可能一倍，也可能两倍，所以不能够用某一个固定的倍数来解决完工概率的问题。

针对上述问题,可以通过分析 PERT模型的概率密度函数，即β函数，如（3-1)所示：

$$
f ( x | a , b , p , q ) = \frac { ( b - a ) ^ { 1 - ( p + q ) } } { B ( p , q ) } ( x - a ) ^ { p - 1 } ( b - x ) ^ { q - 1 }
$$

式中，a，b表示分布区间的两个端点， ${ \mathsf p } , \ { \mathsf q }$ 则影响分布曲线的形态。

从上述公式可以看出，其概率密度函数有四个参数，针对不同的随机变量特性，可以产生不同的分布形态，对项目计划者来说，如何确定活动相应的参数，使分布符合实际情况，用以拟合不同活动的特征，是一个需要解决的问题，而活动的样本量将会对该类活动的均值和方差产生很大的影响[8][9]。

# 3.2PERT技术的新发展

为了克服PERT技术在计划时的局限性，很多学者也利用其他模型对PERT进行改进，如模糊PERT、关键链、启发式算法等。

# 1、模糊 PERT[10][1]

模糊 PERT是通过模糊数学的方式来对活动的完工概率进行估计，从而克服了经典 PERT活动假设服从β分布，整个活动的工期服从正态分布这样的限制，模糊数是美国控制论专家L.A.Zadeh 教授于1965年提出的,和实数的二值逻辑相比，模糊数采用连续的区间来表示事物的发生程度，通常有L-R隶属函数，运用模糊数运算方法来对活动的时间进行推算和排序，来进行整个活动的关键路径的分析。

# 2、启发式算法[12][13]

启发式算法通过制定相应的有限规则或者依据自然法则经验，搜索可行空间，

估价可能有解的各种不同途径，记录已经搜索到的各个路径。启发式算法在资源受限项目调度问题的应用过程中有很高的搜索效率。

# 3、关键链技术[

1997年以色列物理学家高德拉特在他的书《关键链》中，首先提出了关键链这样一个概念，关键链技术强调全局和整体最优，通过识别项目管理中的约束因素，在关键的节点和路径上设置一定的缓冲时间，而大多数这种约束来自于资源受限，根据实际情况设置缓冲区的大小，通过集中管理缓冲区来监控项目的执行。

# 4、PERT三时估算法的发展现状

β分布在实际应用过程很繁琐，为了解决这个问题，国内外的学者对其做了优化处理，提出了很多近似优化公式，其中有些学者对估算样本点数进行了优化，比如以两点估算或者五点估算，而另外一些学者对完工概率进行了优化[17]。

(1)Moder-Roger 模型

$$
\mu = \frac { x ( 0 . 0 5 ) + 4 m + x ( 0 . 9 5 ) } { 6 }
$$

$$
\sigma ^ { 2 } = \left[ { \frac { x ( 0 . 9 5 ) - x ( 0 . 0 5 ) } { 3 . 2 } } \right] ^ { 2 }
$$

其中， $\mathbf { x } ( 0 . 0 5 )$ 表示单项工作完成概率为 $5 \%$ 时的工作时间估计， $\times ( 0 . 9 5 )$ 表示单项工作完成概率为 $9 5 \%$ 时的工作时间估计， $\mathsf { m }$ 为最可能完成时间。

(2)改进的 Davison-Cooper 模型

$$
\mu { = } 0 . 1 6 m + 0 . 4 2 \big [ x ( 0 . 1 0 ) + x ( 0 . 9 0 ) \big ]
$$

$$
\sigma ^ { 2 } = \left[ \frac { x ( 0 . 9 0 ) - x ( 0 . 1 0 ) } { 2 . 6 5 } \right] ^ { 2 }
$$

在该模型中，不但将乐观时间与悲观时间改为了完工概率 $10 \%$ 和 $90 \%$ 时的完工时间，且将三时估算中三个时间点的权值进行了修正，同时其方差也对应地进行了更改。

(3) Person-Tukey 模型

$$
\mu = 0 . 6 3 x ( 0 . 5 0 ) + 0 . 1 8 5 \big [ x ( 0 . 0 5 ) + x ( 0 . 9 5 ) \big ]
$$

$$
\displaystyle \sigma ^ { 2 } = \left[ \frac { x ( 0 . 9 5 ) - x ( 0 . 0 5 ) } { 3 . 2 5 } \right] ^ { 2 }
$$

该模型改用完工概率 $5 \%$ 与 $9 5 \%$ 的工作时间替换乐观时间与悲观时间，同时也将三点时间的权值进行了修正。

以上三种模型都是对三时估算的权重进行修正，经过比较分析研究，改进的Davison-Cooper模型和 Person-Tukey模型无论在期望值误差和计算精度上都表现地比较好，而Moder-Roger 模型在计算精度表现最高。而早期的 PERT模型在期望值误差和进度方面都表现较差[1]

# 3.3 PERT技术在研发过程的问题

由于活动的时间不确定，计划评审技术通过概率密度函数来描述活动的时间，目前计划评审技术假定活动是服从β分布的连续性概率密度函数，由于 $\beta$ 分布是定义在区间(0,1)上的一个连续性随机变量，它的概率密度函数为

$$
f ( x ) = ( x - a ) ^ { p - 1 } ( b - x ) ^ { q - 1 } \frac { 1 } { ( b - a ) ^ { q - 1 } \beta ( p , q ) }
$$

其中 $a < x < b$ ， $\mathsf { p } { > } 0 , \mathsf { q } { > } 0$ ， $\beta ( \rho , \textrm { \textsf { q } } )$ 是特征系数为 ${ \mathsf p } , { \mathsf q }$ 的 $\beta$ 分布函数。

β分布有很大的灵活性，通过形态参数 ${ \mathsf p } , { \mathsf q }$ 的变化，能够拟合出不同形态的分布图形。令 ${ \mathsf p } { = } 1$ ， $\mathsf { q } = 1$ ，可以得到均匀分布；当 $\mathsf { p }$ 和 $\mathfrak { q }$ 增大直至无穷时， $\beta$ 分布退化为一个准确的单点时间估计，此时，可以通过关键路径法来解决相关的时间估计问题。

β分布在计划评审技术中被广泛应用，先对其性质进行研究[18]:(1)若随机变量 $\mathsf { X }$ 服从(0，1)区间上的参数为 ${ \mathsf p } , \ { \mathsf q }$ 的β分布，则

$$
E ( x ) = { \frac { p } { p + q } }
$$

$$
V a r ( x ) = \frac { p q } { ( p + q ) ^ { 2 } ( p + q + 1 ) }
$$

(2）若随机变量×定义域在(0，1)区间上，且其服从参数为 ${ \mathsf p } , { \mathsf q }$ 的β分布，则其最有可能的取值

$$
x _ { 0 } = \frac { p - 1 } { p + q - 2 }
$$

若随机变量×映射到区间[a,b]上，可以得到以下结论：

(3)活动的期望时间

$$
E T = { \frac { a q + b p } { p + q } }
$$

活动的方差

$$
\sigma ^ { 2 } = \frac { ( b - 1 ) ^ { 2 } p q } { ( p + 2 ) ^ { 2 } ( p + q + 1 ) }
$$

(4)β分布中最可能取值:

$$
m = \frac { a ( q - 1 ) + b ( p - 1 ) } { p + q - 2 }
$$

(5)当p，q越大时，分布的峰度越大，概率分布越集中

(6)当 ${ \mathsf p } { > } { \mathsf q }$ 时，该分布为负偏，其最可能时间

$$
m < { \frac { a + b } { 2 } }
$$

(7)当 $\mathsf { p } { < } \mathsf { q }$ 时，该分布为正偏，其最可能时间

$$
m > { \frac { a + b } { 2 } }
$$

![](images/a6087b9ff7786d60c230d4652d458319a8c465409181e87fdd672ec00479c5ff.jpg)  
图 $4 \mathrm { ~ p ~ }$ 、q值与β分布的关系

Fig.4 Relationship between p、q and $\beta$ （204

将式1与式5联立，式2与式6联立，同时将式7代入方程组中，可以得到当 $p = q = 4$ 时,三时估算法的期望值与方差才是 $\beta$ 分布的期望值与方差的无偏估计 [19]

# 3.4单项活动的时间修正

著名数学家华罗庚曾就经典的计划评审技术中单项活动的期望时间的计算公式做过这样通俗的解释，即三值估算中假定最可能时间 $\mathsf { m }$ 是乐观时间a的两倍，同时 $\mathsf { m }$ 也是悲观时间b的两倍，则a与 $\mathsf { m }$ 的平均值 $x _ { 1 } = { \frac { a + 2 m } { 3 } }$ （204 b与 $\mathsf { m }$ 之间的平均值 $x _ { 2 } = { \frac { 2 m + b } { 3 } }$ （2042 。事实上， $\mathbf { X } _ { 1 }$ 和 $\left| { { \bf { { X } } } _ { 2 } } \right.$ 是在样本容量为2的样本集中，对于 $\mathsf { m }$ 相关的两个样本容量。假设 $\mathbf { \dot { X } } _ { 1 }$ 和 $\mathbf { X } _ { 2 }$ 是独立的两个随机变量，两者平均，得到样本均值

$$
\stackrel { - } { x } = \frac { 1 } { 2 } \left\{ \frac { a + 2 m } { 3 } + \frac { 2 m + b } { 3 } \right\} = \frac { a + 4 m + b } { 6 }
$$

该样本集的方差是

$$
S ^ { 2 } = { \frac { 1 } { n - 1 } } \sum _ { i = 1 } ^ { n } ( x _ { i } - { \overline { { x } } } ) ^ { 2 } = \left[ ( { \frac { a + 2 m } { 3 } } - { \frac { ( a + 4 m + b ) } { 6 } } ) ^ { 2 } + ( { \frac { 2 m + b } { 3 } } - { \frac { a + 4 m + b } { 6 } } ) ^ { 2 } \right] = { \frac { ( b - a ) ^ { 2 } } { 1 8 } }
$$

因为有两份样本空间，所以在均分情况下的样本空间的方差是

$$
\sigma ^ { 2 } = \frac { 1 } { n } S ^ { 2 } = \frac { 1 } { 2 } S ^ { 2 } = \frac { ( b - a ) ^ { 2 } } { 3 6 }
$$

由上述证明可见，经典PERT在计算活动的持续时间时是建立在独立样本集这样的前提下。但在实际应用中， $\mathbf { X } _ { 1 }$ 和 $\mathbf { X } _ { 2 }$ 通常是相关的，因此经典PERT对所求的活动期望时间存在一定程度的误差。

根据β分布的性质，可以考虑通过在对三点时间进行预测时，增加对其分布参数p、q的估计，控制β分布函数的总体形态，减小其完成时间的期望及方差预测的偏差，达到更精确预测工作完成时间的目的。

实际上，在软件开发项目计划编制的过程中，各单项活动中的三点估计值在实际实施中不可能固定在某个概率上。为了能够更精确地反映单个活动在各自不同情况的可能性，同时将其体现在最终的完工时间的概率分布上，可以在乐观时间，悲观时间，最可能时间的估计基础上增加三点时间的发生概率估计值，通过上述六项参数对单个活动的分布进行拟合，从而得到相应分布参数。比如假设某个活动中，乐观时间、悲观时间和最可能时间的发生概率分别为 ${ \bf p } _ { 1 }$ $\ 、 \ p _ { 2 } \ 、 \ p _ { 3 }$ $\left( \mathtt { p } _ { 1 } \cdot \mathtt { p } _ { 2 } \cdot \mathtt { p } _ { 3 } > 0 \right) \circ$

根据上述修正思路及均值的计算方法，对 $\beta$ 分布期望值及方差重新进行计算：结合相应发生的概率，乐观时间a和最可能时间 $\mathsf { m }$ 的平均值 $x _ { 1 } ^ { ' } = \frac { ( a p _ { 1 } + b p _ { 3 } ) } { p _ { 1 } + p _ { 3 } }$ （ap+bp3），悲观时间和最可能时间'的平均值 $x _ { 2 } ^ { ' } = \frac { b p _ { 2 } + m p _ { 3 } } { p _ { 2 } + p _ { 3 } }$ bp+mp，设x'与x为样本容量为2的两个样本变量，则能够得到样本均值：

$$
{ \overline { { x } } } = { \frac { x _ { 1 } + x _ { 2 } } { 2 } } = { \frac { 1 } { 2 } } { \Bigg [ } { \frac { ( a p _ { 1 } + m p _ { 3 } } { p _ { 1 } + p _ { 3 } } } + { \frac { b p _ { 2 } + m p _ { 3 } } { p _ { 2 } + p _ { 3 } } } { \Bigg ] }
$$

该样本集合的样本方差为：

$$
S ^ { 2 } = \frac { 1 } { n - 1 } \sum _ { i = 1 } ^ { n } ( x _ { i } - \overline { { { x } } } ^ { . - } ) = \frac { 1 } { 2 } \Bigg [ \frac { ( a p _ { 1 } + m p _ { 3 } } { p _ { 1 } + p _ { 3 } } { - } \frac { b p _ { 2 } + m p _ { 3 } } { p _ { 2 } + p _ { 3 } } \Bigg ] ^ { 2 }
$$

两个样本的整体方差为：

$$
\sigma ^ { 2 } = \frac { 1 } { n } S ^ { 2 } = \frac { 1 } { 4 } \left[ \frac { ( a p _ { 1 } + m p _ { 3 } } { p _ { 1 } + p _ { 3 } } { - \frac { b p _ { 2 } + m p _ { 3 } } { p _ { 2 } + p _ { 3 } } } \right] ^ { 2 }
$$

利用β分布的基本性质，可以从分布的最可能取值 $\mathsf { m }$ 与方差 $\sigma ^ { 2 }$ 得到 $\beta$ 分布的形态参数 ${ \mathsf { p } } _ { \boldsymbol { \mathsf { \backprime } } } \ { \mathsf { q } } .$ 。

$$
\sigma ^ { 2 } = { \frac { ( b - a ) ^ { 2 } p q } { ( p + q ) ^ { 2 } ( p + q + 1 ) } }
$$

$$
m = \frac { a ( q - 1 ) + b ( p - 1 ) } { p + q - 2 }
$$

# 3.5本章小节

本章介绍了计划评审技术发展状况，同时指出了计划评审技术在单项活动估算方面的缺陷，增加了三时估算发生概率的估计，并利用β分布性质和分布形态参数p、q，推导出从均值和方差确定相应的形态参数，该方法能够更加精确地

体现单项活动的完工时间概率分布。

# 第四章总体路径的编排及工时修正

# 4.1概述

在软件开发过程中，通过对项目任务的分解之后，依据任务之间的逻辑关系，可以形成含有多条路径的网络图，而整体工期的估算是通过计算各条路径上的总时长，依据时间长短进行排序，将时间最长的路径作为整个项目的总时长，在项目过程中进行重点控制。但是对于多路径汇入而言，活动工期由于受到多条路径的影响，其完工概率势必会受到影响。

经典的 PERT在项目工期估算方面也存在一定的误差，主要由以下两个方面：

1．经典PERT假设最可能时间的完工概率是 $50 \%$ ，而在实际的开发项目活动中，开发活动的不确定性远大于其他类型的项目， $50 \%$ 的完工概率显然是不能满足其要求的，因此需要对其进行修正。  
2．当有多项活动汇入同一节点时，不同活动间的网络时差可能会导致整体项目的工期延迟，本章将基于当量概率方法对此进行修正。

为了更好地在计划编制时对每个活动的重要程度进行甄别，引入了基于风险理论的活动关键度指标:ACP。通过对每个活动的关键度指标进行计算，识别相应活动的风险高低，能够更好地对项目进度进行控制。

最后，本章将给出基于蒙特卡洛(MC)方法[20]的网络时间总工期的抽样计算方法。

# 4.2总工期分布形态

在项目的进度管理过程中，各项活动的持续时间是由各自特点及所需占用的资源确定的，其可以假定为服从β分布，也可以服从正态分布、三角分布、γ分布等等。项目的总工期则是把PERT网络图中关键路线上活动的持续时间相加得到的总体时间概率分布，当项目中的活动较多时(整个项目的活动数大于30)，而每个活动相对于整体的工期来说影响都比较小时，可根据概率论中的中心极限定律，

认为项目的总工期是服从正态分布的。

# 4.2.1完工概率及风险系数

在单项活动服从β分布的前提下，根据之前提到的修正方法，能够确定单项活动的β分布。在给定β分布：

$$
\beta ( a , b , p , q ) = \int _ { a } ^ { b } ( x - a ) ^ { p - 1 } ( b - x ) ^ { q - 1 } \frac { 1 } { ( b - a ) ^ { p + q - 1 } \beta ( p , q ) } d t
$$

在[a，b]范围内，上式的积分值定义为P，即为该单项活动的完工概率[22]

$$
P ( t \leq T ) = \int _ { a } ^ { t } ( x - a ) ^ { p - 1 } ( b - x ) ^ { q - 1 } \frac { 1 } { ( b - a ) ^ { p + q - 1 } \beta ( p , q ) } d t
$$

式中,T表示活动的期望完成时间。

当 $\mathsf { P } { = } 0 . 5$ 时，可计算得到 $\scriptstyle { \mathrm { { t } } = { \mathsf { T } } }$ ，即该活动在时间 $\intercal$ 时概率为 $50 \%$ ；当 $\mathrm { t } { > } \mathsf { T }$ 时，即完工时间在 $\intercal$ 的右侧，根据积分的特性，其概率大于 $\intercal$ 的概率，即 $50 \%$ ；当t<T时，完工时间在 $\intercal$ 的左侧，其概率小于T的概率，即 $1 5 0 \%$ 。

定义 $\mathrm { P _ { e } }$ 为在确定工期内无法完成该活动的概率：

$$
P _ { e } = 1 - P
$$

# 4.3工时修正

# 4.3.1单项活动的工时修正

在PERT技术中，单项活动的工期分布服从β分布。一般情况下，网络时间的计算是基于单项活动完工概率为 $50 \%$ 的情况进行的。而在软件开发项目中，由于其需求变化快，时间压力大，需要协调的项目资源多，涉及的范围广，项目整体风险较高，如果将所有的活动都是按照 $50 \%$ 的完工概率计算显然是不合理的，而如果完工概率低于 $50 \%$ ，由于风险过高，该项目就很难得到管理层的认可，根据经验，通常将完工概率设定为 $7 5 \% - 8 5 \%$ 较为合理。根据β分布对于完工概率及风险系数的定义，在给定单项活动的β分布后，可利用以下步骤对单项活动的工期进行修正。

1、确定该活动的完工概率 $\mathrm { P } _ { 1 }$

2、设立方程

$$
P _ { 1 } = \int _ { a } ^ { t } ( x - a ) ^ { p - 1 } ( b - x ) ^ { q - 1 } \frac { 1 } { ( b - a ) ^ { p + q - 1 } \beta ( p , q ) } d t
$$

3、利用计算机软件(如 mathcad 等)解方程，得到完工概率 $\mathrm { P _ { 1 } }$ 时对应的工期 $\mathbf { t } _ { 1 }$

4、得到 $\Delta t = t _ { 1 } - m$ ;

假设某项活动工期服从β分布，其中 $\mathtt { a } = 3 1$ （天)， $\scriptstyle \mathtt { b } = 8 ($ 天)， $\mathsf { p } { = } 4$ ， $\mathtt { q } = 4$ ，计算得到完工概率 $50 \%$ 的工期为5.5天，取 $\mathrm { P _ { 1 } } = 7 0 \%$ ，计算得到工期为5.9天，因此$\Delta \mathrm { t } = 0 . 4 $ (天)，在实际项目中通常是以整数天作为计划最小单位，可视具体情况对 $\Delta \mathrm { t }$ 取值。

![](images/b6ff93c72ce6fcfd41e28b817a44b057126e374d8a6d52cfc2a1e1636aa01db1.jpg)  
图5单项活动完工概率图  
Fig.5 Single Task Duration Delay

在网络图处理时，可在修正的活动后面增加一道虚拟活动，其完成时间为△t，方差为0，其他计算过程不变。

# 4.3.2多路径汇入的工时修正及路径判断

在随机性网络计划中，并行路线同时汇入一个节点时，若按照经典PERT技术考量，通常仅会对关键路线上的活动进行重点计划和控制，但事实上，除了关键路线之外的其他并行路线对于汇入节点的工期准确性也有很大影响，特别是当其他并行路线的工期与关键路径工期非常接近的情况下，多路径汇入的工期不仅仅由关键路径决定，非关键路径上的活动也会对其产生影响，从而增大其不确定性。通过采用当量概率法对上述情况进行修正，可以有效提高多路径汇入工时估算的准确性。

多路径汇入的工期修正可按以下步骤进行计算：

# 1、计算各路线的期望与方差

设节点j为有多条路径汇入的节点，汇入分流节点的各条路线最临近的分流节点为b，从项目开始的节点b到节点j之间有 $\sf k ( k > 1 )$ 条相互独立的线路。

设某条路线上 $\mathsf { m }$ 项活动的工期分布分别为 $\mathrm { x } _ { 1 } \setminus \mathrm { x } _ { 2 } \setminus \ \dots \ \mathrm { x } _ { \mathrm { m } }$ ，对应的期望工期为 $\mathrm { T } _ { 1 }$ 、 $\mathrm { T } _ { 2 }$ 、..、 $\mathrm { T } _ { \mathrm { m } }$ ，对应的方差为 $\sigma _ { 1 } \cdot ~ \sigma _ { 2 } \setminus ~ \cdots ~ \sigma _ { \mathrm { m } }$ ，显然该条路线的工期分布为$y = x _ { 1 } + x _ { 2 } + x _ { 3 } + \ldots + x _ { m } ,$

该条路线的期望工期为:

$$
T _ { b j } = \sum _ { i = 1 } ^ { m } T _ { i }
$$

该条路线的整合方差为：

$$
{ \sigma _ { b j } } ^ { 2 } = \sum _ { i = 1 } ^ { m } ( \frac { \widehat { \partial } _ { y } } { \widehat { \partial } _ { x } } ) ^ { 2 } \sigma _ { i } ^ { 2 }
$$

# 2、确定主导线路

根据第一步的计算结果，选取从节点b到节点j之间工期持续时间最长的线路作为节点b与节点j的主导线路；若存在 $\mathsf { n } ( \mathsf { n } { > } 2 )$ 条线路持续时间相同且为最长路线，在这n条线路中选取方差最大者作为主导线路。

$$
T _ { b j } ^ { \ c } = \operatorname* { m a x } ( T _ { b j } )
$$

$$
( { \sigma _ { b j } } ^ { c } ) ^ { 2 } = \operatorname* { m a x } ( { \sigma _ { b j } } ^ { 2 } )
$$

2、利用β分布的期望值与方差公式，计算求得各条并行线路的工期分布。

$$
E ( x ) = \frac { a q + b p } { p + q }
$$

$$
\sigma ^ { 2 } ( x ) = \frac { ( b - a ) ^ { 2 } p q } { ( p + q ) ^ { 2 } ( p + q + 1 ) }
$$

根据中心极限定理，当相互独立的活动数量足够多时，其相互叠加的总工期的分布趋向于正态分布，由于在本例的项目中，叠加的单项活动数量较多 $( > 3 0 )$ ，可利用Crystalball 软件对活动进行叠加计算，通过蒙特卡洛仿真，概率分布为正态分布进行拟合。

# 4、应用当量概率法计算修正值

当量概率方法以如下方式在本文中进行应用：假定在节点b和节点j之间存在两条并行路径，其中主导路径的活动相对持续时间的期望值和方差分别为T和σ，则由概率论可得其按期望值完工的概率为 $50 \%$ ；假设非主导线路按该期望值完工的概率为p，而节点j的完工时间同时受到非主导线路与主导线路的作用，线路合成后，该节点的完工概率为 $0 . 5 \mathsf { p }$ 。为降低非主导路线工期对节点b与节点j之间的活动对工期的影响，将主导路线期望值延长 $\Delta \mathrm { T }$ ，在工作时间 $\mathsf { T } { + } \Delta \mathrm { T }$ 处的完工概率为 $50 \%$ 。

![](images/87068e6714a5847292c391da53e7406875152eefeec908e68e9769672179866c.jpg)  
图6多节点汇入修正  
Fig.6 Multi-node import delay

可通过解积分方程得到此时的 $\Delta \mathrm { T }$ ， $\Delta \mathrm { T }$ 的方程如下：

$$
\int _ { \operatorname* { m i n } ( a _ { 1 } , a _ { 2 } , \ldots a _ { k } ) } ^ { t } \beta _ { 1 } ( x , p _ { 1 } , q _ { 1 } ) \int _ { \operatorname* { m i n } ( a _ { 1 } , a _ { 2 } , a _ { k } ) } ^ { t } \beta _ { 2 } ( x , p _ { 2 } , q _ { 2 } ) \ldots \int _ { \operatorname* { m i n } ( a _ { 1 } , a _ { 2 } , \ldots a _ { k } ) } ^ { t } \beta _ { k } ( x , p _ { k } , q _ { k } ) = 0 . 5
$$

$$
\Delta T = t - T _ { b j } ^ { \ c }
$$

其中， $\mathbf { a } _ { 1 } , \mathbf { a } _ { 2 } , \dots , \mathbf { a } _ { \mathrm { k } }$ 为 $\boldsymbol { \mathsf { k } }$ 条子线路的乐观时间取值， $\mathsf { p }$ 和 $\mathfrak { q }$ 为各条子路线的分布参数， $\beta ( \mathrm { x } , \textrm { \textmu } _ { \mathrm { p } _ { \mathrm { k } } } , \textrm { \text q } _ { \mathrm { k } } )$ 为第 $\boldsymbol { \mathsf { k } }$ 条路线的分布函数，利用当量概率法可以得到汇入节点修正时间的取值。

整体网络图修正处理，可在修正活动的后面增加一道虚拟活动，其工作时间为 $\Delta \mathrm { T }$ ，方差为0，其他计算过程不变。

# 4.4本章小结

本章针对软件开发项目活动的完工概率，结合PERT模型的完工概率为 $50 \%$ 这一情况，对单项活动完工概率提出了修正。在多路径汇入的节点，采用当量概率法对相应的节点进行修正。

# 第五章进度风险控制

# 5.1概述

进度风险控制是通过对项目进度中的风险进行识别、预防和控制的一个过程。以软件开发项目为例，在开发过程中会遇到各式各样的风险，风险种类也是多种多种，既有可能来自于外部环境，也可能来自于内部环境，既有可能来自于团队，也有来自于技术实现。当软件项目的需求不断变化，导致项目不断的返工，从而会延长开发周期而拖累进度。当项目利益相关人对于软件项目态度发生变化时，会使软件项目面临被取消的风险。当团队不稳定，项目的技术骨干突然离职，软件开发的进度就会被打乱而导致延期。综合以上的分析，软件开发过程中的风险主要集中在以下几个方面：

1．软件开发项目风险存在的客观性和普遍性

对软件开发项目来说，用到意外情况时有发生的事，这些事在项目管理上成为风险，根据风险的定义，软件开发过程中对项目产生负面影响的因素发生概率的统计计量。一般来讲风险往往由消极的事件组成，在评估风险时，有两个方面的考量：发生的概率以及当风险发生时产生的后果。即：风险 $=$ f(概率，后果)而在开发过程中，经常会遇到的风险主要有以下几类：第一类是团队的风险，团队成员的经验和团队的稳定性，第二类是环境的风险，既可能来自内部，也可能来自外部，第三类是需求的风险，需求在后期变更往往会导致项目的返工，从而给项目的进度带来不利的影响等等，而项目管理人员身处一个面临多种风险的环境，要时刻注意风险发生的诱因，以免风险的发生给项目带来不利的影响。

2．软件开发项目的风险发生的偶然性和必然性

软件开发项目的风险在什么发生往往事前无法估计，但是一旦反生，往往是几种风险因素和其他因素共同作用的结果，以新技术的应用而言，在新技术的应用之前，往往很难评估其对软件开发的影响，但是在应用过程中没有考虑到相关的技术约束和项目所处环境，往往会让新技术在应用过程中风险过高而被迫中途放弃。通过对软件开发项目的历史数据进行分析发现，软件开发过程中的风险往往以概率的形式表现出来，伴有一定的随机性，有时候风险未发生只是环境还未成熟，一旦条件成熟，风险的发生是必然的。经过研究后发现，软件开发过程中的进度风险具有一定程度的运动特征，这使得项目管理者可以通过概率统计等量化风险分析方法来研究软件开发中的进度风险，并计算风险发生的概率和损失程度，从而加强对软件开发进度风险的管理和控制。

# 3．软件开发项目风险的多样性和层次性

软件开发项目往往涉及个几方面的内容，一个是业务，一个是技术，还有一个是团队，对软件开发者来说，在对业务的理解上，往往是通过业务人员的讲解进行学习，这样的学习往往能了解业务的大概而缺少对细节的深入理解，所以一旦对业务的理解出现偏差，就会导致在软件设计上出现问题；而在新技术的应用上，开发人员往往缺少对新技术的了解，只根据自己以往的经验进行应用，但是在实际应用过程中会碰到很多以前从未遇到的问题，如果这些问题没法及时解决，往往会造成进度延期。经过以上分析，在软件开发过程当中风险因素多且种类繁杂，有些风险会在软件开发的全生命周期中有可能发生，而且内在的因素之间的关系错综复杂，风险因素之间也表现出层次性，内因到外因，软件开发中的风险因素从内部到外部都有可能发生。

软件开发项目进度管理的重点工作之一就是寻找合适的风险指标，对项目进行过程中各个活动的进度风险进行预测并在项目实施过程中对风险较大的活动进行风险控制和管理。

# 5.2风险判断指标

作为随机性网络计划，各项活动的工期都存在或大或小的不确定性，因此所有路径都有可能成为关键路径。然而为了确保项目能够按时完成，项目管理者需要识别出对项目进度风险影响最大的活动，也成为关键活动，对组成关键路线的各项活动，项目管理者需要首先予以重视，因为这些活动无论是对工期还是资源的分配来说，起着相当重要的作用。活动关键度指标 ACP(Activity Criticality Index)[23-25] 表示活动落在关键路径上的可能性，可以在随机性网络解决并行路线成为关键路径方面有很好的效果[26-28]。 根据 ACP 的定义，可通过以下过程进行[29-31]:

1、 汇总 PERT网络图中各路径，以及节点对应的所在路径;  
2、 统计各路径成为关键路径的次数M，统计比率 $\cdot { \frac { \bf M } { \bf N } }$ 作为路径关键度的指标;  
3、 加总节点所在路径的关键度作为该节点的关键度指标。

# 5.3本章小结

本章通过对软件开发活动的风险进行了分析，给出了路径关键度和活动关键度作为风险分析指标。

# 第六章基于MC方法编制PERT计划的方法

# 6.1 MC 方法概述

Monte Carlo 是计算机模拟随机现象的一种技术，其理论基础为概率统计学,通过对所求的问题进行建模，建立适当的概率模型，然后通过计算机产生仿真数据，找出其中的规律并进行验证。以软件开发项目为例，通过对其进行建模，仿真一次，相当于项目运行一次，仿真过程中非关键路径也可能成为关键路径，通过多次模拟，通常是10000以上，生成样本量较大的仿真数据，根据仿真的数据结果，找出相应的统计特征，分析相应的风险对项目的影响程度，而模拟的结果往往是带有统计特征的近似解[24]。 但为了得到具有更高精度的数学解，所需抽样计算的次数非常多，如果采用人工抽样计算的方法很难在短时间内完成。

随着计算机技术的不断发展，通过计算机仿真将MonteCarlo 的抽样过程转换为计算机随机抽样的计算过程，大大节约了人工计算的时间，使Monte Carlo 仿真技术在工程项目管理上的广泛应用成为可能[32-37]。

一般情况下，项目进度计划中的MC仿真过程如下所述:

1．给出软件开发项目活动的三时估计值,即最乐观时间a，最悲观时间b 和最  
可能时间m;  
2．确定项目工期的分布类型和分布参数;  
3．确定仿真次数N;  
4．根据活动持续时间的分布，通过计算机抽样得到每个活动的完成时间;  
5．计算项目的整体工期;  
6．重复步骤4-5，得到整体项目工期的序列值(共N 个);  
7．最后对整个项目进行统计分析，得到总体的期望值和标准差。

在本文的 MC 方法仿真中，使用的是 Oracle Crystalball 软件。Oracle Crystalball是一套用于预测、仿真和优化的软件，该软件建立在微软的 Exce 程序上，方便数据的整合；用户可以对各种不同情况下的选择或项目进度计划的分布预测进行仿真和评估;CrystalBall具有非常强大的Monte Carlo 仿真功能，且仿真过程后，计算机会根据仿真情况自动计算相关分布参数并对抽样后的分布情况进行拟合。

# 6.2整体项目进度计划编制

基于 MC方法，将改进 PERT方法应用于软件开发项目计划编制的整体思路及方法，可按照下列步骤展开：

1．根据软件开发项目编制活动网络图;  
2．确定网络图中每个活动的特征值，包括：乐观时间a，对应的完成概率 ${ \bf p } _ { 1 }$   
悲观时间b，对应的完成概率 ${ \tt p } _ { 2 }$ ；最可能时间 $\mathsf { m }$ ，对应的完成概率 ${ \bf p } _ { 3 }$ ：  
3．按照第三章关于单个活动的工期修正方法进行修正，并计算得到每个活动的  
对应β分布函数;  
4．根据4.4对各个活动的期望时间进行修正，得到每个活动的修正时间 $\Delta \mathfrak { t } _ { 1 }$   
5．根据4.11得到所有有两条以上路线汇入的节点的工期修正 $\Delta \mathfrak { t } _ { 2 }$ ；  
6．利用MC方法对整体项目的工期与方差进行仿真；  
7．根据仿真结果计算路径的关键度以及各项活动的的关键度指标ACP;  
8．形成PERT计划并根据计算结果对项目进行管理。

# 6.3本章小结

本章介绍了MC方法，现代计算机技术的发展可以提高计算效率，在给出活动的分布类型和分布参数以及仿真次数，能够计算相应活动的风险指标，所以基于 MC仿真方法，本章给出了软件开发项目的整体项目网络计划编制思路及方法。

# 第七章应用案例

# 7.1概述

在整合 PERT 技术相关理论基础上，通过将相应的优化方法应用于软件开发项目《互联网广告排期系统开发与技术研究》，利用修正的PERT方法进行计划进度研究并最终形成完整的计划进度方案。同时对得到的项目进度计划和实际的结果进行比较分析。通过对各项活动的关键度进行分析，明确了在实际实施过程中需要重点控制的活动及进度安排。

# 7.2案例计算

# 7.2.1项目简介

广告排期是指在一定的时间范围内，为达到一定的广告投放效果而创建的一个媒体投放计划。目前公司在生成媒体排期时主要是通过手动的方式进行的，但是随着广告客户的增多，原先手动生成排期的工作变得非常繁重，而一张排期中由于含有的媒体数量很多导致挑选过程麻烦且耗时较长。为了提高广告在执行过程中的效率，公司需要建立一个自动化排期生成系统。

排期系统主要功能包括广告位的挑选、排期表的生成和审核，通过预先定义的挑选规则，根据相应的投放计划要求，包括预算，广告形式，流量和 KPI等信息，系统会自动生成一张有效的广告排期，排期根据不同的客户要求，可以分为RON 排期和媒体排期。RON 排期又称为子网络排期，该排期主要用于挑选相应的子网络，比如某公司为某个广告需要投放一个女性频道，那么系统会根据这些信息生成一个含有多个子网络的排期。而媒体排期又称为广告位排期，这种排期的特点是针对广告位挑选，在客户已经明确需要在那些子网络上挑选时，系统会相应的挑选相关的广告位来生成一张媒体排期。当排期表生成以后，就需要进行相应的邮件审核，审核通过之后对排期表进行下载。同时排期系统提供相应的外部接口给相关的系统进行调用，随着排期系统的数据增多，需要进行相应的数据导出,这些数据包括广告位和媒体在排期中的使用情况,排期表的KPI导出等等。

排期系统的开发为公司的相关工作人员为广告主生成广告投放排期时，可以快速的挑选出符合要求的媒体方案，保证广告能够及时上线，从而为公司节省了大量的时间和成本，提高了公司运营的效率。

# 7.2.2项目计划编制

根据前面章节对于网络编制的整体思路，对《互联网广告排期系统开发与技术研究》进行进度计划编制，具体步骤如下：

1．根据广告排期系统编制项目网络图(图7):

![](images/b0269310277f7f63b7ffda669ce56a81d846b09f51acbeddcd09c7e1166d1d2d.jpg)  
图7项目网络图

Fig.7 Project Network Map

广告排期系统的活动共有42个，现对其中的一些重要的功能进行简单介绍.其中包括:

1）搭建开发环境：在项目开始之前搭建一个新的开发环境,，包括开发工具、版本管理工具、项目管理工具、web 服务器以及数据库的安装、系统的部署环境、域名解析和域控制系统的安装。  
2）架构设计：考虑到技术的发展，排期系统决定采用asp.net mvc 框架，可以方便程序的扩展，同时采用oracle作为存储的数据库，memcache 做缓存。在业务功能上，排期系统分为提案模块、RON、挑媒体、对内挑媒体以及特殊媒体的功能需要开发，每种排期类型可以单独作为一个模块，但是后台的算法功能可以重用，而asp.netmvc 提供了模型和视图分离的技术框架，方便开发人员进行并行开发。  
3）提案模块设计：提案主要是销售给广告主投放广告的意向性需求，可能转换成为公司的订单．该模块的功能主要包括提案的属性有哪些，界面的设计规范以及怎么样存储数据，以及提案给到排期的接口数据。  
4）挑媒体模块的设计：挑媒体作为排期系统的主要功能之一，主要是用于生成排期时间表，包括媒体表的生成，KPI的计算、媒体的挑选算法，查量算法等等.  
5）审核排期表：排期表单在正式生效之前，需要给排期表的相关人员进行审核,而排期表本身的一些信息将作为审核条件，产生不同的审核流程。  
6)下载排期表：排期表的下载通过 Excel 组件，由系统自动生成excel形式的排期表。系统会为每种排期表生成相应的排期表模板。  
7）调度程序的开发：排期表需要定期生成报表，该任务有调度程序自动完成。

# 2．确定网络图中每个活动的特征值

在任务分解好以后，让负责开发任务的资深工程师为自己所负责的任务提供三个时间的估算值，和传统的三时估算不同，在时间估算的过程中，每个估算人员还需要提供三个时间所对应的完工概率值。其中下表（表1）包括：乐观时间a，对应的完工概率 ${ \bf p } _ { 1 }$ ；悲观时间b，对应的完工概率 ${ \bf p } _ { 2 }$ ；最可能时间 $\mathsf { m }$ ，对应的完工概率 ${ \bf p } _ { 3 }$ 。

表1活动的特征值表  

<table><tr><td>序号</td><td>活动名称</td><td>紧前活动</td><td>乐观时 间/概率</td><td>悲观时间 /概率</td><td>最可能时 间/概率</td></tr><tr><td>1</td><td>搭建开发环境</td><td></td><td>2/0.2</td><td>5/0.2</td><td>3/0.6</td></tr><tr><td>2</td><td>架构设计</td><td>1</td><td>3/0.3</td><td>6/0.3</td><td>4/0.4</td></tr><tr><td>3</td><td>提案模块设计</td><td>2</td><td>1/0.2</td><td>5/0.3</td><td>3/0.5</td></tr><tr><td>4</td><td>RON 排期模块设计</td><td>3</td><td>4/0.2</td><td>10/0.2</td><td>7/0.6</td></tr><tr><td>5</td><td>挑媒体排期模块设</td><td>3</td><td>6/0.3</td><td>12/0.3</td><td>10/0.4</td></tr></table>

<table><tr><td></td><td>计</td><td></td><td></td><td></td><td></td></tr><tr><td>6</td><td>对内排期模块设计</td><td>4</td><td>4/0.2</td><td>10/0.2</td><td>7/0.6</td></tr><tr><td>7</td><td>特殊排期模块设计</td><td>3</td><td>3/0.3</td><td>8/0.2</td><td>5/0.5</td></tr><tr><td>8</td><td>挑媒体排期表基本 信息开发</td><td>5</td><td>2/0.2</td><td>4/0.2</td><td>3/0.6</td></tr><tr><td>9</td><td>挑媒体报价单开发</td><td>5</td><td>1/0.2</td><td>3/0.2</td><td>2/0.6</td></tr><tr><td>10</td><td>挑媒体算法开发</td><td>8，9</td><td>5/0.3</td><td>10/0.2</td><td>8/0.6</td></tr><tr><td>11</td><td>配送算法开发</td><td>10</td><td>2/0.2</td><td>6/0.1</td><td>4/0.7</td></tr><tr><td>12</td><td>查量算法开发</td><td>11</td><td>5/0.2</td><td>10/0.2</td><td>8/0.6</td></tr><tr><td>13</td><td>利润率算法开发</td><td>11</td><td>2/0.3</td><td>6/0.2</td><td>4/0.5</td></tr><tr><td>14</td><td>挑媒体页面媒体表 开发</td><td>12,13</td><td>5/0.2</td><td>10/0.2</td><td>7/0.6</td></tr><tr><td>15</td><td>编辑保存挑媒体排 期表</td><td>14</td><td>1/0.3</td><td>3/0.3</td><td>2/0.4</td></tr><tr><td>16</td><td>RON 排期表基本信 息开发</td><td>4</td><td>1/0.3</td><td>3/0.3</td><td>2/0.4</td></tr><tr><td>17</td><td>RON 排期表报价单 开发</td><td>4</td><td>1/0.3</td><td>3/0.3</td><td>2/0.4</td></tr><tr><td>18</td><td>RON 排期表计算</td><td>16,17</td><td>2/0.3</td><td>4/0.3</td><td>3/0.4</td></tr><tr><td>19</td><td>保存 RON 排期表</td><td>18</td><td>1/0.3</td><td>3/0.3</td><td>2/0.4</td></tr><tr><td>20</td><td>审核 RON 排期表</td><td>19</td><td>5/0.3</td><td>10/0.3</td><td>7/0.4</td></tr><tr><td>21</td><td>拉选 RON报价单</td><td>6</td><td>1/0.2</td><td>3/0.2</td><td>2/0.6</td></tr><tr><td>22</td><td>保存对内排期表</td><td>14</td><td>1/0.3</td><td>3/0.3</td><td>2/0.4</td></tr><tr><td>23</td><td>审核排期表</td><td>22,15</td><td>5/0.3</td><td>10/0.3</td><td>7/0.4</td></tr><tr><td>24</td><td>下载排期表</td><td>20,23,29</td><td>6/0.3</td><td>12/0.3</td><td>8/0.4</td></tr><tr><td>25</td><td>特殊排期的表单开 发</td><td>7</td><td>2/0.2</td><td>4/0.2</td><td>3/0.6</td></tr><tr><td>26</td><td>特殊排期的 NPR 计 算开发</td><td>25</td><td>3/0.2</td><td>7/0.2</td><td>5/0.6</td></tr><tr><td>27</td><td>特殊排期的查量算 法开发</td><td>26</td><td>4/0.2</td><td>8/0.2</td><td>6/0.6</td></tr><tr><td>28</td><td>存储特殊排期功能|27 开发</td><td></td><td>1/0.3</td><td>3/0.3</td><td>2/0.4</td></tr></table>

<table><tr><td>29</td><td>特殊排期的审核流 程开发</td><td>28</td><td>5/0.3</td><td>10/0.3</td><td>7/0.4</td></tr><tr><td>30</td><td>排期列表页面开发</td><td>24</td><td>3/0.3</td><td>6/0.3</td><td>5/0.4</td></tr><tr><td>31</td><td>搜索功能开发</td><td>30</td><td>1/0.2</td><td>3/0.2</td><td>2/0.6</td></tr><tr><td>32</td><td>菜单功能开发</td><td>31</td><td>2/0.2</td><td>4/0.2</td><td>3/0.6</td></tr><tr><td>33</td><td>权限功能开发</td><td>31</td><td>4/0.3</td><td>8/0.3</td><td>6/0.4</td></tr><tr><td>34</td><td>日志功能开发</td><td>31</td><td>2/0.3</td><td>4/0.3</td><td>3/0.4</td></tr><tr><td>35</td><td>证书功能开发</td><td>31</td><td>5/0.3</td><td>10/0.3</td><td>7/0.4</td></tr><tr><td>36</td><td>接口整体设计</td><td>32,33,34,35</td><td>2/0.2</td><td>4/0.2</td><td>3/0.6</td></tr><tr><td>37</td><td>排期表接口开发</td><td>36</td><td>3/0.2</td><td>6/0.2</td><td>4/0.6</td></tr><tr><td>38</td><td>邮件审核接口开发</td><td>36</td><td>2/0.2</td><td>4/0.2</td><td>3/0.6</td></tr><tr><td>39</td><td>导数据接口开发</td><td>36</td><td>2/0.3</td><td>5/0.3</td><td>4/0.4</td></tr><tr><td>40</td><td>调度程序开发</td><td>37,38,39</td><td>5/0.2</td><td>10/0.2</td><td>7/0.6</td></tr><tr><td>41</td><td>性能优化技术研究</td><td>40</td><td>5/0.2</td><td>10/0.2</td><td>7/0.6</td></tr><tr><td>42</td><td>项目总结</td><td>41</td><td>2/0.3</td><td>5/0.3</td><td>3/0.4</td></tr></table>

# 3．单项活动工期分布计算

根据第三章关于单项活动的工期修正方法对单个活动进行修正，并计算得到每个活动的对应β分布函数;利用Mathcad 软件进行计算，按照每个活动的完工概率为 $80 \%$ 计算，得到对应β分布的分布参数p、q，如表2所示。其中，a表示乐观时间，k1表示乐观时间对应的概率，b表示悲观时间，k2表示悲观时间对应的概率， $\mathsf { m }$ 表示最可能时间，k3表示最可能时间对应的概率。

表2单项活动的 $\mathfrak { p / q }$ 值表  

<table><tr><td>序号</td><td>活动名称</td><td>a</td><td>k1</td><td>b</td><td>k2</td><td>m</td><td>k3</td><td>p值</td><td>q值</td></tr><tr><td>1</td><td>搭建开发环境</td><td>2</td><td>0.2</td><td>5</td><td>0.2</td><td>3</td><td>0.6</td><td>4.901</td><td>8.802</td></tr><tr><td>2</td><td>架构设计</td><td>3</td><td>0.3</td><td>6</td><td>0.3</td><td>4</td><td>0.4</td><td>1.758</td><td>2.516</td></tr><tr><td>3</td><td>提案模块设计</td><td>1</td><td>0.2</td><td>5</td><td>0.3</td><td>3</td><td>0.5</td><td>4.081</td><td>4.081</td></tr><tr><td>4</td><td>RON 排期模块设计</td><td>4</td><td>0.210</td><td></td><td>0.2</td><td>7</td><td>0.6</td><td>7. 5</td><td>7. 5</td></tr><tr><td>5</td><td>挑媒体排期模块设计</td><td>6</td><td>0.3</td><td>12</td><td>0.3</td><td>10</td><td>0.4</td><td>2.516</td><td>1.758</td></tr><tr><td>6</td><td>对内排期模块设计</td><td>4</td><td>0.210</td><td></td><td>0.2</td><td>7</td><td>0.6</td><td>7. 5</td><td>7. 5</td></tr><tr><td>7</td><td>特殊排期模块设计</td><td>3</td><td>0.3</td><td>8</td><td>0.2</td><td>5</td><td>0.5</td><td>3.581</td><td>4.872</td></tr><tr><td>8</td><td>挑媒体排期表基本信</td><td>2</td><td>0.2</td><td>4</td><td>0.2</td><td>3</td><td>0.6</td><td>7.5</td><td>7. 5</td></tr></table>

<table><tr><td></td><td>息开发</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td>9</td><td>挑媒体报价单开发</td><td>1</td><td>0.2</td><td>3 0.2</td><td>2</td><td>0.6</td><td>7.5</td><td>7.5</td></tr><tr><td>10</td><td>挑媒体算法开发</td><td>5</td><td>0.310</td><td>0.2</td><td>8</td><td>0.6</td><td>5.697</td><td>4.132</td></tr><tr><td>11</td><td>配送算法开发</td><td>2</td><td>0.2</td><td>6 0.1</td><td>4</td><td>0.7</td><td></td><td>16.08916.089</td></tr><tr><td>12</td><td>查量算法开发</td><td>5</td><td>0.210</td><td>0.2</td><td>8</td><td>0.6</td><td>8. 514</td><td>6.01</td></tr><tr><td>13</td><td>利润率算法开发</td><td>2</td><td>0.3</td><td>6 0.2</td><td>4</td><td>0.5</td><td>4.081</td><td>4.081</td></tr><tr><td>14</td><td>挑蝶体页面媒体衣开</td><td>5</td><td>0.210</td><td>0.2</td><td>8</td><td>0.6</td><td>8.514</td><td>6.01</td></tr><tr><td>15</td><td>编辑保存挑媒体排期</td><td>1</td><td>0.33</td><td>0.3</td><td>2</td><td>0.4</td><td>2.222</td><td>2.222</td></tr><tr><td>16</td><td>排期表基本信息</td><td>1</td><td>0.33</td><td>0.3</td><td>2</td><td>0.4</td><td>2.222</td><td>2.222</td></tr><tr><td>17</td><td>ON排期表报价单开</td><td>1</td><td>0.3 3</td><td>0.3</td><td>2</td><td>0.4</td><td>2. 222</td><td>2.222</td></tr><tr><td>18</td><td>RON排期表计算</td><td>2</td><td>0.3 4</td><td>0.3</td><td>3</td><td>0.4</td><td>2.222</td><td>2.222</td></tr><tr><td>19</td><td>保存RON排期表</td><td>1</td><td>0.3 3</td><td>0.3</td><td>2</td><td>0.4</td><td>2.222</td><td>2.222</td></tr><tr><td>20</td><td>审核 RON排期表</td><td>5</td><td>0.310</td><td>0.3</td><td>7</td><td>0.4</td><td>1. 952</td><td>2.428</td></tr><tr><td>21</td><td>拉选RON报价单</td><td>1</td><td>0.2</td><td>3 0.2</td><td>2</td><td>0.6</td><td>7.5</td><td>7.5</td></tr><tr><td>22</td><td>保存对内排期表</td><td>1</td><td>0.3</td><td>3 0.3</td><td>2</td><td>0.4</td><td>2.222</td><td>2. 222</td></tr><tr><td>23</td><td>审核排期表</td><td>5</td><td></td><td>0.3100.3</td><td>7</td><td>0.4</td><td>1. 952</td><td>2.428</td></tr><tr><td>24</td><td>下载排期表</td><td>6</td><td>0.312</td><td>0.3</td><td>9</td><td>0.4</td><td>2. 222</td><td>2.222</td></tr><tr><td>25</td><td>特殊排期的表单开发</td><td>2</td><td>0.2</td><td>4 0.2</td><td>3</td><td>0.6</td><td>7.5</td><td>7.5</td></tr><tr><td>26</td><td>特发排期的NPR计算</td><td>3</td><td>0.2</td><td>7 0.2</td><td>5</td><td>0.6</td><td>7.5</td><td>7.5</td></tr><tr><td>27</td><td>特殊排期的查量算法 开发</td><td>4</td><td>0.2</td><td>8 0.2</td><td>6</td><td>0.6</td><td>7.5</td><td>7.5</td></tr><tr><td>28</td><td>存储特殊排期功能开</td><td>1</td><td>0.3</td><td>3 0.3</td><td>2</td><td>0.4</td><td>2.222</td><td>2.222</td></tr><tr><td>29</td><td>特发排期的审核流程</td><td>5</td><td>0.310</td><td>0.3</td><td>8</td><td>0.4</td><td>2.248</td><td>1. 952</td></tr><tr><td>30</td><td>排期列表页面开发</td><td>3</td><td>0.3</td><td>6 0.3</td><td>5</td><td>0.4</td><td>2.516</td><td>1.758</td></tr><tr><td>31</td><td>搜索功能开发</td><td>1</td><td>0.2</td><td>3 0.2</td><td>2</td><td>0.6</td><td>7.5</td><td>7. 5</td></tr><tr><td>32</td><td>菜单功能开发</td><td>2</td><td>0.2</td><td>4 0.2</td><td>3</td><td>0.6</td><td>7.5</td><td>7.5</td></tr><tr><td>33</td><td>权限功能开发</td><td>4</td><td>0.3</td><td>8 0.3</td><td>6</td><td>0.4</td><td>2.222</td><td>2.222</td></tr><tr><td>34</td><td>日志功能开发</td><td>2</td><td>0.3</td><td>4 0.3</td><td>3</td><td>0.4</td><td>2.222</td><td>2.222</td></tr><tr><td>35</td><td>证书功能开发</td><td>5</td><td>0.310</td><td>0.3</td><td>7</td><td>0.4</td><td>1. 952</td><td>2.428</td></tr><tr><td>34</td><td>接口整体设计</td><td>2</td><td>0.2</td><td>4 0.2</td><td>3</td><td>0.6</td><td>7.5</td><td>7.5</td></tr><tr><td>37</td><td>排期表接口开发</td><td>3</td><td>0.2</td><td>6 0.2</td><td>4</td><td>0.6</td><td>4.901</td><td>8.802</td></tr><tr><td>38</td><td>邮件审核接口开发</td><td>2</td><td>0.2</td><td>4 0.2</td><td>3</td><td>0.6</td><td>7.5</td><td>7.5</td></tr><tr><td>39</td><td>导数据接口开发</td><td>2</td><td>0.3</td><td>5 0.3</td><td>3</td><td>0.4</td><td>1. 758</td><td>2.516</td></tr><tr><td>40</td><td>调度程序开发</td><td>5</td><td>0.210</td><td>0.2</td><td>8</td><td>0.6</td><td>8.514</td><td>6.01</td></tr></table>

<table><tr><td>41</td><td>性能优化技术研究</td><td>50.2100.280.6</td><td></td><td></td><td></td><td></td><td></td><td>8.514</td><td>6.01</td></tr><tr><td>42</td><td>项目总结</td><td></td><td>20.3</td><td>5</td><td>0.3</td><td>3</td><td>0.4</td><td>1.758</td><td>2.516</td></tr></table>

# 4、 单项活动工期修正计算

根据4.4对各项活动的期望时间进行修正，得到每个活动的修正时间 $\Delta \mathrm { T } _ { 1 }$ 。默认情况下，完成概率为 $50 \%$ ，考虑到项目的实际情况，此处考虑完成概率为 $80 \%$ 。根据实际项目实施经验， $\Delta \mathrm { T } _ { 1 }$ 计算结果小数位小于0.3天的，不进位； $\Delta \mathrm { T } _ { 1 }$ 大于0.4天的，进一位。利用Mathcad软件对各个活动的完成概率进行重新计算，得到各个活动的修正时间，如表3所示。

表3 单项活动的工期修正表  

<table><tr><td>编号</td><td>1</td><td>2</td><td>3</td><td>4</td><td>5</td><td>6</td></tr><tr><td>△Ti计算值</td><td>0.394</td><td>0.831</td><td>0.594</td><td>0.655</td><td>0.755</td><td>0.655</td></tr><tr><td>△T取值</td><td>0</td><td>1</td><td>1</td><td>1</td><td>2</td><td>1</td></tr><tr><td>编号</td><td>7</td><td>8</td><td>9</td><td>10</td><td>11</td><td>12</td></tr><tr><td>△Ti计算值</td><td>0.83</td><td>0.218</td><td>0.218</td><td>0.57</td><td>0.206</td><td>0.482</td></tr><tr><td>△T取值</td><td>1</td><td>0</td><td>0</td><td>1</td><td>0</td><td>1</td></tr><tr><td>编号</td><td>13</td><td>14</td><td>15</td><td>16</td><td>17</td><td>18</td></tr><tr><td>△Ti计算值</td><td>0.594</td><td>0.373</td><td>0.404</td><td>0.404</td><td>0.946</td><td>0.404</td></tr><tr><td>△T取值</td><td>1</td><td>0</td><td>1</td><td>1</td><td>1</td><td>1</td></tr><tr><td>编号</td><td>19</td><td>20</td><td>21</td><td>22</td><td>23</td><td>24</td></tr><tr><td>△Ti计算值</td><td>0.404</td><td>1.229</td><td>0.218</td><td>0.404</td><td>1.229</td><td>1.211</td></tr><tr><td>△Ti取值</td><td>1</td><td>2</td><td>0</td><td>1</td><td>1</td><td>1</td></tr><tr><td>编号</td><td>25</td><td>26</td><td>27</td><td>28</td><td>29</td><td>30</td></tr><tr><td>△Ti计算值</td><td>0.218</td><td>0.437</td><td>0.437</td><td>0.404</td><td>0.716</td><td>0.377</td></tr><tr><td>△T取值</td><td>0</td><td>1</td><td>1</td><td>1</td><td>2</td><td>0</td></tr><tr><td>编号</td><td>31</td><td>32</td><td>33</td><td>34</td><td>35</td><td>36</td></tr><tr><td>△Ti计算值</td><td>0.218</td><td>0.218</td><td>0.807</td><td>0.404</td><td>1.229</td><td>0.218</td></tr><tr><td>△Ti取值</td><td>0</td><td>0</td><td>1</td><td>1</td><td>1</td><td>0</td></tr><tr><td>编号</td><td>37</td><td>38</td><td>39</td><td>40</td><td>41</td><td>42</td></tr><tr><td>△Ti计算值</td><td>0.394</td><td>0.218</td><td>0.831</td><td>0.482</td><td>0.482</td><td>0.831</td></tr><tr><td>△T取值</td><td>0</td><td>0</td><td>1</td><td>1</td><td>1</td><td>1</td></tr></table>

# 5、多节点汇入时间修正计算

根据 4.11得到所有有两条以上路线汇入的节点的工期修正 $\Delta \mathrm { T } _ { 2 }$ ，本案例中共有七处节点有大于1条的路径汇入(活动10、活动14、活动18、活动 23、活动24、活动36、活动40)，因此该七处需要进行 $\Delta \mathrm { T } _ { 2 }$ 的修正计算，如表4所示。

表4 多路径汇入节点的工期修正值表  

<table><tr><td>编号</td><td>10</td><td>14</td><td>18</td><td>23</td><td>24</td><td>36</td></tr><tr><td>△Ti计算值</td><td>1.17</td><td>0.193</td><td>0.77</td><td>0.826</td><td>1.832</td><td>0.147</td></tr><tr><td>△Ti取值</td><td>1</td><td>0</td><td>1</td><td>1</td><td>2</td><td>0</td></tr><tr><td>编号</td><td>40</td><td></td><td></td><td></td><td></td><td></td></tr><tr><td>△Ti计算值</td><td>0.02</td><td></td><td></td><td></td><td></td><td></td></tr><tr><td>△Ti取值</td><td>0</td><td></td><td></td><td></td><td></td><td></td></tr></table>

# 6．工期修正值汇总

在计算完排期系统的每个单项活动的修正工期和多路径汇入节点的工期，对各个活动的修正工期进行汇总，如表5所示。

表5 工期修正值汇总表  

<table><tr><td>序号</td><td>活动名称</td><td>紧前活动</td><td>期望值</td><td>方差</td><td>完工</td><td>工期修</td></tr><tr><td>1</td><td>搭建开发 环境</td><td></td><td>3.125</td><td>0.140625</td><td>3.394</td><td>0.394</td></tr><tr><td>2</td><td>架构设计</td><td>1</td><td>4.214286</td><td>0.413265</td><td>4.831</td><td>0.831</td></tr><tr><td>3</td><td>提案模块 设计</td><td>2</td><td>3.089286</td><td>0. 436543</td><td>3.594</td><td>0.594</td></tr><tr><td>4</td><td>RON排期 模块设计</td><td>3</td><td>7</td><td>0.5625</td><td>7. 655</td><td>0.655</td></tr><tr><td>5</td><td>挑媒体排 期模块设 计</td><td>3</td><td>9.571429</td><td>1. 653061</td><td>10.755</td><td>0.755</td></tr><tr><td>6</td><td>对内排期 模块设计</td><td>4</td><td>7</td><td>0.5625</td><td>7. 655</td><td>0.655</td></tr></table>

<table><tr><td>7</td><td>特块设计</td><td>3</td><td>5.053571 0.645727</td><td></td><td>5.83</td><td>0.83</td></tr><tr><td>8</td><td>挑媒体排 期表基本 信息开发</td><td>5</td><td>3</td><td>0.0625</td><td>3.218</td><td>0.218</td></tr><tr><td>9</td><td>挑</td><td>5</td><td>2</td><td>0.0625</td><td>2.218</td><td>0.218</td></tr><tr><td>10 +1</td><td>挑媒算</td><td>8，9</td><td>8.75</td><td>0.5625</td><td>9.132</td><td>1. 132</td></tr><tr><td>11</td><td>配算法</td><td>10</td><td>3.902778</td><td>0.120563</td><td>4.206</td><td>0.206</td></tr><tr><td>12</td><td>查量算法 开发</td><td>11</td><td></td><td>7.8750.390625</td><td>8.482</td><td>0.482</td></tr><tr><td>13</td><td>利润率算 法开发</td><td>11</td><td>3.9107140.436543</td><td></td><td>4.594</td><td>0.594</td></tr><tr><td>14</td><td>挑媒体页 面媒体表 开发</td><td>12,13</td><td>7.875</td><td>0.390625</td><td>8.373</td><td>0.373</td></tr><tr><td>15</td><td>编辑保存 挑媒体排 期表</td><td>14</td><td>2</td><td>0.183673</td><td>2.404</td><td>0.404</td></tr><tr><td>16</td><td>RON排期 表基本信 息开发</td><td>4</td><td>2</td><td>0.183673</td><td>2.404</td><td>0.404</td></tr><tr><td>17</td><td>RON排期 表报价单 开发</td><td>4</td><td>4.214286</td><td>0.413265</td><td>3.946</td><td>0.946</td></tr><tr><td>18 +1</td><td>RON排期 表计算</td><td>16,17</td><td>4</td><td>0.183673</td><td>3.827</td><td>0.827</td></tr><tr><td>19</td><td>保存RON 排期表</td><td>18</td><td></td><td>20.183673</td><td>2. 404</td><td>0.404</td></tr><tr><td>20</td><td>审核RON 排期表</td><td>19</td><td>7. 214286</td><td>1. 147959</td><td>8.229</td><td>1. 229</td></tr><tr><td>21</td><td>拉选RON 报价单</td><td>6</td><td>2</td><td>0.0625</td><td>2.218</td><td>0.218</td></tr><tr><td>22</td><td>保存</td><td>14</td><td>2</td><td>0.183673</td><td>2.404</td><td>0.404</td></tr><tr><td>23+1</td><td>审核排期 表</td><td>22,15</td><td>8.214286</td><td>1.147959</td><td>8.901</td><td>1.901</td></tr><tr><td>24 + 2</td><td>下载排期</td><td>20,23,29</td><td>11</td><td>1. 653061</td><td>11. 191</td><td>2.191</td></tr></table>

<table><tr><td>25</td><td>特殊排期 的表单开 发</td><td>7</td><td>3</td><td>0.0625</td><td>3.218</td><td>0.218</td></tr><tr><td>26</td><td>特殊排期 的NPR计 算开发</td><td>25</td><td>5</td><td>0.25</td><td>5.437</td><td>0.437</td></tr><tr><td>27</td><td>特殊排期 的查量算 法开发</td><td>26</td><td>6</td><td>0.25</td><td>6.437</td><td>0.437</td></tr><tr><td>28</td><td>存储特殊 排期功能 开发</td><td>27</td><td>2</td><td>0.183673</td><td>2.404</td><td>0.404</td></tr><tr><td>29</td><td>特殊排期 的审核流 程开发</td><td>28</td><td>7.785714</td><td>1. 147959</td><td>8.716</td><td>0.716</td></tr><tr><td>30</td><td></td><td>24</td><td>4.785714</td><td>0.413265</td><td>5.377</td><td>0.377</td></tr><tr><td>31</td><td>搜发功能</td><td>30</td><td>2</td><td>0.0625</td><td>2.218</td><td>0.218</td></tr><tr><td>32</td><td>菜单功能</td><td>31</td><td>3</td><td>0.0625</td><td>3.218</td><td>0.218</td></tr><tr><td>33</td><td>发功能</td><td>31</td><td>6</td><td>0.734694</td><td>6.807</td><td>0.807</td></tr><tr><td>34</td><td>开麦功能</td><td>31</td><td>3</td><td>0.183673</td><td>3.404</td><td>0.404</td></tr><tr><td>35</td><td>证功能</td><td>31</td><td>7. 214286</td><td>1. 147959</td><td>8.229</td><td>1. 229</td></tr><tr><td>36</td><td>接整体</td><td>32,33,34, 35</td><td>3</td><td>0.0625</td><td>3.218</td><td>0.218</td></tr><tr><td>37</td><td>排期表接 口开发</td><td>36</td><td>4.125</td><td>0.140625</td><td>4.394</td><td>0.394</td></tr><tr><td>38</td><td>邮件审核 接口开发</td><td>36</td><td>3</td><td>0.0625</td><td>3.218</td><td>0.218</td></tr><tr><td>39</td><td>导数楼</td><td>36</td><td>3.214286</td><td>0.413265</td><td>3. 831</td><td>0.831</td></tr><tr><td>40</td><td>调度程序 开发 性能优化</td><td>37,38,39</td><td>7.875</td><td>0.390625</td><td>8.482</td><td>0.482</td></tr><tr><td>41</td><td>技术研究</td><td>40</td><td>7.875</td><td>0.390625</td><td>8.482</td><td>0.482</td></tr><tr><td>42</td><td>项目总结</td><td>41</td><td>3.214286</td><td>0.413265</td><td>3.831</td><td>0.831</td></tr></table>

# 7、计算机仿真计算

利用 MonteCarlo 对整体项目的工期和方差进行模拟仿真，得到总工期预测分布。总工期的分布假设为正态分布，通过模拟，发现项目整体工期与正态分布拟合地最好，这和假设是基本吻合的。

![](images/73edf4be5b44c2f17c1e0ca2ca2dec29a2d3e0e2b4be2ec925443edffb134a3c.jpg)  
图8工期分布图  
Fig.8 Frequency View

![](images/6d96ce21b5613cfc27dd79ab24f49fefd9ff81570f604f73990a125eebe82ed9.jpg)  
图9工期预测值

Fig.9 Forecast Chart

根据计算结果，总体工期为 126 天(数值计算 125.712)，该结果为考虑了单项活动的修正完工概率以及多节点汇入的修正时间。相对地，若不考虑多节点汇入的修正时间，则整体工期为 122天(数值计算为 121.712)，完工概率 $50 \%$ 时，计算得到的整体工期为113天。从计算结果看，利用单项活动修正及整体工期修正，预测的工期有所延长，但从进度的风险角度看，项目的整体完工概率得到提高，风险相应地降低了。

# 8、关键度计算和分析

排期系统共有44条路径，路径分布假定为正态分布，根据仿真结果首先计算各路径的关键度指数，然后累加各个活动所在路径的关键度作为活动的风险系数ACP(表6，表7)

表6 排期系统路径表  

<table><tr><td>路径 编号</td><td>路径节点</td></tr><tr><td>1</td><td>1-&gt;2-&gt;3-&gt;5-&gt;8-&gt;10-&gt;11-&gt;14-&gt;15-&gt;23-&gt;24-&gt;30-&gt;31-&gt;35-36-37-&gt;40-&gt;41-&gt;42</td></tr></table>

<table><tr><td>2</td><td>1-&gt;2-&gt;3-&gt;5-&gt;8-&gt;10-&gt;11-&gt;14-&gt;22-&gt;23-&gt;24-&gt;30-&gt;31-&gt;35-36-37-&gt;40-&gt;41-&gt;42</td></tr><tr><td>3</td><td>1-&gt;2-&gt;3-&gt;5-&gt;8-&gt;10-&gt;11-&gt;14-&gt;15-&gt;23-&gt;24-&gt;30-&gt;31-&gt;33-36-37-&gt;40-&gt;41-&gt;42</td></tr><tr><td>4</td><td>1-&gt;2-&gt;3-&gt;5-&gt;8-&gt;10-&gt;11-&gt;14-&gt;22-&gt;23-&gt;24-&gt;30-&gt;31-&gt;35-36-37-&gt;40-&gt;41-&gt;42</td></tr><tr><td>5</td><td>1-&gt;2-&gt;3-&gt;5-&gt;8-&gt;10-&gt;11-&gt;14-&gt;15-&gt;23-&gt;24-&gt;30-&gt;31-&gt;35-36-39-&gt;40-&gt;41-&gt;42</td></tr><tr><td>6</td><td>1-&gt;2-&gt;3-&gt;5-&gt;8-&gt;10-&gt;11-&gt;14-&gt;22-&gt;23-&gt;24-&gt;30-&gt;31-&gt;35-36-39-&gt;40-&gt;41-&gt;42</td></tr><tr><td>7</td><td>1-&gt;2-&gt;3-&gt;5-&gt;8-&gt;10-&gt;11-&gt;14-&gt;15-&gt;23-&gt;24-&gt;30-&gt;31-&gt;33-36-39-&gt;40-&gt;41-&gt;42</td></tr><tr><td>8</td><td>1-&gt;2-&gt;3-&gt;5-&gt;8-&gt;10-&gt;11-&gt;14-&gt;22-&gt;23-&gt;24-&gt;30-&gt;31-&gt;35-36-39-&gt;40-&gt;41-&gt;42</td></tr><tr><td>9</td><td>1-&gt;2-&gt;3-&gt;4-&gt;16-&gt;18-&gt;19-&gt;20-&gt;6-&gt;21-&gt;24-&gt;30-&gt;31-&gt;32-36-37-&gt;40-&gt;41-&gt;42</td></tr><tr><td>10</td><td>1-&gt;2-&gt;3-&gt;4-&gt;16-&gt;18-&gt;19-&gt;20-&gt;6-&gt;21-&gt;24-&gt;30-&gt;31-&gt;32-36-38-&gt;40-&gt;41-&gt;42</td></tr><tr><td>11</td><td>1-&gt;2-&gt;3-&gt;4-&gt;16-&gt;18-&gt;19-&gt;20-&gt;6-&gt;21-&gt;24-&gt;30-&gt;31-&gt;32-36-39-&gt;40-&gt;41-&gt;42</td></tr><tr><td>12</td><td>1-&gt;2-&gt;3-&gt;4-&gt;16-&gt;18-&gt;19-&gt;20-&gt;6-&gt;21-&gt;24-&gt;30-&gt;31-&gt;33-36-37-&gt;40-&gt;41-&gt;42</td></tr><tr><td>13</td><td>1-&gt;2-&gt;3-&gt;4-&gt;16-&gt;18-&gt;19-&gt;20-&gt;6-&gt;21-&gt;24-&gt;30-&gt;31-&gt;33-36-38-&gt;40-&gt;41-&gt;42</td></tr><tr><td>14</td><td>1-&gt;2-&gt;3-&gt;4-&gt;16-&gt;18-&gt;19-&gt;20-&gt;6-&gt;21-&gt;24-&gt;30-&gt;31-&gt;33-36-39-&gt;40-&gt;41-&gt;42</td></tr><tr><td>15 16</td><td>1-&gt;2-&gt;3-&gt;4-&gt;16-&gt;18-&gt;19-&gt;20-&gt;6-&gt;21-&gt;24-&gt;30-&gt;31-&gt;34-36-37-&gt;40-&gt;41-&gt;42</td></tr><tr><td>17</td><td>1-&gt;2-&gt;3-&gt;4-&gt;16-&gt;18-&gt;19-&gt;20-&gt;6-&gt;21-&gt;24-&gt;30-&gt;31-&gt;34-36-38-&gt;40-&gt;41-&gt;42</td></tr><tr><td></td><td>1-&gt;2-&gt;3-&gt;4-&gt;16-&gt;18-&gt;19-&gt;20-&gt;6-&gt;21-&gt;24-&gt;30-&gt;31-&gt;34-36-39-&gt;40-&gt;41-&gt;42</td></tr><tr><td>18</td><td>1-&gt;2-&gt;3-&gt;4-&gt;16-&gt;18-&gt;19-&gt;20-&gt;6-&gt;21-&gt;24-&gt;30-&gt;31-&gt;35-36-37-&gt;40-&gt;41-&gt;42</td></tr><tr><td>19</td><td>1-&gt;2-&gt;3-&gt;4-&gt;16-&gt;18-&gt;19-&gt;20-&gt;6-&gt;21-&gt;24-&gt;30-&gt;31-&gt;35-36-38-&gt;40-&gt;41-&gt;42</td></tr><tr><td>20</td><td>1-&gt;2-&gt;3-&gt;4-&gt;16-&gt;18-&gt;19-&gt;20-&gt;6-&gt;21-&gt;24-&gt;30-&gt;31-&gt;35-36-39-&gt;40-&gt;41-&gt;42</td></tr><tr><td>21 22</td><td>1-&gt;2-&gt;3-&gt;4-&gt;17-&gt;18-&gt;19-&gt;20-&gt;6-&gt;21-&gt;24-&gt;30-&gt;31-&gt;32-36-37-&gt;40-&gt;41-&gt;42</td></tr><tr><td></td><td>1-&gt;2-&gt;3-&gt;4-&gt;17-&gt;18-&gt;19-&gt;20-&gt;6-&gt;21-&gt;24-&gt;30-&gt;31-&gt;32-36-38-&gt;40-&gt;41-&gt;42</td></tr><tr><td>23</td><td>1-&gt;2-&gt;3-&gt;4-&gt;17-&gt;18-&gt;19-&gt;20-&gt;6-&gt;21-&gt;24-&gt;30-&gt;31-&gt;32-36-39-&gt;40-&gt;41-&gt;42</td></tr><tr><td>24</td><td>1-&gt;2-&gt;3-&gt;4-&gt;17-&gt;18-&gt;19-&gt;20-&gt;6-&gt;21-&gt;24-&gt;30-&gt;31-&gt;33-36-37-&gt;40-&gt;41-&gt;42</td></tr><tr><td>25</td><td>1-&gt;2-&gt;3-&gt;4-&gt;17-&gt;18-&gt;19-&gt;20-&gt;6-&gt;21-&gt;24-&gt;30-&gt;31-&gt;33-36-38-&gt;40-&gt;41-&gt;42</td></tr><tr><td>26</td><td>1-&gt;2-&gt;3-&gt;4-&gt;17-&gt;18-&gt;19-&gt;20-&gt;6-&gt;21-&gt;24-&gt;30-&gt;31-&gt;33-36-39-&gt;40-&gt;41-&gt;42</td></tr><tr><td>27</td><td>1-&gt;2-&gt;3-&gt;4-&gt;17-&gt;18-&gt;19-&gt;20-&gt;6-&gt;21-&gt;24-&gt;30-&gt;31-&gt;34-36-37-&gt;40-&gt;41-&gt;42</td></tr><tr><td>28</td><td>1-&gt;2-&gt;3-&gt;4-&gt;17-&gt;18-&gt;19-&gt;20-&gt;6-&gt;21-&gt;24-&gt;30-&gt;31-&gt;34-36-38-&gt;40-&gt;41-&gt;42</td></tr><tr><td>29</td><td>1-&gt;2-&gt;3-&gt;4-&gt;17-&gt;18-&gt;19-&gt;20-&gt;6-&gt;21-&gt;24-&gt;30-&gt;31-&gt;34-36-39-&gt;40-&gt;41-&gt;42</td></tr><tr><td>30</td><td>1-&gt;2-&gt;3-&gt;4-&gt;17-&gt;18-&gt;19-&gt;20-&gt;6-&gt;21-&gt;24-&gt;30-&gt;31-&gt;35-36-37-&gt;40-&gt;41-&gt;42</td></tr></table>

<table><tr><td></td><td>311-&gt;2-&gt;3-&gt;4-&gt;17-&gt;18-&gt;19-&gt;20-&gt;6-&gt;21-&gt;24-&gt;30-&gt;31-&gt;35-36-38-&gt;40-&gt;41-&gt;42</td></tr><tr><td>32</td><td>1-&gt;2-&gt;3-&gt;4-&gt;17-&gt;18-&gt;19-&gt;20-&gt;6-&gt;21-&gt;24-&gt;30-&gt;31-&gt;35-36-39-&gt;40-&gt;41-&gt;42</td></tr><tr><td>33</td><td>1-&gt;2-&gt;3-&gt;7-&gt;25-&gt;26-&gt;27-&gt;28-&gt;29-&gt;24-&gt;30-&gt;31-&gt;32-36-37-&gt;40-&gt;41-&gt;42</td></tr><tr><td>34</td><td>1-&gt;2-&gt;3-&gt;7-&gt;25-&gt;26-&gt;27-&gt;28-&gt;29-&gt;24-&gt;30-&gt;31-&gt;32-36-38-&gt;40-&gt;41-&gt;42</td></tr><tr><td></td><td>35|1-&gt;2-&gt;3-&gt;7-&gt;25-&gt;26-&gt;27-&gt;28-&gt;29-&gt;24-&gt;30-&gt;31-&gt;32-36-39-&gt;40-&gt;41-&gt;42</td></tr><tr><td></td><td>36|1-&gt;2-&gt;3-&gt;7-&gt;25-&gt;26-&gt;27-&gt;28-&gt;29-&gt;24-&gt;30-&gt;31-&gt;33-36-37-&gt;40-&gt;41-&gt;42</td></tr><tr><td>37</td><td>1-&gt;2-&gt;3-&gt;7-&gt;25-&gt;26-&gt;27-&gt;28-&gt;29-&gt;24-&gt;30-&gt;31-&gt;33-36-38-&gt;40-&gt;41-&gt;42</td></tr><tr><td></td><td>38|1-&gt;2-&gt;3-&gt;7-&gt;25-&gt;26-&gt;27-&gt;28-&gt;29-&gt;24-&gt;30-&gt;31-&gt;33-36-39-&gt;40-&gt;41-&gt;42</td></tr><tr><td></td><td>39|1-&gt;2-&gt;3-&gt;7-&gt;25-&gt;26-&gt;27-&gt;28-&gt;29-&gt;24-&gt;30-&gt;31-&gt;34-36-37-&gt;40-&gt;41-&gt;42</td></tr><tr><td></td><td>401-&gt;2-&gt;3-&gt;7-&gt;25-&gt;26-&gt;27-&gt;28-&gt;29-&gt;24-&gt;30-&gt;31-&gt;34-36-38-&gt;40-&gt;41-&gt;42</td></tr><tr><td>41</td><td>1-&gt;2-&gt;3-&gt;7-&gt;25-&gt;26-&gt;27-&gt;28-&gt;29-&gt;24-&gt;30-&gt;31-&gt;34-36-39-&gt;40-&gt;41-&gt;42</td></tr><tr><td></td><td>42|1-&gt;2-&gt;3-&gt;7-&gt;25-&gt;26-&gt;27-&gt;28-&gt;29-&gt;24-&gt;30-&gt;31-&gt;35-36-37-&gt;40-&gt;41-&gt;42</td></tr><tr><td></td><td>431-&gt;2-&gt;3-&gt;7-&gt;25-&gt;26-&gt;27-&gt;28-&gt;29-&gt;24-&gt;30-&gt;31-&gt;35-36-38-&gt;40-&gt;41-&gt;42</td></tr><tr><td></td><td>44|1-&gt;2-&gt;3-&gt;7-&gt;25-&gt;26-&gt;27-&gt;28-&gt;29-&gt;24-&gt;30-&gt;31-&gt;35-36-39-&gt;40-&gt;41-&gt;42</td></tr></table>

在 Excel表中建立各路径对应的期望值、方差以及标准差的表格，预测值列当某路径是关键路径时记为1，否则记为0。期望值列的假设分布为正态分布，根据中心极限定律假定，当路径在活动集合较多的情况下，路径分布假定为正态分布.

表7各路径蒙特卡洛模拟  

<table><tr><td>路径编 号</td><td>期望值</td><td>方差</td><td>标准差</td><td>预测值</td></tr><tr><td>1</td><td>100.8313</td><td>9.785742</td><td>3.128217</td><td>1</td></tr><tr><td>2</td><td>100.8313</td><td>9. 785742</td><td>3.128217</td><td>1</td></tr><tr><td>3</td><td>99.61706</td><td>9.372477</td><td>3.06145</td><td>0</td></tr><tr><td>4</td><td>100.8313</td><td>9.785742</td><td>3.128217</td><td>1</td></tr><tr><td>5</td><td>99.92063</td><td>10.05838</td><td>3.171495</td><td>0</td></tr><tr><td>6</td><td>99.92063</td><td>10.05838</td><td>3.171495</td><td>0</td></tr><tr><td>7</td><td>98.70635</td><td>9. 645117</td><td>3.105659</td><td>0</td></tr><tr><td>8</td><td>99.92063</td><td>10.05838</td><td>3.171495</td><td>0</td></tr><tr><td>9</td><td>83.51786</td><td>9.934949</td><td>3.151975</td><td>0</td></tr><tr><td>10</td><td>82.39286</td><td>7.282207</td><td>2. 698556</td><td>0</td></tr></table>

<table><tr><td>11</td><td></td><td>82.607147. 5548472.74608</td><td></td><td>0</td></tr><tr><td></td><td></td><td>1286.517867.5548472.748608</td><td></td><td>0</td></tr><tr><td></td><td></td><td>1385.392867.876276</td><td>2.80647</td><td>0</td></tr><tr><td></td><td></td><td>1485.607148.227041</td><td>2.868282</td><td>0</td></tr><tr><td></td><td>1583.51786</td><td></td><td>7.403382.720915</td><td>0</td></tr><tr><td></td><td></td><td>1682.392869.9779973.158797</td><td></td><td>0</td></tr><tr><td></td><td>1782.607147.3252552.706521</td><td></td><td></td><td>0</td></tr><tr><td></td><td>1887.732148.3676662.892692</td><td></td><td></td><td>0</td></tr><tr><td></td><td>1986.607148.289541</td><td></td><td>2.879156</td><td>0</td></tr><tr><td></td><td>2086.8214311.293053.360513</td><td></td><td></td><td></td></tr><tr><td></td><td>2185.732147.5117982.740766</td><td></td><td></td><td>0</td></tr><tr><td></td><td>2284.607147.4336732.726476</td><td></td><td></td><td></td></tr><tr><td></td><td>2384.821437.7844392.790061</td><td></td><td></td><td>0</td></tr><tr><td></td><td>2488.732148.1839922.860768</td><td></td><td></td><td>0</td></tr><tr><td></td><td>2587.607148.105867</td><td></td><td>2.84708</td><td>0</td></tr><tr><td></td><td>2687.821438.4566332.908029</td><td></td><td></td><td>0</td></tr><tr><td></td><td>2785.732147.6329722.762783</td><td></td><td></td><td>0</td></tr><tr><td></td><td>2884.607147.5548472.748608</td><td></td><td></td><td>%</td></tr><tr><td></td><td>2984.821437.9056122.811692</td><td></td><td></td><td>0</td></tr><tr><td></td><td>3089.946438.5972582.932108</td><td></td><td></td><td>0</td></tr><tr><td></td><td>3188.8214311.171883.342435</td><td></td><td></td><td>0</td></tr><tr><td></td><td>3289.035718.5191332.918755</td><td></td><td></td><td>0</td></tr><tr><td></td><td>3384.142867.119262.668194</td><td></td><td></td><td>0</td></tr><tr><td></td><td>3483.017867.0411352.653514</td><td></td><td></td><td>0</td></tr><tr><td></td><td>3583.232147.3919012.718805</td><td></td><td></td><td>0</td></tr><tr><td></td><td>3687.142867.7914542.791318</td><td></td><td></td><td>0</td></tr><tr><td></td><td>3786.017867.7133292.777288</td><td></td><td></td><td>0</td></tr><tr><td></td><td>3886.232148.0640942.839735</td><td></td><td></td><td>0</td></tr><tr><td></td><td>3987.142867.7914542.791318</td><td></td><td></td><td>0</td></tr><tr><td></td><td>4083.017867.1623092.676249</td><td></td><td></td><td>0</td></tr><tr><td></td><td>4183.232147. 5130742.740999</td><td></td><td></td><td>0</td></tr><tr><td></td><td></td><td></td><td>4284.142867. 2404342.690805</td><td>。</td></tr><tr><td></td><td></td><td></td><td>4383.017867.1623092.676249</td><td>0</td></tr><tr><td></td><td></td><td></td><td>4487.446438.477362.911591</td><td>0</td></tr></table>

通过仿真模拟，得到各条路径成为关键路径的概率，从表中数据可看出，各路径的概率值各不相同，有些概率为零，而有些概率则大于零，下表列出了各条路径在10000 次模拟过程中成为关键路径的概率。

<table><tr><td>路径</td><td>1</td><td>2</td><td>3</td><td>4</td><td>5</td><td>6</td></tr><tr><td>PCI</td><td>0.18</td><td>0.18</td><td>0.08</td><td>0.18</td><td>0.11</td><td>0.11</td></tr><tr><td>路径</td><td>7</td><td>8</td><td>9</td><td>10</td><td>11</td><td>12</td></tr><tr><td>PCI</td><td>0.05</td><td>0.11</td><td>0</td><td>0</td><td>0</td><td>0</td></tr><tr><td>路径</td><td>13</td><td>14</td><td>15</td><td>16</td><td>17</td><td>18</td></tr><tr><td>PCI</td><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td></tr><tr><td>路径</td><td>19</td><td>20</td><td>21</td><td>22</td><td>23</td><td>24</td></tr><tr><td>PCI</td><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td></tr><tr><td>路径</td><td>25</td><td>26</td><td>27</td><td>28</td><td>29</td><td>30</td></tr><tr><td>PCI</td><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td></tr><tr><td>路径</td><td>31</td><td>32</td><td>33</td><td>34</td><td>35</td><td>36</td></tr><tr><td>PCI</td><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td></tr><tr><td>路径</td><td>37</td><td>38</td><td>39</td><td>40</td><td>41</td><td>42</td></tr><tr><td>PCI</td><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td></tr><tr><td>路径</td><td>43</td><td>44</td><td></td><td></td><td></td><td></td></tr><tr><td>PCI</td><td>0</td><td>0</td><td></td><td></td><td></td><td></td></tr></table>

活动关键度是以路径关键度作为基础的，通过将活动所在的路径相应的关键度进行加总，得到每个活动的关键度，经过汇总，如表9所示。

表9活动关键度计算结果  

<table><tr><td>编号</td><td>1</td><td>2</td><td>3</td><td>4</td><td>5</td><td>6</td></tr><tr><td>ACP</td><td>1</td><td>1</td><td>1</td><td>0</td><td>1</td><td>0</td></tr><tr><td>编号</td><td>7</td><td>8</td><td>9</td><td>10</td><td>11</td><td>12</td></tr><tr><td>ACP</td><td>0</td><td>1</td><td>0</td><td>0</td><td>1</td><td>1</td></tr><tr><td>编号</td><td>13</td><td>14</td><td>15</td><td>16</td><td>17</td><td>18</td></tr><tr><td>ACP</td><td>0</td><td>1</td><td>0.42</td><td>0</td><td>0</td><td>0</td></tr><tr><td>编号</td><td>19</td><td>20</td><td>21</td><td>22</td><td>23</td><td>24</td></tr><tr><td>ACP</td><td>0</td><td>0</td><td>0</td><td>0.58</td><td>1</td><td>1</td></tr><tr><td>编号</td><td>25</td><td>26</td><td>27</td><td>28</td><td>29</td><td>30</td></tr><tr><td>ACP</td><td>0</td><td>0</td><td>0</td><td>0</td><td></td><td>1</td></tr><tr><td>编号</td><td>31</td><td>32</td><td>33</td><td>34</td><td>35</td><td>36</td></tr><tr><td>ACP</td><td>1</td><td>0</td><td>0.13</td><td>0</td><td>0.87</td><td>1</td></tr></table>

<table><tr><td>编号</td><td>37</td><td>38</td><td>39</td><td>40</td><td>41</td><td>42</td></tr><tr><td>ACP</td><td>0.62</td><td>0</td><td>0.38</td><td>1</td><td>1</td><td>1</td></tr></table>

除了前8条路径的的路径关键度不为零之外，其余的路径的关键度都为0,由于风险值过低，可以忽略不计。重点关注前8条路径，其中第1、2和4条路径的 PCI最高，应该给予高度关注，其次是第5、6和8条路径，再次是第3条和第7条路径。

在活动的关键度方面，活动1、2、3、14、23、24、30、31、36、40、41和 42的关键度都为1，风险度较高，原因是这些活动是各条路径必经之节点，所以应该给予重点关注。其次是活15、22、33、35、37和39，这些活动的ACP相对其他节点比较高，风险较高，也应该给予重点关注。其中活动35，即证书开发，由于其实现难度较高，风险也比较高，所以在项目进度控制过程中要给予足够的重视和关注。

# 7.2.3实施情况对比分析

# 7.2.3.1项目实施情况

《互联网广告排期系统开发与技术研究》从2013年8月开始实施，截至到2013 年12月底结束，实际工期共计136天，各项活动的实际完成情况如下表所示(表10)。

表10 实际工期实施时间表  

<table><tr><td>编号</td><td>1</td><td>2</td><td>3</td><td>4</td><td>5</td><td>6</td></tr><tr><td>实施情况</td><td>3</td><td>4</td><td>3</td><td>7號</td><td>10</td><td>7</td></tr><tr><td>编号</td><td>7</td><td>8</td><td>9</td><td>10</td><td>11</td><td>12</td></tr><tr><td>实施情况</td><td>5</td><td>5</td><td>2</td><td>12</td><td>6</td><td>8</td></tr><tr><td>编号</td><td>13</td><td>14</td><td>15</td><td>16</td><td>17</td><td>18</td></tr><tr><td>实施情况</td><td>4</td><td>8號</td><td>2</td><td>2</td><td>3</td><td>3</td></tr><tr><td>编号</td><td>19</td><td>20</td><td>21</td><td>22</td><td>23</td><td>24</td></tr></table>

<table><tr><td>实施情况</td><td>2</td><td>8</td><td>2</td><td>2</td><td>8</td><td>11</td></tr><tr><td>编号</td><td>25</td><td>26</td><td>27</td><td>28</td><td>29</td><td>30</td></tr><tr><td>实施情况</td><td>3</td><td>5</td><td>6</td><td>2</td><td>8</td><td>5</td></tr><tr><td>编号</td><td>31</td><td>32</td><td>33</td><td>34</td><td>35</td><td>36</td></tr><tr><td>实施情况</td><td>2</td><td>3</td><td>6</td><td>3</td><td>8</td><td>3</td></tr><tr><td>编号</td><td>37</td><td>38</td><td>39</td><td>40</td><td>41</td><td>42</td></tr><tr><td>实施情况</td><td>4</td><td>3</td><td>3</td><td>11</td><td>8</td><td>3</td></tr></table>

实际项目实施过程中，作为重点活动的编号9、10、11完成时间均超过了期望值，特别是活动10(排期表算法开发)由于遇到较大的技术难度和数据处理，完成时间较期望值延长了3天，超过预计的最晚时间2天，说明在预测中未完全预见某些活动的技术难度。除了关键路径上的活动较计划有所延误外，在另一项关键技术(调度程序开发)的进展上也遇到了预期之外的困难，因为该活动涉及到并发控制和调度框架的实现等难度较高的技术从而导致相关的路径整体延期10 天。从项目的整体完工时间看，实际的关键路径为：

$1 \cdot > 2 \cdot > 3 \cdot > 5 \cdot > 8 \cdot > 1 0 \cdot > 1 1 \cdot > 1 2 \cdot > 1 4 \cdot > 2 2 \cdot > 2 3 \cdot > 2 4 \cdot > 3 0 \cdot > 3 1 \cdot > 3 5 \cdot 3 6 \cdot 3 9 \cdot > 4 0 \cdot > 4 1 \cdot > 4 2$ ，该路径的实际工期为136天，与修正后的预测工期相比延迟了10天，计划准确率为$9 2 . 0 6 \%$ 。若按照未修正的PERT方法进行预测(不进行单个活动的时间修正，多路径汇入节点时间计算，完工概率 $5 0 \%$ ，则项目将比计划延迟23天，计划准确率为 $7 9 . 6 4 \%$ ，远低于修正后的计划准确率。根据上述分析，本文对于经典PERT方法的修正及改进是适合软件开发项目的实际情况的，具有一定的借鉴意义，能够用于实际软件开发项目的应用，特别是在单项活动完工概率预测、多节点汇入时间修正、路径关键度和活动关键度等方面具有较强的实用性。但是在某些技术难度较大的单个活动工期预测方面仍然存在一定误差，这主要是缺乏相关的经验导致的，在后续的项目管理过程中，应针对这方面的问题继续深入研究，期望能够有所改进。

# 7.3本章小结

本章结合实际案例，从单项活动工期修正、多路径汇入节点的工期修正、整个项目工期的仿真计算、路径关键度和活动关键度以及和实际的进度进行比较分析，得出了修正后的进度计划编制方法在准确率上较修正之前有较大的提高。

# 第八章总结与展望

# 8.1总结

项目的进度计划编制以及对项目实施过程中的进度控制对于项目按时、保质保量完成具有重要意义，因此PERT技术自出现以来就一直是项目进度计划和控制的重要手段之一。从PERT技术本身看，在软件开发项目领域应用时，存在着以下不足:1、三时估算法假设相应的完工概率保持固定不变;2、对于单项活动的工期通常是按照完工概率 $50 \%$ 进行估算，并未考虑活动的风险对于完工概率的影响;3、对于多路径汇入的节点，经典的计划评审技术未考虑；4、路径的关键度和活动关键度目前只考虑了时间限制，并未考虑资源和成本方面的限制;5、未考虑网络图在实施过程中动态调整。

对于软件开发项目而言，由于需求不明确，任务估算难度较大等特点，因此对于项目的计划、组织、协调和控制等方面提出了更高的要求，特别是在项目计划编制时，项目的不确定性极大地影响了进度计划的准确度。

本文针对经典PERT技术在软件开发项目管理中的不足，对PERT技术进行了深入研究，在经典的计划评审技术的相关理论及优化理论基础上，应用于本企业软件开发项目进度计划的编制管理，主要是在以下几方面进行了优化:

1、在三时估算法的基础上增加三点时间的发生概率，并通过计算单个活动的最可能时间和方差，得到单个活动的工期概率分布参数，从而使该分布最大程度体现实际的项目工期分布;  
2、根据研发项目的特点提高完工概率值，对单个活动的完成时间进行修正；  
3、对于有多条路径汇入的节点，根据当量概率方法对节点的完工时间进行修正;  
4、引入路径关键度和活动关键度指标，为各项活动实施过程中的资源分配提供指导依据。

# 8.2本文的不足及展望

在实际的软件开发项目《互联网广告排期系统开发与技术研究》应用过程中发现，改进 PERT方法有以下不足：对于技术难度比较大的活动工期，在时间的估计准确性方面仍然存在一定不足之处；在路径关键度和活动关键度的计算方面，只考虑了相互独立的活动和路径，对于相互影响的活动，并未考虑。因此对于软件开发项目进度编制的精确度还有一定提高的空间，下一步将继续对 PERT 方法进行研究，积累更多的活动相关数据，优化PERT模型，使PERT技术不论在软件开发项目还是项目进度管理本身都能够得到更加广泛的应用。

#

参考文献[1] (美)项目管理协会著王勇 张斌译，项目管理知识体系指南(PMBOK指南）第四版，电子工业出版社 2009-5-1;[2](美)哈罗德.科兹纳著 杨爱华 王丽珍等译 项目管理 计划、进度和控制的系统方法(第10版)，电子工业出版社2013-09;[3]Herrer' as-Velasco, Jos é Manuel ;Herreri'as-Pleguezuelo Rafael; Revisiting the PERT mean and variance, European Journal of Operational Research,2011,210(2), 448-451[4] T.K.Litlefield, JR. AND PH.Randolph An Answer To Sasieni’s Question On PERT Times, Management Science,1987,33(10), 1357[5]郭富才，黄辉，项目管理工作机构分解（WBS）方法及其准则研究，管理工程学报，2002：49-51[6] 蔡晨;万伟 基于PERT/CPM的关键链管理[J]，中国科学管理 2013.12;[7] Sasieni MW, A note on PERT times，Management Science,1986,32,1652-1653[8]李平，顾心一，PERT网络工期风险计算方法的研究[J]，统计与决策，2004，17(5): 16-17[9]杨小迪，应用 PERT法进行项目工期估计[J]，中国港湾建设，2004（2)：61-63[10]潘春光，陈英武，汪浩，模糊计划评审技术 F-PERT 中关键路径的规划解法[J]，数学的实践与认识，2004（8)：29-34[11]李春阳，周国华等，模糊网络计划技术发展综述[J]，工程建设与设计，2004 (3): 53-54[12]刘士新，王梦光等，资源受限工程调度问题的优化方法综述[J]，控制与决策，2001（增刊)：647-651[13]刘明等,资源有限一工期最短的分枝定界算法[J],系统工程,1999(4)：73-75[14]颜钢锋等，采用混合单亲遗传算法求解一类资源一时间优化问题[J],系统工程理论与实践，2001（4)：75-79[15]蔡晨，万伟，基于PERT/CPM的关键链管理[J]，中国管理科学，2003(12): 35-39

[16]杨铭等，基于改进的计划评审技术的航空项目风险评价技术研究[J],  
计算机集成制造系统，2008（14)：192-196[17]段建中，李民奎，PERT 行为时间参数近似计算公式选优[J]，合肥工  
业大学学报，2001（24)：366-369[18]文平，计划评审技术的改进，数学的实践与认识[J]，2007（37)：5-10[19]Sasieni MW, A note on PERT times[J], Management Science， 1986， 32:  
1652-1653[20]张猛，张洪才,Monte Carlo 模拟在施工进度方案综合评价中的应用[J],  
建筑技术开发，2004（4)：86-88[21]孙东川，杨立洪，钟拥军，管理的数量方法[M]，北京，清华大学出版  
社，2005:89-90[22]李兵，用网络模型 PERT 技术对工程项目工期进度进行风险评价[J],  
建筑施工，2005(5),120-123[23] Salah E Elmaghraby, On criticality and sensitivity in activity networks[J],  
European Journal of Operational Research, 2000(127), 220-238[24] Van Slyke, Richard M. Monte Carlo Methods and the PERT Problem  
Operations Research, 1 September 1963, 11(5), 839-860[25] Salah Eldin Elmaghraby, 1927- Activity networks; project planning and  
control by network models. New York, Wiley 1977, 20-40[26] T. M. Williams Criticality in Stochastic Networks Journal of the Operational  
Research Society, 1992, 43(4), 353[27] Monhor, D A new probabilistic approach to the path criticality in stochastic  
PERT Central European Journal Of Operations Research, 2011 Dec, 19(4), 615-633[28] Cho,Jg , Yum, Bj, An uncertainty importance measure of activities in PERT  
networks International Journal Of Production Research, 1997 Oct, 35(10), 2737-2757[29] Basjis M.Dodin, Salah E.Elmaghraby Approximating the Criticality Indices  
of the Activities in PERT Networks 1985, 31(2), 207-223[30] James E.Kelly, Jr, Critical-Path Planning and Scheduling: Mathematical  
Basis 1961, 9(3), 296-320[31] J.J.Martin, Distribution of the Time Through a Directed, Acyclic Network  
1965, 13(1), 46-66[32] R.A.Bowman, Eficient Estimation of Arc Criticalities in Stochastic Activity  
Networks 1995, 41(1), 58-67[33] Charles E.Clark, The Greatest of a Finite Set of Random Variables 1961,53

9(2), 145-162

[34] Sabeghi, Narjes , Tareghian, Hamed ,Demeulemeester, Erik ， etc Determining the timing of project control points using a facility location model and simulation Computers & Operations Research, Sep 2015,61, 69 [35] Peng, Wuliang ; Huang, Min A critical chain project scheduling method based on a differential evolution algorithm International Journal of Production Research, 2014, 52(13), 3940-3949 [36] Batselier, Jordy ; Vanhoucke, Mario Construction and evaluation framework for a real-life project database International Journal of Project Management, April 2015, 33(3), 697-710 [37] Jianxun Qi, Dedong Sun, Zhixiong Su Simplification of Large Scale Network in Time-Cost Tradeoff Problem International Journal of Advancements in Computing Technology, 2013, 5(4), 938-945 [38] D.G.Malcolm, J.H.Roseboom, C.E.Clark, Application of a Technique for Research and Development Program Evaluation, 1959, Vol.7(5), 646-669

# 致谢

研究生学习生涯即将结束，回首过去的三年历程，心里感慨万千。每周末都要风雨无阻地去上课，放弃了无数的节假日，即使工作再繁忙也要完成老师布置地任务，和同学一起熬夜攻克难关。在论文即将结束之际，特向所有关心过我的老师、同学、同事、朋友和亲人们致以诚挚的谢意。

首先，衷心感谢我的导师，在课题研究和论文编写的过程中，导师施亮教授给予了我极大的帮助与支持。施亮教授学识渊博、治学严谨，从课题开始就从繁忙的科研、教学工作中抽出时间对我进行指导。在整个研究过程中，施老师提供了很多建设性的意见，对课题研究的完成与论文的编写起到了至关重要的作用。施老师的创新思维与严谨细实的工作中给我留下了深刻的印象，也将成为我今后的科研、工作、学习的标杆与榜样。在此对施老师的悉心指导致以衷心的感谢。

其次，我得到了很多老师、同学以及同事的帮助，在此我向他们表示衷心的感谢。

最后，我要感谢我的家人，在我论文写作期间，给予我悉心的照顾，在我遇到困难和挫折时给与我鼓励，使我能够继续前行，衷心地感谢你们，你们是我努力与前进的动力。

# 攻读硕士学位期间发表的学术论文

# 已刊登发表

[1] 蔡春升,IT软件研发项目中的沟通管理,项目管理技术增刊,2014年（上),243-246
# 学 位 论 文

# 国网 S 公司办公信息化平台外包项目进度管理研究

作 者 姓 名 ： 胡一帆  
作 者 学 号 ： 2180370  
指 导 教 师 ： 侯卉 教授东北大学工商管理学院  
申请学位级别： 硕士 学 科 类 别 ： 管理学  
学科专业名称： 工商管理（专业学位）  
论文提交日期： 2023 年 11 月 21 日 论文答辩日期： 2023 年 12 月 3 日  
学位授予日期： 2024年1 月 答辩委员会席：赵晓煜 教授  
评 阅 人 ： 冯国奇 副教授 高振明 高级经济师

# A Thesis in Master of Business Administration

# Research on Progress Management of Office Information Platform Outsourcing Project of State Grid S Company

By Hu Yifan

Supervisor: Professor <PERSON><PERSON> Hui

# Northeastern University December 2023

# 独创性声明

本人声明，所呈交的学位论文是在导师的指导下完成的。论文中取得的研究成果除加以标注和致谢的地方外，不包含其他人己经发表或撰写过的研究成果，也不包括本人为获得其他学位而使用过的材料。与我一同工作的同志对本研究所做的任何贡献均已在论文中作了明确的说明并表示谢意。

学位论文作者签名：

$$
\frac { 1 } { 6 } E = 4 - i
$$

日 期：2023 年 12 月 29 日

# 学位论文版权使用授权书

本学位论文作者和指导教师完全了解东北大学有关保留、使用学位论文的规定：即学校有权保留并向国家有关部门或机构送交论文的复印件和磁盘，允许论文被查阅和借阅。本人同意东北大学可以将学位论文的全部或部分内容编入有关数据库进行检索、交流。

作者和导师同意网上交流的时间为作者获得学位后：

半年 □ 一年□ 一年半□ 两年□学位论文作者签名： $+ \alpha - \gamma \alpha$ 导师签名：签字日期：2023年12月29日 签字日期：2023年12月29日

# 摘 要

随着全球经济一体化的发展，各行业分工深化加剧，当前，软件外包行业经过多年的发展，已经形成了成熟的经济体系，早已打破了单一业务模式，逐渐的形成了战略合作关系模式。对于软件外包项目来讲，软件的开发工作由软件开发企业负责，软件发包方与承包方如何在项目实施过程中进行分工合作，加强项目进度管理，从而使软件外包项目按照进度计划完成，是软件发包方需要重视和予以解决的关键问题。

论文以国网S公司办公信息化平台外包项目为研究对象，首先，对进度管理、外包项目管理和软件开发项目管理等相关的研究和文献进行了阅览和总结，对项目管理、进度管理相关的理论、方法进行了梳理，为论文的写作奠定了理论基础。其次，总结了国网 S 公司办公信息化平台外包项目的基本情况，对该项目的主要内容和实施流程进行了分析，为论文的写作提供了现实依据。再次，编制项目进度计划，深层次的分解项目工作结构，明确项目各活动存在的内在逻辑关系，进行分解，通过三时法来对项目活动时间进行精准的估算，并基于此确定项目总工期，以及对应的关键路径。根据前期的计划内容和工期，制定项目进度控制措施，明确进度控制流程，明确了项目进度监测与报告的方法，给出了项目进度分析和进度纠偏的措施。最后，为了确保顺利的落实项目进度计划和控制措施，从人力、制度、组织和技术层面制定相应的保障措施。

基于论文的研究，为国网 S 公司办公信息化平台外包项目的进度管理提供了依据和支撑，对于该项目能够按照计划完成并投入运行有着重要的现实意义，同时也对其他软件外包项目的进度管理提供了参考。

关键词：办公信息化平台；进度管理；进度计划；进度控制

# Abstract

With the development of global economic integration, the division of labor in various industries has deepened. At present, after years of development, the software outsourcing industry has formed a mature economic system, which has already broken the single business model and gradually formed a strategic cooperative relationship mode. For software outsourcing project, the software development work by the software development enterprise, how the contractor and the contractor in the process of project implementation cooperation, strengthen the projects schedule management,so that the software outsourcing project completed according to the schedule, is the software employer needs to pay attention to and solve the key problems.

Paper to its S company office information platform outsourcing project as the research object, first of all, the schedule management, outsourcing project management and software development project management and other related research and literature reading and summary, the project management, schedule management related theory, methods, laid the theoretical foundation for the paper writing. Secondly, it summarizes the basic situation of the outsourcing project of the office information platform of State Grid S Company, and analyzes the main content and implementation process of the project, which provides a realistic basis for the writing of the paper. Thirdly, the project schedule plan is compiled, the project work structure is deeply decomposed, the internal logical relationship of each activity of the project is defined, decomposed, and the project activity time is accurately estimated through the three-time method, and the total project duration and the corresponding key path are determined based on this. According to the plan content and construction period in the early stage, the project progress control measures are formulated, the progress control process is defined, the methods of project progress monitoring and reporting are defined, and the measures of project progress analysis and progress correction are given. Finally, in order to ensure the smooth implementation of the project schedule and control measures, the corresponding safeguard measures are formulated from the aspects of manpower, system, organization and technology.

The research based on the paper provides the basis and support for the progress management of the outsourcing project of the office information platform of State Grid S Company, which is of great practical significance for the project to be completed and put into operation according to the plan, and also provides a reference for the progress management of other software outsourcing projects.

Key words:Office information platform; Progress management; Schedule; progress control

# 目录

独创性声明.  
摘 要 ...  
Abstract ...

第1 章 绪论.

1.1 研究背景及意义..

1.1.1 研究背景..  
1.1.2 研究意义.

1.2 国内外研究现状.

1.2.1 国外研究现状.  
1.2.2 国内研究现状.

1.3 研究内容与方法..

1.3.1 研究内容.. 6  
1.3.2 研究方法..

1.4 论文结构. .8

# 第2 章 概念界定及理论基础. . 10

2.1 相关概念. .10

2.1.1 办公信息化平台. .10  
2.1.2 进度管理. .10

# 2.2 项目管理理论. 10

2.2.1 项目管理理论发展.. 10  
2.2.2 项目管理理论的量化管理.

2.3 项目进度管理内容和主要方法. 13

2.3.1 项目进度管理的内容. 13  
2.3.2 项目进度管理主要方法. 14

# 第 3 章 国网 S 公司办公信息化平台外包项目基本情况.............. 16

3.1 项目概述. 16

3.1.1 公司简介. .16  
3.1.2 项目简介. 17  
3.1.3 项目管理组织机构 18  
3.1.4 项目总体目标.. .20  
3.2 项目主要内容及实施流程. .20  
3.2.1 项目主要内容. .20  
3.2.2 项目实施流程. .22

# 第 4 章 国网 S 公司办公信息化平台外包项目进度计划编制......24

4.1 项目工作结构分解及逻辑关系确定. .24

4.1.1 项目工作结构分解. .24  
4.1.2 确定项目活动逻辑关系 .29

4.2 项目里程碑计划及时间估算. .30

4.2.1 项目里程碑计划. .30  
4.2.2 项目时间估算. .31

4.3 关键路径的确定及项目总工期. .35

# 第 5 章 国网 S 公司办公信息化平台外包项目进度控制措施......37

5.1 项目进度控制流程.. .37  
5.2 项目需求变更及文档管控. 37  
5.2.1 项目需求变更管控.. .37  
5.2.2 项目文档管理.. .38  
5.3 项目进度监测与报告 ..40  
5.3.1 项目进度监测.. ..40  
5.3.2 项目进度报告.. ..40  
5.4 项目进度分析与纠偏. ..41  
5.4.1 项目进度分析.. ..41  
5.4.2 项目进度纠偏. .41

# 第 6 章 国网 S 公司办公信息化平台外包项目进度管理保障措施

# .43

6.1 组织保障.. ..43

6.1.1 成立S公司项目领导小组. 43  
6.1.2 明确各参与方责任. ..44

6.2 制度保障.. ..45

6.2.1 建立健全项目管理制度. .45

# 6.2.1 明确需求变更管理流程. ..46

# 第7 章 结论与展望.. . 49

# 参考文献.. ..51

# 致谢 ..... .55

# 第 1 章 绪论

# 1.1 研究背景及意义

# 1.1.1 研究背景

企事业单位的信息化程度是其与时俱进的重要标志，而作为推动信息化快速发展的基础性软件——办公信息化平台，是面向大多数人的应用软件，能为单位管理人员、办公人员提供良好的办公帮手，使单位的信息化建设不断完善。通过信息化平台的应用可以实现服务的便捷化和办公自动化，能够对当前的组织管理结构进一步优化，并逐步的完善管理体制，从而提升整体的协同办公能力，从而使单位的管理流程更加清晰，减少了人力，降低了错误率。

国网 S公司的办公管理系统经过两个阶段的发展变革：2013年前，纸质化办公，2013 年，国网 S 公司购买了一套 OA 系统，通过该系统可以实现文件的传输和审批、员工请假和出差的审批、会议通知等工作，在该系统中，文件的传输、审阅等仍要基于 OFFICE 等办公软件，同时需要在电脑端进行操作，利用该系统办公，要求领导和员工必须要在办公室才能使用，往往是需要领导审批的事项需要等待或通过电话等方式进行提醒，这也就导致了在拥有该办公自动化系统的同时，经常出现员工直接用纸质文件流转的情况，久而久之，该系统已经成为了摆设。这种“混合办公模式”极大地浪费了系统和人力资源，从公司战略需求层面分析，此类办公模式远不能够满足发展服务的需求，也无法有效的推进公司信息化公办的发展。公司需要对当前的办公模式进行变革，充分的利用信息化技术手段，构建形成一套符合自身的、科学系统化的办公信息化平台，以能够满足公司信息传递、移动办公、在线审批等自动化、信息化办公的高效发展。

2023 年，经过国网 S 公司的研究决定，通过招标的方式开发国网 S 公司办公信息化平台，通过前期走访和市场调查，决定该项目要在一年时间内完成并上线使用，同时该项目需要大量资金投入，涉及到国网 S 公司各部门、各单位及其相关人员，所以，在项目实施过程中必须要进行科学的、有效的进度管理，否则难以保证项目在计划时间内完成，并造成财物资源、人力资源的浪费。

# 1.1.2 研究意义

（1）理论意义

论文通过对进度管理、软件开发、办公信息化平台等相关研究和文献的研阅，对进度管理相关理论进行梳理和分析，以国网 S 公司办公信息化平台外包项目为具体研究对象，编制该项目的进度计划、制定该项目进度控制措施，丰富了进度管理理论的研究成果，对于进度管理理论的发展和实践有着一定的理论意义。

# （2）实践意义

论文以国网 S 公司办公信息化平台开发项目为研究对象，通过对项目情况的梳理和总结，根据项目实际特点编制项目进度计划，为了保证项目进度计划的有序实施，制定进度控制措施，为该项目的管理提供理论依据，对于保障项目按照进度计划完成并上线使用有着重要的实践意义。

# 1.2 国内外研究现状

# 1.2.1 国外研究现状

（1）进度管理方面的研究

Nazimko和Zakharova（2023）指出，项目加速是一个重要的工具，它可以防止项目进度拖延后的损失风险，同时他们设计了一个迭代崩溃算法(AIC)，该算法不仅涉及传统的时间、成本、资源参数，考虑了项目进度计划的结构、关键路径(CP)的数量，而且构建了两个辅助指标来选择关键活动和控制进度加急过程[1]。AcebesFernando、Poza David 和 Gonzalez-Varona Jose Manuel（2022）等指出，EDM 和EVM 有一个关键的区别：在 EDM 中，活动的值表示为工作周期；而在 EVM 中，价值是以成本来表示的。作者用一个真实的案例比较了EVM 和ESM对PSM的适用性[2]。Ansari Ramin、Khalilzadeh Mohammad 和 Hosseini M（2021）等提出了一种确定工程建设项目时间缓冲区大小的多目标方法，将问题表述为两阶段随机规划模型。结果证明了提出的多目标时间缓冲方法在实际情况下的有效性[3]。Yu Z和Hui D（2020）从进度管理理论层面出发，并以关键链方法为基础，研究构建出了一种项目进度管理模型，在这一模型对工期估算中，考虑了项目不确定性，将每个过程的安全时间纳入统一管理[4]。Zou J、Wang H 和 Lei Y（2019）指出，建筑信息模型的出现极大地方便了施工，提高了施工效率，但在管理阶段的应用较少。从一个独特的角度分析了建筑信息建模在管理阶段的实用性以及推广建筑信息建模的优势[5]。Suci Miranda 和 M Sugarindra（2019）以某航空工业资源受限项目调度为例进行研究，利用项目管理软件（PMS），12 名操作人员在 91 天内完成项目，并认为Microsoft Project 是进一步评估和管理项目调度方案的首选。对数据进行自动化处理，并应用水准测量方法压缩完工项目时间，利用重新安排的进度计划，可以快速完成项目[6]。

（2）外包项目管理方面的研究

Mu Rui、Wu Peiyi 和 Haershan Maidina（2023）基于一个智慧城市外包项目的案例研究，介绍了一个前伸式关系治理框架，该框架抓住了关系培育的契约前维度及其在促进正式缔约方面的作用。作者指出，项目的合同前阶段，当项目充斥着各种不确定性时，关系治理有很大的空间。公共和私人当事方可以采取措施，在签署正式合同之前建立信任、促进灵活性和建立相互依存关系[7]。Li X、ChenY和 Sun Y（2019）等根据科研外包项目的特点和现状，通过加强对科研招标的控制，探索建立科研外包项目过程控制机制和信用评价机制，科研项目的合同管理、实施和验收，形成科技部统筹、各职能部门和专业中心分工协作的协调监督机制。使外包项目的研发过程更加规范和高效，提高了外包项目的管理质量和研究水平[8]。Al Azad Samim、Mohiuddin Muhammad 和 Su Zhan（2022）利用结构方程模型（SEM）对 153 个 ITO 项目的调查数据进行分析，结果表明，组织态度显著影响知识共享和伙伴关系质量，进而影响 ITO 项目的成功[9]。Gupta Rajorshi Sen（2021）指出，创新外包给客户企业带来了两个关键风险。第一种风险发生在客户向服务提供商披露其技术诀窍和知识产权（IP），但服务提供商将知识产权用于自身商业利益时。第二，在缺乏完美监控的情况下，服务提供商可能对创新项目做出次优的努力。在创新外包中，客户技术的管理和代理人的创新努力都至关重要。它表明，客户可以通过设计适当的合同来阻止这些风险[10]。Large Rudolf O、Pache Gilles和 Merminod Nathalie（2021）对德国 201 名管理人员进行了调查，试图验证研究问题。数据分析基于虚拟回归。结果证实，管理者的态度、社会压力感知和行为控制感知是参与意向的三个驱动因素，而基本的个人特征对管理者意向的强度和方向没有直接影响[11]。

# （3）软件开发项目方面的研究

Kim SangYoon、Cho NamWook 和 Yoo MyungSik（2023）等介绍了医用麻醉药品使用监测信息管理系统（NIMS）的开发过程及其实施效果。除功能性需求外，非功能性需求包括隐私性、可用性、连通性、互操作性和数据完整性。给出了系统的实体关系图设计及其实现过程[12]。Scott E和Campo M（2023）在研究中提出了一种在 Scrum 中培训开发人员的自适应方法，研究结果描述了如何在软件工程领域中使用自适应虚拟学习环境[13]。Di F R、Mayes Ma L 和 Richard R M（2023）

等研究了软件基础设施需求和投资，着眼于充分利用百亿亿次资源，为下一代科学问题和科学发现提供独特的计算工具[14]。Kassaymeh S、Abdullah S 和 Al B M A（2023）等的研究开发了几种由反向传播神经网络（BPNN）组成的ML 模型。用于替代传统的网络训练方法并解决其局限性[15]。Yue Mingyu 和 Feng Haiyan（2021）针对传统的基于原型系统的需求分析方法在软件开发中存在的描述不完整、歧义性和不一致性等问题，提出了一种基于原型画像的需求分析方法，介绍了根据用户需求利用原型画像方法获取软件需求规格说明并进行软件设计的过程，并对该方法的过程进行形式化描述，以客户认可的原型画像需求文档进行软件开发，从而保证所设计的功能需求的正确性。实践结果表明，基于原型画像的需求分析方法克服了传统原型系统需求分析方法存在的开发周期长、故障率高等问题，提高了软件开发的质量和周期[16]。

# 1.2.2 国内研究现状

（1）进度管理方面的研究

杨凯（2022）运用管理学工具进行工程进度管理的研究，对工程进度管理计划的步骤和方法、工程进度分析方法、工程进度控制与优化方法进行了系统的梳理和分析[17]。吴云峰、王科文和董鹏（2022）以某型号舰船小修项目为研究对象，通过 Project 2016 项目管理软件的可视化功能制定计划，运用 Project 2016 项目管理软件对舰船小修项目进行进度管理，通过现代软件严格控制进度[18]。徐希鹏（2023）指出，传统的进度管理方法很难满足施工管理的要求，BIM 技术的使用，实现了信息技术与实体工程管理的对接，文章从传统进度管理中的不足、BIM 技术的融合点及应用过程等方面展开论述[19]。刘伟煜、王磊和张虎（2023）等将机器学习算法引入到 IT 项目进度管理领域，基于历史指标数据构建模型，设计了针对项目进度的评分卡模型。通过以上模型可以利用当前项目的各项数据得到进度量化评分指标，用于指导 IT 项目进度管理工作。结果表明，该系统能够充分挖掘项目进度管理实施过程中产生的指标数据的潜在价值，提升项目进度管理效率[20]。杨秋林（2022）选择了J 市农村人居环境综合监管平台项目，基于工作分解结构来梳理分解了项目各项工作，并且利用三点估算法来估算了整个项目的工期，识别出关键路径，对于项目实施的缓冲区消耗，采用相对监控法来进行监测，从而更好的支持对项目实施进度的控制[21]。赵思奇（2022）以 XX 公司 A游戏项目为研究对象，通过 WBS 法的运用，把工作细分为若干个基础单元，根据任务前后逻辑关系，预估出任务时间，通过计算得出期望时间之后根据关键路径对任务计划进行分析。在项目实施阶段对关键路径内容以及里程碑事件进行控制。运用缓冲区的方法对项目进度的制定与控制做出调整，以解决之前其他项目出现的项目延期等问题[22]。马逸宇、蒋黎晅和陈江红（2022）等采用扎根理论方法进行定性研究，分析收集到的访谈资料，编码得出七大主要因素，并提出现代综合医院项目进度管理模型。现代综合医院项目中的传统进度管理包含了“资源供应”“协调不足”和“外部环境”3 个影响因素[23]。吴建新（2023）首先阐述了房地产建筑项目进度管理的内涵，然后分析了房地产建筑项目进度管理中存在的问题，最后提出了几条房地产建筑项目进度管理的有效策略，为房地产项目进度管理提供参考和借鉴[24]。叶明珠、汪明和彭礼勇（2023）针对传统地铁施工进度管理方式下数据间协同效率低、缺乏形象化表达等问题，将建筑信息模型技术与地铁工程分部分项相结合，提出了基于 BIM 的地铁工程施工进度管理方案[25]。李含宇（2022）指出，计划评审技术是普遍使用的一种非确定型进度计划方法，属于前馈控制，主要利用得到的最新信息进行预测，通过将计划目标与预测情况相比较，使得预测结果与计划目标相吻合[26]。郭江海（2022）主要探讨了国有企业科研项目进度管理的典型问题，针对问题提出优化策略，以期为国有企业科技创新工作的顺利开展提供支持[27]。

# （2）外包项目管理方面的研究

陈丽、李晓利和余俊（2021）为确保软件外包满足合同规定、指标满足技术协议要求，依据有关项目管理标准、项目研制规定，深入研究软件外包项目立项、建立外包协议、项目研制、验收交付等活动特征，分析每个活动应注意的关键问题，给出建议措施[28]。王秀梅（2021）指出，针对软件外包项目存在的问题，结合自身岗位的管理经验提出优化策略，从而达到软件外包规范化管理的目的[29]。李卫福（2020）以某大型火力发电企业外包项目管理中的一些经验实践，分析当前电力企业外委项目管理中存在的问题，提出促进外包项目管理的几项措施，供同行业相关的交流探讨[30]。李海涛和甄慧琳（2019）针对政府招标采购的档案数字化外包项目存在的问题，提出了量化项目开展及评价指标、建立及完善项目知识管理体系、加强数字化成果验收方与项目组沟通、稳定项目业务人才、等绩效提升策略[31]。张莉妹（2021）就软件测试外包驻场测试中如何解决信息安全问题开展研究，提出采用体系化的管理方式，并给出一些可采取的有效性措施[32]。陈国卫和周雨菁（2020）从装备软件外包的全过程展开了分析，认为装备软件外包主要包括策划阶段、软件研制开发阶段和软件使用维护阶段三个阶段[33]。喻珠晗（2020）以总包商成本、风险最低，同时保证服务质量为目标，通过外包过程分析，提出可能存在风险，并为总包商提出相应管控措施[34]。

（3）软件开发项目管理方面的研究

冉姝玲和杨柳青（2022）对软件工程技术在系统软件开发过程中的应用措施展开探讨，研究软件工程技术在系统软件开发中的使用措施并提出相关技术应用流程。希望能为我国软件系统开发提供基础技意见[35]。李映晟（2021）指出，大量软件开发成功案例证明，如果在软件开发中不断加强软件项目管理，随着国内软件产业的不断发展壮大，软件开发企业将来会面临严峻的管理挑战和发展风险。因此，为了有效保证企业软件开发管理效率与服务质量，必须高度重视强化软件项目管理必要性，采取多管齐下管理政策及积极有效项目管理策略。本文将重点探讨在软件开发中有效整合项目管理技术的策略[36]。程明霞（2022）阐述了远程办公软件现状，企业自动化办公软件在电脑PC 端、在手机客户端的应用，探讨企业自动化办公软件的价值，从而提升企业资源的利用效率[37]。王虹（2021）针对计算机办公软件在办公室的应用，分析了计算机办公软件的应用价值，提出计算机办公软件在应用中存在的问题，并给出了具体的解决策略，希望能充分发挥计算机办公软件的便利性、快捷性和准确性，提升企业管理水平，进一步推动我国经济的发展[38]。邢嘉舒和徐硕博（2021）研究分析了安卓手机办公软件对于办公效率的提升，保障时间和空间等因素不再影响企业的办公活动，实现更加灵活的办公，从而满足企业的办公需求。深入分析了基于安卓手机开发移动办公软件，详细阐述安卓手机移动办公 App 软件的开发和应用，并进一步探讨税务移动办公软件的设计[39]。张萍（2019）深入的研究阐述了软件开发的属性，并且针对工程化软件开发，选择了软件项目管理、软件过程改进等层面，研究分析了具体的开发方法[40]。

# 1.3 研究内容与方法

# 1.3.1 研究内容

论文以国网 S 公司办公信息化平台外包项目为研究对象，首先，对国内外进度管理、外包项目管理、软件开发项目管理相关的研究和文献进行研阅和总结，对项目进度管理相关理论进行梳理，为论文的写作奠定理论基础；其次，在对国网 S 公司办公信息化平台外包项目基本情况进行梳理和分析的基础上，编制了项目进度计划；再次，基于项目情况，结合进度计划，制定相应的进度控制措施，确保根据计划完成项目实施；最后，为了确保项目的进度计划编制和进度控制有序实施和保障能力，制定了项目进度管理保障措施。主要研究内容为：

第一章绪论，从研究背景方面进行了阐述，并论述了本研究的理论和实践意义，对国内外研究现状进行了全面的梳理，并简单介绍了本文研究内容，以及所使用到的研究方法。

第二章项目进度管理相关理论，重点是对软件开发和进度管理进行了概念介绍，然后，梳理了项目进度管理内容，包括项目进度计划和项目进度控制两个方面。

第三章国网 S 公司办公信息化平台外包项目基本情况，介绍了基本情况、主要内容和实施流程。

第四章是国网 S 公司办公信息化平台外包项目进度计划编制，按照项目进度计划编制的流程，进行了工作结构分解、确定工作逻辑关系、基于三时法进行时间估算、确定关键路径和确定项目历程碑计划。

第五章国网 S 公司办公信息化平台外包项目进度控制措施，首先给出了项目进度控制的流程，然后制定了项目进度监测与报告的方法，最后制定了项目进度分析与纠偏的措施。

第六章国网 S 公司办公信息化平台外包项目进度管理保障措施，从组织、制度、技术、人力方面，制定了项目进度保障措施。确保项目进度管理可以有效、有序实施。

第七章结论。

# 1.3.2 研究方法

（1）文献研究法

在论文题目拟定及开题过程中，通过线上、线下等渠道对国内外项目进度管理、外包项目管理、软件开发项目管理等方面的研究和文献进行了研阅和梳理，明确了论文写作的方向和基本思路，以及论文的框架，奠定了论文写作的理论基础。

# （2）访谈法

在论文写作过程中，需要对国网 S 公司办公信息化平台外包项目有较为全面和系统的了解，在此过程中，对国网 S 公司相关部门及人员、对软件开发公司及

相关人员等进行了访谈，了解了项目的基本情况、主要内容和实施流程等，同时对软件开发过程中的相关工作的时间有了一定了解，是进行项目进度计划编制和制定控制措施的基础。

# 1.4 论文结构

论文选择了国网 S 公司办公信息化平台外包项目开展研究，进行项目进度计划的编制，并结合计划制定了相应的项目进度控制措施，同时为了保证计划和控制措施的落实，还设计出了一些进度管理保障措施，保证了项目进度管理的有效落实。首先，大量收集汇总了有关项目管理、进度管理、软件开发管理的研究成果，进一步梳理总结了相关的研究方法和理论基础，明确了论文研究的理论依据；其次，对国网 S 公司办公信息化平台外包项目的基本情况进行了分析，包括项目的实施计划、主要内容以及项目实施的主要流程，为之后的进度计划编制奠定基础；再次，编制了项目进度计划，包括工作结构分解、确定活动逻辑关系、项目时间估算、关键路径确定和项目里程碑计划；然后，制定了项目进度控制措施，主要包括项目进度的监测、报告以及进度的分析和纠偏；最后，制定了项目进度管理的保障措施。论文结构如图1.1所示。

![](images/eb43077dfc94273fad2c89221d4178c298f54cb0df2c71423191cefc754adce0.jpg)  
图 1.1 论文结构图  
Fig. 1.1 Structure diagram of the paper

# 第 2 章 概念界定及理论基础

# 2.1 相关概念

# 2.1.1 办公信息化平台

信息化平台其实表示的是为了实现信息化的应用和发展，某个领域或组织开展建设的有利于信息化发展的环境。

本文办公信息化平台是指国网 S 公司拟开发的基于公司的实际需求出发的一套日常办公系统，涵盖公司内部公文、日程、报销、会议、车辆等管理功能模块。

# 2.1.2 进度管理

项目进度管理其实就是在设定的项目工期的基础上，通过编制进度计划和目标，根据项目和计划需求，明确资源供应，控制项目实施进度，同时结合费用、质量等方面的目标要求，推动项目最终的完成。项目进度管理旨在能够制定出有效、经济的进度计划，并且在后续执行中确保按照计划落实，保证在项目工期内完成项目目标。

# 2.2 项目管理理论

# 2.2.1 项目管理理论发展

项目管理理论的发展演变，共经过了三个发展阶段，具体如下：

（1）现代项目管理理论产生

20世纪中前期，在当时出现了多个重大工程项目，包含了“曼哈顿工程”等在内，这些工程项目对时间和资金方面有着较高的要求，为此，工程和管理人员共同商讨相关的方案，希望能够在有限的资金和资源的基础上，实现项目按期按质完工。项目完工后，人们开始对项目实施过程进行总结，并发现在大型工程实施过程中，通过限定资源、时间，对项目成本和进度严格控制，能够带来理想的项目实施效果。1957 年，美国杜邦公司等编制了关键路径法（CPM），这是当时最初设计出来的计划管理方法。1958 年，美国海军在北极星导弹核潜艇的研制时，为了更好的开展计划管理，编制了PERT法。由于和两种方法均是基于网路模型技术编制的，所以又叫做网络计划技术。

# （2）理论体系初步形成

80年代，在项目管理方面，网络计划技术成为了一种比较实用的技术方法，并且为各大项目的落实提供了重要支持。各国也积极地推动项目管理学术研究的发展，并发展出了相应的项目管理学术团体。1965 年，IPMA 成立，这也是首个项目管理国际学术团体，这也极大的丰富和优化项目管理理论，形成了一个相对完善的理论体系。80 年代，开始涌现出大量项目管理专著，如1983年的《项目管理手册》，而且随着各类学术报告和论文的发表，在这一领域逐渐构建形成完善的基础理论体系。

# （3）理论体系逐渐成熟

随着社会的发展，项目类型的增加，最初的项目管理理论体系无法满足时间需求，在结合了长期的实践经验的借基础上，逐渐的发展出了与现代化项目环境相适应的项目管理技术方法，不断的丰富、完善和发展项目管理理论。

# 2.2.2 项目管理理论的量化管理

传统的项目管理论著在管理项目管理的论述上，虽然覆盖较为全面，却很少有从量化管理层面来开展研究，而这也导致了项目管理呈现出了“混沌”状态。

（1）量化管理发展现状

项目的量化管理仍然处于快速的发展阶段，当前，比较常见的理论包括了六西格玛管理和 CMM/CMMI体系。

六西格玛在质量管理方面，是基于准确全面的数据开展的管理，确保达到最理想的管理效果。六西格玛其实即为 6σ。其主要是通过采集和统计全面的数据，找出误差存在的根本原因，并针对这些原因问题的存在，找出解决的方法，从顾客要求出发来开展管理活动。

六西格玛包括了五个阶段的运用，分别是：定义(D)，测量（M），分析（A），改进（I），控制（C)。这五个阶段是相互关联，相互影响的，而并非独立存在的。六西格玛是以数据为依据而非以经验来处理事务，做出决策，所以其本身更加的精准和科学。这一方法以需求为基础和方向，通过量化的方式来发挥出作用，能够将项目现状客观真实的反映出来。

1991年，卡耐基梅隆大学软件工程研究院（SEI）为美国国防部设计了CMM（Capability Maturity Model），这是一个软件过程改良、评估模型。如今经过多个版本的优化，发展出了系列标准模型。全球软件企业中，有上万家使用 CMM 评估。这一模型的核心是将软件开发看作是一个过程，监控这一过程中的工作，确保开发过程的科学标准化。

针对当前存在的各类 CMM 模型所表现出来的重复性、复杂性，解决由此所带来的成本和过程增加，发展出了 CMMI（CapabilityMaturityModelintegration），这是 SEI 为美国国防部研发的能力成熟度模型集成，它实现了软件 CMM2.0 版草案 C(SW-CMM)、IPD-CMM、EIA 过渡标准 731（系统工程 CMM）的集成，而且能够兼容ISO15504。在软硬件开发公司和政府机构得到了广泛应用。

（2）需要量化管理的项目管理领域

项目管理是一个非常复杂的体系，其中有不少的地方需要量化管理，管理主要包括了估算和度量两大类。估算表示的是在调查统计的基础上，结合事物内在发展规律和联系来估算出事物未来变化趋势、增长数量等变化。而度量则是结合相关标准来衡量当前事物，分析事物实际与标准之间的差距。在项目管理中，一般是对下面几个阶段开展估算：

# 1）项目范围估算

一个项目的落实，必须要提前进行评估项目预期范围，若对范围的评估出现了失误，则可能带来巨大损失。

# 2）项目成本估算

对于一个项目而言，必须要在项目开展前估算完成项目所需要的各类资源成本，项目预算其实就是根据估算所得到的总成本，划分到项目活动的各个阶段和工作活动中，通过构建相应的基准成本，以此来对项目执行做好衡量工作。所以，项目成本预算直接取决于成本估算。

# 3）项目进度估算

项目进度估算是针对项目开展和完成进度做出的估算，这也是项目管理的一个重要内容。估算项目进度，对于项目计划的制定、项目活动的监督落实有着指导意义。

# 4）项目风险估算

项目风险是项目活动中面临的一个重要问题，也是项目成功的一个重要影响因素，如果不能够有效的估算出项目风险，则会极大的影响到项目的开展，甚至导致项目失败。

# （3）量化管理的方法

项目管理的估算、度量，是项目量化管理的重要表现，项目管理人员主要采用的估算方法具体如下：

1）Delphi 法

这也是一类常见的专家评估法，主要是针对缺少历史数据的项目，通过Delphi法，要求参与专家开展讨论，从而降低估算带来的偏差。

方法步骤：聚集各位专家，将项目基本情况介绍给各位专家，并发放估计表格；通过小组会的方式参与相关影响因素的讨论分析；各专家进行的迭代表格的填写；收集表格，统计结果，并汇总到迭代表中，让各专家观看了解；开展小组会议，对估计中存在较大差异的地方进行讨论；专家根据表格数据以及商讨结果，再次填写估计表格；重复操作，直到最高和最低估计一致。

# 2）类比法

这是很对存在着相似历史项目的背景下所使用到的方法，包括项目环境、项目背景、复杂度、领域等相似，将历史项目与新项目对比，从而得出相应的估计数据。历史数据是否准确完整，直接决定着类比法估计结果是否准确，所以，开展项目后评估机制是发挥类比法的一个重要前提，要保证分析历史项目是可靠可使用的。

计划评审技术（PERT）通常是用来进行估计项目工期。而因素估算法、经验估算法等则经常用来进行估计项目成本。

# 2.3 项目进度管理内容和主要方法

# 2.3.1 项目进度管理的内容

项目进度管理不仅包含了制定项目进度计划，而且还包括了根据计划来开展技术控制，在实践中，必须要根据实际来拆解计划内容，才能够更好的推动工作的开展。

项目进度管理主要包括如下两个阶段：

（1）制定项目进度计划

项目进度计划是项目开展的前提和依据，只有制定项目进度计划，才能够按照计划有序的开展项目，保证项目的标准化落实，也能够避免出现延期的情况。制定计划包括了收集资料、分解项目结构、估算项目时间、编制进度计划等步骤。

为确保编制出一个科学合理的项目进度计划，在进行计划的编制前，需要收集真实全面的信息，在这些数据资料的基础上才能够制定出准确合理的计划。主要是收集项目背景、条件、实施单位、技术、人员、相关规定等。

# （2）项目进度计划的控制

制定项目进度计划，是确保项目落实中有一个科学依据，更好的开展项目管理，不过这也只不过是一个前提，而并非保障。项目实施中，因为面临着一直变动的外部环境和条件，因此，在实际执行中，往往会出现偏离计划的情况，此时若不能够及时控制并纠正偏差，则会导致无法按照计划落实项目，也就会最终影响的项目的进度。因此，要根据项目进度计划对项目的实施进行严格控制。

项目进度计划控制其实就是在项目开展进程中，结合计划来对项目活动的实施持续动态的跟踪检查，收集项目实际实施中的进度信息，并与计划相对比，从而分析中实际与计划存在的偏差，然后找出问题的原因，尽快解决问题，并根据影响情况修改进度计划，继续按照进度计划监控落实。这是一个持续动态的过程，是从项目开始到项目完成的过程全覆盖。

# 2.3.2 项目进度管理主要方法

（1）关键路径法

在项目管理中，一个最常用到的概念就是关键路径法 CPM（Critical PathMethod），CPM 起到了承上启下的作用，承上与 WBS（工作分解结构）相连，启下连接着进度监控过程。项目计划中最长路线即为关键路径，这也是项目需要消耗的最长时间的路径。因此，项目关键路径的确定以及管理，对于整个项目的控制实施非常关键。

根据绘制方法包括了两类关键路径法：即箭线图和前导图。

（2）甘特图

甘特图，其实也叫做条状图、横道图等。是亨利·L·甘特先生首次提出的。

从本质上来看，甘特图的内涵其实比较的明了，也就是在特定项目活动时间中，通过图示的方式将相关的活动与时间刻度的关联展现出来，表现出各项活动的顺序，以及消耗的时间长度。其横轴为时间，纵轴为活动，线条为期间计划和实际活动的完成情况。它能够将任务时间和进度清晰的反映出来，并可以展现出实际进度和计划的对比情况。因此，管理者通过甘特图就能够清楚地了解到完成的任务以及当前的任务，开展有效的工作进度评估。

# （3）网络图

网络图是一种如同网络结构的图解模型，其包含了三个基本要素，分别是作业（箭线）、事件（节点）和路线。网络图经常运用在工程管理中，通过节点和箭线来表示出工作的流程。

网络图模型以计划目标数量来进行划分，包括了单目标和多目标网络模型。

下图展现了网络图的形式。节点和箭线在不同网络图中的含义不一样，在单代号网络图中，节点表示的是工作，而箭线表示的是关系。不过，在双代号网络图中则并非表示的这种含义，节点为工作开始和结束，箭线为工作及走向。在图中，线路为起点和节点之间所形成的通路，而在众多的线路中，存在着一天最长工期的线路，则即为关键线路，只有保证关键线路的时间，按期完成关键线路工作，才能够避免出现延期的情况。

# 第3 章 国网S 公司办公信息化平台外包项目基本情况

# 3.1 项目概述

# 3.1.1 公司简介

国网 S 供电公司作为国家电网的地方性公司，隶属于国网辽宁公司，作为国有电力公司，主要任务是保障 SY市生产、生活的电力供应。

国网S供电公司为沈阳全市县区供电，供电范围1.3万平方公里。当前，公司有员工5986人，16个供电分公司，1个客户服务中心。另外，公司还在全市各地分布着39个营业大厅，134个营业所。到2022年末，公司资产总额104.52亿元，从 SY 电网的组成来看，主要是：3 座 500 千伏变电站，16 条 500 千伏线路；19座220千伏变电站，81条220千伏线路；167座66千伏公用变电站，217条66千伏公用线路。2022 年末，营业户数 362.6 万户，完成售电219.35亿千瓦时，同比增长 $1 0 . 5 1 \%$ 。

依据国家电网和国网辽宁省电力有限公司的机构设置，以及国网 S 供电公司工作实际，国网 S 供电公司由办公室、发展策划部、人事部、财务资产部、建设部等职能部门和各县区供电分公司以及技改中心、营销服务中心等业务支撑单位组成。国网S供电公司组织结构图如图3.1所示。

![](images/7ebefcde902680d53b5d8c0246f99d38697bbf10c5a0a93d6d1bab835abb5d5c.jpg)  
图 3.1 国网 S供电公司组织结构图  
Fig. 3.1 Organizational chart of State Grid S Power Supply Company

# 3.1.2 项目简介

国网S公司使用的办公管理系统经过2013年以前全部为纸质流转传输和现有OA 系统的使用之后，仍然存在员工直接用纸质文件流转的情况，这种“混合办公模式”导致了人力资源和系统资源的双重浪费。政府积极地开展电力行业革新，用电量多年连续上升，电网覆盖不断的扩张，在电网中新增了更多运营设备，加大了部门工作任务。而当前的管理流程复杂，且本身并不科学合理，极大的影响到了工作效率。为实现更加高效科学化的管理，必须要构建形成一个系统化的生产体系，实现信息共享融合、流程精简化，并且可以动态监控任务执行情况，从而进一步提升经营管理水平。

国网 S 公司办公信息化平台需要实现办公工作流电子化，推动办公数据共享，强化内外信息流通，从而更好的开展办公和管理；推动管理量化发展，提升办公效率，降低管理成本，促进公司逐渐的转型为信息化办公模式。

从工作管理层面来看，通过搭建办公信息化平台，能够更好的收集各类工作相关的信息，方便领导依据全面的信息做出准确科学的决策，更好的整合资源来推动公司关键业务和工作的开展；协助管理层开展管理工作，动态跟踪目标情况，强化办公管理。从企业整体层面来看，该平台的运作，可以使得行政指令更加高效的下达和运行，实现办公管理效率的高效化运作，打通了内部沟通渠道，实现了便捷化、快速化的交流互动，简化流程，实现企业工作综合效率的提升，从而增强企业实力。

办公信息化平台是将信息技术等引入到企业管理中，以企业实际需求为导向，开发出满足企业工作任务要求的办公系统，包括了内部公文、会议、日程、报销、车辆等多个模块，项目最终交付内容为国网 S 公司办公信息化平台系统。该项目拟于 2024 年 1 月开始建设，9 月 1 日正式投入使用。软件功能模块图如图 3.2 所示。

![](images/57b0580f570c657a798b8ddeb50beee908579a2534c4723a01ac26ffc2fd56a7.jpg)  
图 3.2 软件功能模块图  
Fig. 3.2 Software Function Module Diagram

# 3.1.3 项目管理组织机构

结合国网 S 公司办公信息化平台外包项目的建设实际和管理需求，办公信息化平台项目是国网 S公司2024年重点项目，是提升公司信息化水平和管理现代化能力的重要组成部分，经过初步研究，国网 S 公司决定在项目管理中，派遣分管科技互联网部的副经理作为项目第一责任人，在项目中，第一负责人的主要工作是：选择项目负责人，与项目负责人敲定项目组成员，划分内部任务工作分工；在项目实施过程中，跟踪项目进度，在出现了难以解决的问题时，及时给予项目负责人支持，参与协调问题的解决；主持技术评审，确定相关就绪和完成准则。

项目组成员的岗位和职责如下：

项目负责人：由国网 S 公司科技互联网部负责人担任，在分管领导的指导下全权管理项目；根据项目的进度情况，组织开展技术评审和里程碑评审；审核、批复软件技术文档；向领导汇报项目进度情况；审批软件配置、质量管理等活动；验证项目各过程活动；对于项目组内部不能够解决的问题，协调处理解决。

软件负责人：这是中标企业参与到项目中来的负责人，主要是参与项目策划和评审活动；对软件的开发进行跟踪和监督，对出现的偏差及时纠正；分析项目需求，并负责软件从设计到运行的工作；分配研发、测试软件人员，并对人员开展管理考核；制定、评审软件开发计划；控制需求变更；向领导层和负责人汇报项目进展；组织开展组内例会，处理出现的问题。

配置管理人员：在软件开发计划基础上，来编制科学合理的软件配置管理计划，推动计划落实；建立项目开发库、基线库、受控库；记录管理过程，以报告的方式发布出来；对配置库权限进行维护。

软件需求分析人员：收集相关的需求数据，开展需求分析，构建需求矩阵；动态跟踪需求矩阵的变化。

质量保证人员：在开发和配置管理计划的编制中参与进来，并且对计划开展评审活动，确保计划的落实能够满足质量要求，并在计划编制评审中，提供相关标准指导作为参考；编制、维护软件质量保证规划；对软件设计开发过程活动和软件产品审核；对出现的质量问题，及时跟踪指导解决，汇报项目质量情况；组内无法处理的问题，及时的上报给上级领导；撰写质量保证报告，并且根据前期的质量情况，分析评估项目质量趋势。

软件研发人员：在项目组召开的双周会上，发表工作相关报告，听取任务要求和指导；撰写工作日记和周报；设计文档，编制软件代码；测试和检查代码，找出其中的问题并纠正；参与项目技术和里程碑评审；在需求分析，以及软件从设计到测试等过程中，积极地参与进来；解决软件产品存在的缺陷；编制软件开发文档。

软件测试人员：软件测试时最主要的任务，如单元测试、集成测试、合格性测试等；编制项目测试计划、测试程序、测试用例；对测试中的问题进行记录、汇报；对测试结果进行总结分析，编写测试报告。

国网 S 公司办公信息化平台外包项目管理机构如图3.3所示。

![](images/ec1e4cff78338705aa759c09261a1853807abc93f790d169a2e5a0d8b54a1e47.jpg)  
图 3.3 国网 S公司办公信息化平台外包项目管理机构图  
Fig. 3.3 Outsourcing Project Management Organization of State Grid S Company's Office Information Platform

# 3.1.4 项目总体目标

根据国网 S 公司领导班子办公会议决定，办公信息化平台开发项目由公司分管科技互联网部的副经理负责主持开展，由科技互联网部部长具体具体负责。项目的前期调研、准备工作、项目实施后的协调、沟通由科技互联网部负责组织开展。

项目的交付物：国网 S公司办公信息化平台。

项目周期：2024 年 1 月 4 日—2024 年 8 月 30 日，2024 年 9 月 1 日正式上线运行。

项目投资额：80 万元。国网 S 公司办公信息化平台开发项目采取招投标方式实施，项目纳入 2024年公司预算。

项目质量要求：国网 S 公司办公信息化平台是将信息技术等引入到企业管理中的重点项目，要求以企业实际需求为导向，要满足国网 S 公司办公需要的所有功能，平台系统具有较好的稳定性、可维护性，具有良好和便捷的用户界面。

# 3.2 项目主要内容及实施流程

# 3.2.1 项目主要内容

为了确保办公信息化平台能满足国网 S 公司需求，并且可以有效的匹配现行管理模式，要结合该公司的管理制度和思路来进行平台构建，在构建平台时，主要从技术、功能和应用三个层面来开展，确保开发的平台能够满足公司的需求，并且提升公司的办公质量和效率。在对需求进行深入调研后发现，所要开发的办公信息化平台不仅能够满足一般办公需求，而且还必须根据国网S公司工作特征，开发出一些相关的特定功能。国网 S 公司办公信息化平台主要包括以下几个方面内容。

# （1）提高办公效率

当前，国网 S 公司的制度和流程已经相当的完善，不过，仍然依靠人工方式办公和处理问题，效率太低。如在公司内部的文件审批方面，必须要通过纸质文件下达相关的文件要求，要实现纸质文件的全面流转覆盖，这一方面需要花费大量时间来找各个领导审批，另一方面效率也非常的缓慢；公司的工作模式以为闭环模式，需要消耗大量资源来开展监督工作，因此，公司当前最主要的任务是优化资源配置，而通过搭建办公信息化平台，则可以为公司降低大量的生产成本。通过平台的运作，可以降低成本、节省时间和精力，提升工作效率。

# （2）公文流转自动快捷

公文流转是办公信息化平台最主要的功能，而对于国网 S 公司而言，公文流转也是当前日常工作中的一个重要部分，并且需要消耗大量的时间和精力。该公司作为电力企业，保证生产安全是工作的重点任务，因此，在日常生产中，面临着大量的安全文件需要流转传递，通过平台能够实现快速传递，而在重要活动或者是节假日期间，公司全员处于高度紧张的工作状态，每天的文件传输数量更大，在这种频繁的传递任务中，平台的优势彰显出来。不过，这需要平台必须要保证流传程序简单方便，易于操作使用，其主要是为了实现真实有效的传递信息，简化流程，合理划分权限，便于维护等，只有在这些原则基础上，才能够发挥出平台的优势，降低成本，提升工作效率。另外，平台的收文、发文、签报等功能，也要能满足简单易学的特点，填写内容简化，保证文件传递所需时间更短，从而实现文件流转的高效化。

# （3）人员权限明确

公司部门众多，有着大量的员工和设备，所以，制定了规范完善的组织架构，通过公司的组织架构，来了解各岗位对平台所提出的功能需求，以及对应的使用权限，并且根据权限管理可以直接定位责任人，快速的视线责任划分和责任追究，从而避免出现扯皮、推诿等情况。

因为公司各岗位都可以参与到办公信息化平台的操作使用中来，所以，必须要结合各岗位的权责来进行设定对应的平台系统使用权限，如一些特定文件的审核，仅限定在某些特定岗位，避免其他岗位越权审核的情况发生。而且通过责任权限的划分，也让员工认清自身的目标，从而工作更有方向，也便于根据权责划分来进行监管督导工作的开展，保证各环节衔接，保障安全高效生产。

（4）工作督办计划管理需求

电力企业处于特殊行业，所以，不管是制度还是管理模式方面，都不同于普通企业，为能够更好的发挥出办公信息化平台在电力企业中的作用，有必要进一步拓展平台的功能。国网 S 公司采取的是闭环的管理模式，通过平台实现流程追踪和状态管理，增加工作督办功能，从而实现通过平台的方式开展闭环管理，也更加的符合电力行业需求。

# （5）会议管理

在国网 S 公司内部，会议管理是一个使用频率较高的功能，如在线申请、安排会议，并且记录会议召开情况和内容，还可以查询以往的会议召开记录，以避免会议召开时存在场地使用冲突的情况。

# （6）车辆管理

国网 S 公司车辆使用比较的频繁，当前仍然采用的是人工进行车辆管理，无法实现车辆快速、准确的调度，也不能够跟踪记录车辆的行程，这就极大地降低了生产车辆的使用效率，增加了管理成本。因此，要将车辆管理功能加入到办公信息化平台中来，从而在平台上统一管理、调度、维护公司内部车辆。

# （7）报销管理

报销业务是国网 S 公司日常处理的主要业务内容之一，不过，当前主要是开具手工报销单的方式来处理，而且必须要经过相关领导审批，程序比较繁琐，过程麻烦，浪费大量的精力和时间。而在办公信息化平台中加入报销管理功能，则能够实现线上报销管理工作，提升了报销管理工作的效率。

# 3.2.2 项目实施流程

国网 S 公司办公信息化平台外包项目是国网 S 公司提升信息化水平和全面提升管理能力的重点项目，也是按照国家电网公司和国网辽宁电力公司提质增效要求的重点工作，国网 S 公司对办公信息化平台项目非常重视。国网 S 公司专门召开领导班子会议研究了该项目的建设工作，根据会议的相关要求和议定内容，该项目的建设组织、管理工作由科技互联部具体负责。经过项目的初步研究和调研，国网S公司办公信息化平台外包项目的实施主要有以下几个流程。

首先，国网 S 公司办公信息化平台外包项目要进行前期的调研准备，由科技互联网部就项目建设相关情况进行前期的调研，调研后根据调研情况形成总体策划方案，方案报到国网 S 公司管理层，管理层研究确定后，项目开始进行详细准备。国网 S 公司办公信息化平台外包项目总体方案确定后，由科技互联网部拟定招标文件，准备招标，招标结束后，国网 S 公司与中标单位成立办公信息化平台项目联合项目组。国网 S 公司办公信息化平台外包项目就可以进入到具体实施阶段。

其次，国网 S 公司办公信息化平台外包项目的软件开发阶段，也就进入了项目的具体实施阶段，在软件开发阶段，主要有总体方案设计、需求分析、系统设计、详细设计、编码和软件测试等内容。

再次，软件开发企业还要对国网 S 公司员工进行培训，对软件使用、维护等内容进行培训。

然后，国网 S 公司要在软件开发企业的指导帮助下完成数据的准备工作，即将公司基本数据录入和迁移到新开发的办公信息化平台中。

最后，要进行国网S 公司办公信息化平台的试运行和验收，平台通过验收后，可以正式投入使用。

![](images/8c80eae7be5cbc999f39547c392e0e626de52b57d214c70ef515d33704e5d520.jpg)  
国网S 公司办公信息化平台外包项目主要流程如图3.4所示。  
图 3.4 国网 S 公司办公信息化平台外包项目主要流程图

Fig. 3.4 Main flowchart of the office information platform project of State Grid S Company

# 第4 章 国网S 公司办公信息化平台外包项目进度计划编制

# 4.1 项目工作结构分解及逻辑关系确定

# 4.1.1 项目工作结构分解

工作分解结构是项目进度计划编制的前提和基础。工作分解结构（WorkBreakdown Structure，简称 WBS）从原理上来看，其实与因数分解相同，是将项目根据相应的原则进行分解，从而得到了各个分解后的任务，并进一步划分形成工作内容，然后分配给相应的员工，实现项目分解成最小的工作内容。

通过对国网 S 公司办公信息化平台外包项目建设内容和主要流程的分析，根据各项工作的连续性和相关性关系，将该项目分解为 5 个阶段，分别为项目的准备阶段、软件开发阶段、培训阶段、数据准备阶段和项目交付阶段。下面对每一个阶段的工作进行详细分析，以完成该项目的工作结构分解。

（1）项目准备阶段

在国网 S 公司办公信息化平台外包项目的准备阶段，主要完成项目调研、策划、审批、招标等工作，其中，项目调研主要有科技互联网部完成，包括对办公信息化平台项目主要建设内容、实现功能、软件开发企业、项目建设成本等方面的市场前期调研，是该平台建设的前提和基础工作。项目调研完成后，科技互联网部要根据前期调研情况，制定国网 S公司办公信息化平台外包项目的总体策划，策划内容包括调研情况的总结、项目建设的目标、建设方法、软件开发行业的市场情况、项目实施总体规划等内容。项目策划方案制定后，要上报国网 S 公司讨论审批，审批通过后，国网S公司办公信息化平台外包项目可以按照该方案实施。

国网 S 公司办公信息化平台外包项目为新建项目，为提高项目建设质量，按照国网公司政府采购要求，应采用公开招标方式建设。在项目招投标过程中，可以分为两个主要活动，一是编制招标文件，这是项目招投标过程中非常重要的一个环节；二是项目的招投标和签订合同，按照项目招投标要求实施招投标，投标企业中标后即可签订合同，所以将招投标和签订合同作为一项活动。

项目招投标完成后，国网 S 公司要与中标企业共同成立办公信息化平台项目组，确定项目负责人，各项工作负责人，工作职责等，并召开项目组会议，对项目实施进行第一次研究。所以，成立项目组也是国网 S 公司办公信息化平台外包项目实施中的一项重要活动。

（2）软件开发阶段

在软件开发阶段，国网 S 公司办公信息化平台外包项目就进入了具体的实施阶段，要完成总体方案设计、需求分析、系统设计、详细设计、编码、软件测试等工作。

其中，总体方案设计是该平台开发的总体设计，主要由软件开发企业完成，软件开发企业应全面详细了解国网 S 公司对办公信息化平台的整体要求，保证方案的合理性，为国网 S 公司国网 S 公司办公信息化平台的建设提供合理建议，给出平台集成方案，说明平台集成的主要工作内容，如有必要应说明平台过渡、迁移对现有已运行业务的影响及应对方案。制定软件开发的计划，包括各项任务、任务成果、负责人、时长，注明里程碑，并明确需要国网 S 公司配合的任务，应提供参加本项目的人员组成结构图，参加人员的背景资料以及明确职责。总体方案设计是国网 S 公司对办公信息化平台软件开发的指导性文件，之后的软件开发相关的工作都要按照这个设计开展。软件开发企业完成总体方案设计后，要将该方案交项目组和国网 S 公司进行审核确认，确认后进行下一步工作。

需求分析是软件开发中最重要的也是最基础的一个环节。在需求分析过程中，软件开发企业系统分析员初步了解了国网 S 公司的需求，并且通过相关软件，将系统需要的几个功能模块罗列出来，其中还细分了各模块中具备的小功能模块，一些需求比较明确，则可以之际确定相应的界面。在系统分析结束后，需要将系统分析结果交由国网 S 公司相关部门和人员进行确认，国网 S 公司相关部门和人员可以在此过程中对需求提出变更，并最后确认。系统分析员对这些需求进行深入分析，并且使用WORD等工具，结合自己的经验来制作出功能需求文档。在其中将系统大功能模块清楚的罗列出来，并且包括了下面的小功能模块，以及相关界面和功能。然后，向用户再次确认需求。所以，通过分析应该将需求分析划分为需求调研、需求确认和编制需求报告三个活动。

需求分析之后，项目开发企业要开展系统系统设计，也就是系统概要设计，主要是从系统组织结构、处理流程、功能分配、模块划分、运行设计、接口设计等开展设计，而只有完成了系统设计，才能够为软件的详细设计提供指导和方向。

详细设计，在开展了系统设计后，必须要进一步的开展详细设计，才能够依据这一设计计划来推动落实，因为这是详细的设计了模块算法，以及软件局部结构设计。基于需求分析来开展详细设计，以确保设计的系统产品能够满足用户需求，在整个软件开发中，详细设计是相对独立的一个工作。

在完成了详细设计后，则开展相应的编码工作，基于《软件系统详细设计报告》，通过编程确保实现各模块功能，从而满足软件各功能上的需求。

软件开发企业为控制质量，发现产品中的问题，根据项目特征，在相应的阶段开展测试，通过各方面的测试，以确保避免出现问题。根据测试结果提供缺陷报告，并进行相关的修复工作，结合测试来对开发质量做出评价，并提出相应的建议，也就是内部测试。

内部测试通过后，软件开发企业应联合国网 S 公司进行综合测试，开发企业需提供测试方案，验证系统间的接口是否联通、传输数据是否正确，以及系统各功能模块协同工作能力。综合测试是指项目开发企业需提供测试方案验证系统最终实现的功能是否满足买方提出的需求，包括功能性需求和非功能性需求；整个系统，包括硬件、系统软件、应用程序、数据质量、自身管理、系统性能、网络、安全等各个方面进行全面测试，测试系统对技术指标的满足和对业务需求的满足能力，验证系统是否达到上线要求。

# （3）培训阶段

一是管理员培训，软件开发企业要结合国网 S 公司办公信息化平台的特点对国网 S 公司的相关人员开展培训，首先要参加培训就是管理员，管理员负责未来该平台在应用过程中用户权限设置、系统管理、系统设置以及相关问题的解决，所以要对新的平台有全面、系统的了解，对新平台的应用和管理要能够掌握。

二是用户培训，国网 S 公司办公信息化平台是新开发的平台，为了使国网 S公司的员工能够较快的熟悉、掌握和应用新的平台，软件开发企业要对国网 S 公司的员工开展系统的培训，对新平台的功能、操作、界面等进行实操讲解。

# （4）数据准备阶段

国网 S 公司办公信息化平台的软件编码完成之后，需要进行内部测试，通过内部测试之后，在进行综合测试和试运行之前，要将国网 S 公司的相关信息、数据等录入到新开发的平台中，以进行下一步的综合测试和试运行，也就是数据准备阶段。

数据准备分为两个部分，一部分是系统信息，一部分是基础数据迁移。其中，系统信息是指系统的管理员信息、用户权限等信息的录入和设置，需要国网 S 公司的系统管理员在软件开发企业的协助、指导下完成录入，这些信息的录入是系统数据迁移和之后测试、运行的基础。数据迁移是将国网 S 公司现有系统的相关数据、信息等迁移或录入到新开发的办公信息化平台中，在员工经过培训后，录入这些信息。所以，国网 S 公司办公信息化平台的数据准备阶段主要包括系统信息录入和基础数据迁移两项活动。

# （5）项目交付阶段

在国网 S 公司办公信息化平台完成了软件开发和综合测试之后，就进入到了项目交付阶段。在项目交付阶段，首先要进行办公信息化平台的试运行，试运行要包括两个方面工作，一是试运行，即在将国网 S 公司及各部门相关信息、数据录入和迁移到新平台后，国网 S 公司各部门人员都要参与到试运行活动中，在实际工作中利用该平台开展各项管理工作和业务操作，对各项业务流程、操作进行测试和试验，进一步测试系统的实际操作性能，发现存在的问题。二是试运行完成后，要对试运行情况作出总结，作为下一步软件验收的基础。

国网 S 公司办公信息化平台的试运行完成后，要进行软件验收，软件开发企业要配合国网 S 公司验收，形成验收测试报告，未能达到验收标准的部分按需求修改相关系统的文档、应用程序或控制流程。

通过验收后，软件开发企业应将各类文档资料交付国网 S 公司，包括设备技术资料、安装调测文件、操作手册、维护文档、软件使用手册、软件资料（含软件清单、源程序）等。

验收通过后，国网 S 公司办公信息化平台就可以正式上线运行。

通过对国网 S 公司办公信息化平台外包项目实施流程的详细分析，根据每一个阶段的工作主要内容，可以完成对该项目的工作分解。项目工作结构分解如图4.1 所示。

![](images/50fb64f0b005e8b298f56e318812dc7432a6e93b144f57cf41b0f71e1c2f6c47.jpg)  
图 4.1 项目工作结构分解图  
Fig. 4.1 Project Work Structure Breakdown

完成项目工作分解后，要对国网 S 公司办公信息化平台外包项目各环节的活动进行 WBS 分解，通过对该项目进行 WBS 分解，将整个项目工作分解细化为一个个的具体活动，这些活动具有相互之间较为独立、操作内容较为明确、便于检查等特点，从而方便和有效地对项目实施过程进行监督和管理，更重要的是提升项目进度管理的针对性和准确性。国网S公司办公信息化平台外包项目WBS 分解如表4.1所示。

# 表 4.1 项目 WBS 分解表

Table4.1 Project WBS breakdown table   

<table><tr><td>WBS分解代码</td><td>分解任务名称</td></tr><tr><td>1</td><td>项目准备</td></tr><tr><td>1.1</td><td>项目调研</td></tr><tr><td>1.2</td><td>总体策划</td></tr><tr><td>1.3</td><td>策划方案审批</td></tr><tr><td>1.4</td><td>编制招标文件</td></tr><tr><td>1.5</td><td>项目招投标</td></tr><tr><td>1.6</td><td>成立项目组</td></tr><tr><td>2</td><td>软件开发</td></tr><tr><td>2.1</td><td>总体方案设计</td></tr><tr><td>2.2</td><td>总体方案确认</td></tr><tr><td>2.3</td><td>需求分析</td></tr><tr><td>2.3.1</td><td>需求调研</td></tr><tr><td>2.3.2</td><td>需求确认</td></tr><tr><td>2.3.3</td><td>编制需求报告</td></tr><tr><td>2.4</td><td>系统设计</td></tr><tr><td>2.5</td><td>详细设计</td></tr><tr><td>2.6</td><td>编码</td></tr><tr><td>2.7</td><td></td></tr><tr><td>2.8</td><td>内部测试</td></tr><tr><td></td><td>综合测试</td></tr><tr><td>3</td><td>培训</td></tr><tr><td>3.1</td><td>管理员培训</td></tr><tr><td>3.2</td><td>用户培训</td></tr><tr><td>4</td><td>数据准备</td></tr><tr><td>4.1</td><td>系统信息录入</td></tr><tr><td>4.2</td><td>数据迁移</td></tr><tr><td>5</td><td>项目交付</td></tr><tr><td>5.1</td><td>试运行</td></tr><tr><td>5.2</td><td>试运行总结</td></tr><tr><td>5.3</td><td>软件验收</td></tr><tr><td>5.4</td><td>用户说明书移交</td></tr><tr><td>5.5</td><td>上线运行</td></tr></table>

# 4.1.2 确定项目活动逻辑关系

在制定项目计划之前，需要先将国网 S 公司办公信息化平台外包项目的各项任务进行排序，然后根据任务的先后逻辑顺序来确定项目的关键路径，继而绘制出项目的甘特图。在国网 S 公司办公信息化平台外包项目进度分解中，所有任务都是串联进行的，这样的安排在一定程度上造成了时间上的浪费。在实际项目的实施过程中，有的任务存在逻辑上的先后关系，而有的任务是可以并行进行的。因此通过进行排序来确定任务之间的前后关联和依赖关系。国网 S 公司办公信息化平台的工作关系如表 4.2所示：

# 表 4.2 项目工作关系表

Table 4.2 Project Work Relationship Table   

<table><tr><td>WBS分解代码</td><td>分解任务名称</td><td>紧前工作</td></tr><tr><td>1.1</td><td>项目调研</td><td></td></tr><tr><td>1.2</td><td>总体策划</td><td>1.1</td></tr><tr><td>1.3</td><td>策划方案审批</td><td>1.2</td></tr><tr><td>1.4</td><td>编制招标文件</td><td>1.3</td></tr><tr><td>1.5</td><td>项目招投标</td><td>1.4</td></tr><tr><td>1.6</td><td>成立项目组</td><td>1.5</td></tr><tr><td>2.1</td><td>总体方案设计</td><td>1.5</td></tr><tr><td>2.2</td><td>总体方案确认</td><td>2.1</td></tr><tr><td>2.3.1</td><td>需求调研</td><td>2.2</td></tr><tr><td>2.3.2</td><td>需求确认</td><td>2.2</td></tr><tr><td>2.3.3</td><td>编制需求报告</td><td>2.3.1、2.3.2</td></tr><tr><td>2.4</td><td>系统设计</td><td>2.3.3</td></tr><tr><td>2.5</td><td>详细设计</td><td>2.4</td></tr><tr><td>2.6</td><td>编码</td><td>2.5</td></tr><tr><td>2.7</td><td>内部测试</td><td>2.6</td></tr><tr><td>2.8</td><td>综合测试</td><td>2.7、4.1、4.2</td></tr><tr><td>3.1</td><td>管理员培训</td><td>2.6</td></tr><tr><td>3.2</td><td>用户培训</td><td>2.6</td></tr><tr><td>4.1</td><td>系统信息录入</td><td>2.7、3.1</td></tr><tr><td>4.2</td><td>数据迁移</td><td>2.7、3.2</td></tr><tr><td>5.1</td><td>试运行</td><td>2.8</td></tr><tr><td>5.2</td><td>试运行总结</td><td>5.1</td></tr><tr><td>5.3</td><td>软件验收</td><td>5.2</td></tr><tr><td>5.4</td><td>用户说明书移交</td><td>5.3</td></tr><tr><td>5.5</td><td>上线运行</td><td>5.4</td></tr></table>

# 4.2 项目里程碑计划及时间估算

# 4.2.1 项目里程碑计划

制定项目进度计划的基础是要对项目的整体需求和计划有全面地了解和掌握，对项目各个主要阶段的完成时间进行明确，从而为项目结构分解和时间估算奠定基础。所以，项目进度计划制定的基础和前提就是确定里程碑事件。根据项目进度工期的要求，国网S公司办公信息化平台开发项目实施时间为 2024年1月4 日—2024 年 8 月 30 日，9 月 1 日正式上线。通过之前对国网 S 公司办公信息化平台开发项目流程的分析和 WBS分解，项目主要分为准备阶段、软件开发阶段、培训阶段和项目交付阶段五个阶段。

项目准备阶段中的成立项目组活动的完成表示项目完成准备工作，即将开始正式实施，所以，成立项目组作为里程碑事件。软件开发阶段中的内部测试完成，表示软件的程序编写、测试等具体开发工作已经完成，所以内部测试活动作为里程碑事件。在软件开发阶段，有一个非常重要的活动就是需求分析，需求分析是软件开发中系统设计、详细设计和编码的基础工作，也是存在较多变数的工作，在需求分析中，需要多次的调查、确认和变更，需求分析完成后，软件开发的其他活动都可以在此基础上有序开展，同时，需求分析一般占到整个软件开发工作中的三分之一的时间，所以，将需求分析活动中的编制需求报告作为一个里程碑事件。综合测试是要在对国网 S 公司的系统管理员和员工进行培训和数据准备完成之后，由国网 S 公司相关人员与软件开发企业共同完成的一项工作，是对软件综合性能、模块设计、业务流程等的综合性测试，所以，综合测试是一个里程碑事件。在项目交付阶段，国网 S 公司办公信息化平台开发项目需要完成试运行、验收等工作，最终实现交付，其中，用户说明书移交活动的完成，表示软件验收等活动全部为完成，该项目的实施阶段基本完成，可以上线运行，所以，用户说明书移交是该项目的里程碑事件。

国网 S 公司办公信息化平台开发项目里程碑计划如表4.3所示。

Table 4.3 Project Milestone Plan   

<table><tr><td>里程碑事件</td><td>3月下旬</td><td>5月中旬</td><td>7月初</td><td>7月下旬</td><td>8月中旬</td></tr><tr><td>成立项目组</td><td>★</td><td></td><td></td><td></td><td></td></tr><tr><td>编制需求报告</td><td></td><td>★</td><td></td><td></td><td></td></tr><tr><td>内部测试</td><td></td><td></td><td>★</td><td></td><td></td></tr><tr><td>综合测试</td><td></td><td></td><td></td><td>★</td><td></td></tr><tr><td>说明书移交</td><td></td><td></td><td></td><td></td><td>★</td></tr></table>

# 4.2.2 项目时间估算

PERT是基于网络计划图所开展的关于项目时间的分析评估方法。在本研究项目中，通过组织人员对活动时间进行预估，主要预估时间包括了三个方面，分别是最可能完成的时间，以及最乐观和最悲观的时间，然后根据这三种估计类型，计算出项目时间。然后结合甘特图计算出项目最长路径，根据这一结果进行项目计划的制定。

50 年代，美国海军在开展的北极星（Polaris）导弹项目中首次使用到了三时估算，通过对这三类时间的估算，来得到项目估算时间。该方法的计算方法，持续时间 $= ( { \bf a } + 4 \times { \bf m } + { \bf b } ) / 6$ 。

在制定项目管理计划时，其中的核心内容就是要开展项目工期评估。若评估时周期与实际存在着较大的偏差，当评估时间过大时，这会导致项目资源存在着大量的浪费情况；而当评估时间过小时，则为了能够尽可能的按计划完工，在项目开展中会降低工作质量，带来产品质量问题。所以，项目工期评估时一个非常重要的内容，评估时间的准确科学与否，直接关系着项目的好坏。在评估时，主要是组长与小组成员结合以往经验，初步估算出项目时间，通过审查各小组结果，对于其中存在的较大偏差进行调整。各组长根据估算的任务和难度来安排人员。

首先要对国网 S 公司办公信息化平台外包项目工作结构分解之后的各个工作任务的工作量进行分析。项目工作量情况如表4.4所示。

表 4.4 项目工作量情况Table 4.4 Project Workload Situation  

<table><tr><td colspan="3"></td></tr><tr><td>任务序号</td><td>任务名称</td><td>工作量 对办公信息化平台项目主要建设内容、实现功能、软件开发</td></tr><tr><td>1</td><td>项目调研</td><td>企业、项目建设成本等方面的市场前期调研 根据前期调研情况，制定国网S公司办公信息化平台外包项</td></tr><tr><td>2</td><td>总体策划</td><td>目的总体策划，策划内容包括调研情况的总结、项目建设的 目标、建设方法、软件开发行业的市场情况、项目实施总体 规划等内容。</td></tr><tr><td>3</td><td>策划方案审批</td><td>项目策划方案制定后，要上报国网S公司讨论审批，审批通 过后，国网S公司办公信息化平台外包项目可以按照该方案 实施。</td></tr><tr><td>4</td><td>编制招标文件</td><td>按照国网公司政府采购要求，编制招标文件.</td></tr><tr><td>5號</td><td>项目招投标</td><td>按照项目招投标要求实施招投标，投标企业中标后即可签订 合同.</td></tr><tr><td>6個</td><td>成立项目组</td><td>国网S公司要与中标企业共同成立办公信息化平台项目组， 确定项目负责人，各项工作负责人，工作职责等. 总体方案设计是该平台开发的总体设计，给出平台集成方</td></tr><tr><td>7</td><td>总体方案设计</td><td>案，说明平台集成的主要工作内容，包括各项任务、任务成 果、负责人、时长，注明里程碑，并明确需要国网S公司配 合的任务。</td></tr><tr><td>8</td><td>总体方案确认</td><td>完成总体方案设计后，要将该方案交项目组和国网S公司进 行审核确认，确认后进行下一步工作。</td></tr><tr><td>9</td><td>需求调研</td><td>了解了国网S公司的需求，通过到各个单位、部门调研，了 解平台的总体需求、各业务流程和需求等，并且通过相关软 件，将系统需要的几个功能模块罗列出来。</td></tr><tr><td>10</td><td>需求确认</td><td>将系统分析结果交由国网S公司相关部门和人员进行确认， 国网S公司相关部门和人员可以在此过程中对需求提出变 更，并最后确认。</td></tr><tr><td>11</td><td>编制需求报告</td><td>系统分析员对这些需求进行深入分析，并且使用WORD等 工具，结合自己的经验来制作出功能需求文档。</td></tr><tr><td>12</td><td>系统设计</td><td>从系统组织结构、处理流程、功能分配、模块划分、运行设 计、接口设计等开展设计。</td></tr><tr><td>13</td><td>详细设计</td><td>设计了模块算法，以及软件局部结构设计。</td></tr><tr><td>14</td><td>编码</td><td>基于《软件系统详细设计报告》，通过编程确保实现各模块 功能。</td></tr></table>

<table><tr><td colspan="4">续表4.4</td></tr><tr><td>15</td><td>内部测试</td><td>通过各方面的测试，以确保避免出现问题。根据测试结果提</td><td>供缺陷报告，并进行相关的修复工作。</td></tr><tr><td>16</td><td>综合测试</td><td>开发企业需提供测试方案，验证系统间的接口是否联通、传 输数据是否正确，以及系统各功能模块协同工作能力。</td><td></td></tr><tr><td>17</td><td>管理员培训</td><td></td><td>对新平台的应用和管理进行管理员培训。</td></tr><tr><td>18</td><td>用户培训</td><td></td><td>对新平台的功能、操作、界面等进行实操讲解。</td></tr><tr><td>19</td><td>系统信息录入</td><td></td><td>系统的管理员信息、用户权限等信息的录入和设置。</td></tr><tr><td>20</td><td>数据迁移</td><td></td><td>将国网 S公司现有系统的相关数据、信息等迁移或录入到新 开发的办公信息化平台中</td></tr><tr><td>21</td><td>试运行</td><td></td><td>国网S公司各部门人员都要参与到试运行活动中，在实际工 作中利用该平台开展各项管理工作和业务操作，对各项业务</td></tr><tr><td>22</td><td>试运行总结</td><td></td><td>流程、操作进行测试和试验，进一步测试系统的实际操作性 能，发现存在的问题。 试运行完成后，要对试运行情况作出总结，作为下一步软件</td></tr><tr><td>23</td><td>软件验收</td><td></td><td>验收的基础。 软件开发企业要配合国网S公司验收，形成验收测试报告。</td></tr><tr><td>24</td><td>用户说明书移交</td><td></td><td>包括设备技术资料、安装调测文件、操作手册、维护文档、</td></tr><tr><td></td><td></td><td></td><td>软件使用手册、软件资料（含软件清单、源程序）等。</td></tr><tr><td>25</td><td>上线运行</td><td></td><td>验收通过后，国网S公司办公信息化平台就可以正式上线运 行。</td></tr></table>

经过对国网 S 公司办公信息化平台外包项目各项工作的工作量分析，结合软件外包项目的相关研究和实践成果，并与项目管理团队的专家等进行沟通，可以对项目工作时间进行估算。工作时间估算情况如表4.5所示。

表 4.5 项目工作时间估算表  
Table 4.5 Project Work Time Estimation Table   

<table><tr><td>任务序号</td><td>任务名称</td><td>最乐观时间 （天）</td><td>最悲观时间 （天）</td><td>最可能时间 （天）</td><td>PERT期望时 间(天）</td></tr><tr><td>1</td><td>项目调研</td><td>14</td><td>20</td><td>15</td><td>16</td></tr><tr><td>2</td><td>总体策划</td><td>5</td><td>9</td><td>7</td><td>7</td></tr><tr><td>3</td><td>策划方案审批</td><td>3</td><td>5</td><td>3</td><td>3</td></tr><tr><td>4</td><td>编制招标文件</td><td>4</td><td>7</td><td>5</td><td>5</td></tr><tr><td>5</td><td>项目招投标</td><td>25</td><td>28</td><td>25</td><td>26</td></tr><tr><td>6</td><td>成立项目组</td><td>2</td><td>4</td><td>2</td><td>2</td></tr><tr><td>7</td><td>总体方案设计</td><td>7</td><td>10</td><td>7</td><td>8</td></tr><tr><td>8</td><td>总体方案确认</td><td>3</td><td>7</td><td>5</td><td>5</td></tr><tr><td>9</td><td>需求调研</td><td>18</td><td>24</td><td>20</td><td>20</td></tr><tr><td>10</td><td>需求确认</td><td>18</td><td>24</td><td>20</td><td>20</td></tr><tr><td>11</td><td>编制需求报告</td><td>5</td><td>8</td><td>7</td><td>7</td></tr><tr><td>12</td><td>系统设计</td><td>6</td><td>10</td><td>7</td><td>7</td></tr><tr><td>13</td><td>详细设计</td><td>6</td><td>9</td><td>7</td><td>7</td></tr><tr><td>14</td><td>编码</td><td>10</td><td>18</td><td>14</td><td>14</td></tr><tr><td>15</td><td>内部测试</td><td>5</td><td>7</td><td>6</td><td>6</td></tr><tr><td>16</td><td>综合测试</td><td>6</td><td>8</td><td>7</td><td>7</td></tr><tr><td>17</td><td>管理员培训</td><td>2</td><td>3</td><td>2</td><td>2</td></tr><tr><td>18</td><td>用户培训</td><td>2</td><td>4</td><td>2</td><td>2</td></tr></table>

续表 4.5  

<table><tr><td>19</td><td>系统信息录入</td><td>1</td><td>3</td><td>2</td><td>2</td></tr><tr><td>20</td><td>数据迁移</td><td>4</td><td>7</td><td>5</td><td>5</td></tr><tr><td>21</td><td>试运行</td><td>14</td><td>14</td><td>14</td><td>14</td></tr><tr><td>22</td><td>试运行总结</td><td>3</td><td>8</td><td>5</td><td>5</td></tr><tr><td>23</td><td>软件验收</td><td>2</td><td>2</td><td>2</td><td>2</td></tr><tr><td>24</td><td>用户说明书移交</td><td>2</td><td>2</td><td>2</td><td>2</td></tr><tr><td>25</td><td>上线运行</td><td>1</td><td>1</td><td>1</td><td>1</td></tr></table>

根据国网 S 公司办公信息化平台外包项目的时间估算表，可以利用软件得到该项目的进度计划。国网 S 公司办公信息化平台外包项目进度计划如表4.6所示。

表 4.6 项目进度计划表  
Table 4.6 Project Schedule   

<table><tr><td>序号</td><td>任务名称</td><td>工期</td><td>开始时间</td><td>完成时间</td><td>前置任务</td></tr><tr><td>1</td><td>项目调研</td><td>16工作日</td><td>2024/1/4</td><td>2024/1/25</td><td></td></tr><tr><td>2</td><td>总体策划</td><td>7工作日</td><td>2024/1/26</td><td>2024/2/5</td><td>1</td></tr><tr><td>3</td><td>策划方案审批</td><td>3工作日</td><td>2024/2/6</td><td>2024/2/8</td><td>2</td></tr><tr><td>4</td><td>编制招标文件</td><td>5工作日</td><td>2024/2/9</td><td>2024/2/15</td><td>3</td></tr><tr><td>5</td><td>项目招投标</td><td>26 工作日</td><td>2024/2/16</td><td>2024/3/22</td><td>4</td></tr><tr><td>6</td><td>成立项目组</td><td>2工作日</td><td>2024/3/25</td><td>2024/3/26</td><td>5</td></tr><tr><td>7</td><td>总体方案设计</td><td>8工作日</td><td>2024/3/25</td><td>2024/4/3</td><td>5</td></tr><tr><td>8</td><td>总体方案确认</td><td>5工作日</td><td>2024/4/4</td><td>2024/4/10</td><td>7</td></tr><tr><td>9</td><td>需求调研</td><td>20 工作日</td><td>2024/4/11</td><td>2024/5/8</td><td>8</td></tr><tr><td>10</td><td>需求确认</td><td>20 工作日</td><td>2024/4/11</td><td>2024/5/8</td><td>8</td></tr><tr><td>11</td><td>编制需求报告</td><td>7工作日</td><td>2024/5/9</td><td>2024/5/17</td><td>9,10</td></tr><tr><td>12</td><td>系统设计</td><td>7工作日</td><td>2024/5/20</td><td>2024/5/28</td><td>11</td></tr><tr><td>13</td><td>详细设计</td><td>7工作日</td><td>2024/5/29</td><td>2024/6/6</td><td>12</td></tr><tr><td>14</td><td>编码</td><td>14工作日</td><td>2024/6/7</td><td>2024/6/26</td><td>13</td></tr><tr><td>15</td><td>内部测试</td><td>6工作日</td><td>2024/6/27</td><td>2024/7/4</td><td>14</td></tr><tr><td>16</td><td>综合测试</td><td>7工作日</td><td>2024/7/12</td><td>2024/7/22</td><td>15,19,20</td></tr><tr><td>17</td><td>管理员培训</td><td>2工作日</td><td>2024/6/27</td><td>2024/6/28</td><td>14</td></tr><tr><td>18</td><td>用户培训</td><td>2工作日</td><td>2024/6/27</td><td>2024/6/28</td><td>14</td></tr><tr><td>19</td><td>系统信息录入</td><td>2工作日</td><td>2024/7/5</td><td>2024/7/8</td><td>15,17</td></tr><tr><td>20</td><td>数据迁移</td><td>5工作日</td><td>2024/7/5</td><td>2024/7/11</td><td>15,18</td></tr><tr><td>21</td><td>试运行</td><td>14 工作日</td><td>2024/7/23</td><td>2024/8/9</td><td>16</td></tr><tr><td>22</td><td>试运行总结</td><td>5工作日</td><td>2024/8/12</td><td>2024/8/16</td><td>21</td></tr><tr><td>23</td><td>软件验收</td><td>2工作日</td><td>2024/8/19</td><td>2024/8/20</td><td>22</td></tr><tr><td>24</td><td>用户说明书移交</td><td>2工作日</td><td>2024/8/21</td><td>2024/8/22</td><td>23</td></tr><tr><td>25</td><td>上线运行</td><td>1工作日</td><td>2024/8/23</td><td>2024/8/23</td><td>24</td></tr></table>

# 4.3 关键路径的确定及项目总工期

由于国网 S 公司办公信息化平台外包项目存在着较多的模块，而且各类项目活动有着复杂的相关关系，这就使得在项目计划的制定上较为困难。针对这一问题，本文在进度计划的编制中引入了关键路径法，通过关键路径法来制定出科学有效的项目进度计划。最初，美国公司在开展工厂建设中，将关键路径法（CPM）用来控制建设成本和时间，通过这一方法，能够实现项目的提前完工，极大的降低了项目成本，节约了建设时间。

关键路径是不考虑资源限制的条件下，从项目开始到完工过程，根据项目进度甘特图顺推和逆推分析，在整个项目中，全部活动最早完成时间所形成的最长路线。本研究项目是基于项目 WBS 活动进行制定项目计划，根据各活动依赖关系，结合三时估算时间，绘制得到了项目甘特图，分析这一甘特图来确定关键路径。国网 S 公司办公信息化平台外包项目工作时间安排如表4.7所示。

表 4.7 项目工作时间安排表  
Table 4.7 Project Work Schedule   

<table><tr><td>编号</td><td>任务名称</td><td>工期</td><td>最早开始</td><td>最早结束</td><td>最晚开始</td><td>的可十应： 最晚结束</td><td>上户工 总时差</td></tr><tr><td>1</td><td>项目调研</td><td>16</td><td>2024/1/4</td><td>2024/1/25</td><td>2024/1/4</td><td>2024/1/25</td><td>0</td></tr><tr><td>2</td><td>总体策划</td><td>7</td><td>2024/1/26</td><td>2024/2/5</td><td>2024/1/26</td><td>2024/2/5</td><td>0</td></tr><tr><td>3</td><td>策划方案审批</td><td>3</td><td>2024/2/6</td><td>2024/2/8</td><td>2024/2/6</td><td>2024/2/8</td><td>0</td></tr><tr><td>4</td><td>编制招标文件</td><td>5</td><td>2024/2/9</td><td>2024/2/15</td><td>2024/2/9</td><td>2024/2/15</td><td>0</td></tr><tr><td>5</td><td>项目招投标</td><td>26</td><td>2024/2/16</td><td>2024/3/22</td><td>2024/2/16</td><td>2024/3/22</td><td>0</td></tr><tr><td>6</td><td>成立项目组</td><td>2</td><td>2024/3/25</td><td>2024/3/26</td><td>2024/8/22</td><td>2024/8/23</td><td>108</td></tr><tr><td>7</td><td>总体方案设计</td><td>8</td><td>2024/3/25</td><td>2024/4/3</td><td>2024/3/25</td><td>2024/4/3</td><td>0</td></tr><tr><td>8</td><td>总体方案确认</td><td>5</td><td>2024/4/4</td><td>2024/4/10</td><td>2024/4/4</td><td>2024/4/10</td><td>0</td></tr><tr><td>9</td><td>需求调研</td><td>20</td><td>2024/4/11</td><td>2024/5/8</td><td>2024/4/11</td><td>2024/5/8</td><td>0</td></tr><tr><td>10</td><td>需求确认</td><td>20</td><td>2024/4/11</td><td>2024/5/8</td><td>2024/4/11</td><td>2024/5/8</td><td>0</td></tr><tr><td>11</td><td>编制需求报告</td><td>7</td><td>2024/5/9</td><td>2024/5/17</td><td>2024/5/9</td><td>2024/5/17</td><td>0</td></tr><tr><td>12</td><td>系统设计</td><td>7</td><td>2024/5/20</td><td>2024/5/28</td><td>2024/5/20</td><td>2024/5/28</td><td>0</td></tr><tr><td>13</td><td>详细设计</td><td>7</td><td>2024/5/29</td><td>2024/6/6</td><td>2024/5/29</td><td>2024/6/6</td><td>0</td></tr><tr><td>14</td><td>编码</td><td>14</td><td>2024/6/7</td><td>2024/6/26</td><td>2024/6/7</td><td>2024/6/26</td><td>0</td></tr><tr><td>15</td><td>内部测试</td><td>6</td><td>2024/6/27</td><td>2024/7/4</td><td>2024/6/27</td><td>2024/7/4</td><td>0</td></tr><tr><td>16</td><td>综合测试</td><td>7</td><td>2024/7/12</td><td>2024/7/22</td><td>2024/7/12</td><td>2024/7/22</td><td>0</td></tr><tr><td>17</td><td>管理员培训</td><td>2</td><td>2024/6/27</td><td>2024/6/28</td><td>2024/7/8</td><td>2024/7/9</td><td>7</td></tr><tr><td>18</td><td>用户培训</td><td>2</td><td>2024/6/27</td><td>2024/6/28</td><td>2024/7/3</td><td>2024/7/4</td><td>4</td></tr><tr><td>19</td><td>系统信息录入</td><td>2</td><td>2024/7/5</td><td>2024/7/8</td><td>2024/7/10</td><td>2024/7/11</td><td>3</td></tr><tr><td>20</td><td>数据迁移</td><td>5</td><td>2024/7/5</td><td>2024/7/11</td><td>2024/7/5</td><td>2024/7/11</td><td>0</td></tr><tr><td>21</td><td>试运行</td><td>14</td><td>2024/7/23</td><td>2024/8/9</td><td>2024/7/23</td><td>2024/8/9</td><td>0</td></tr><tr><td>22</td><td>试运行总结</td><td>5</td><td>2024/8/12</td><td>2024/8/16</td><td>2024/8/12</td><td>2024/8/16</td><td>0</td></tr><tr><td>23</td><td>软件验收</td><td>2</td><td>2024/8/19</td><td>2024/8/20</td><td>2024/8/19</td><td>2024/8/20</td><td>0</td></tr><tr><td>24</td><td>用户说明书移交</td><td>2</td><td>2024/8/21</td><td>2024/8/22</td><td>2024/8/21</td><td>2024/8/22</td><td>0</td></tr><tr><td>25</td><td>上线运行</td><td>1</td><td>2024/8/23</td><td>2024/8/23</td><td>2024/8/23</td><td>2024/8/23</td><td>0</td></tr></table>

从表3.5 可以看出，总时差为零的任务分别为编号是1、2、3、4、5、7、8、9、10、11、12、13、14、15、16、20、21、22、23、24、25，即这些任务在国网S 公司办公信息化平台外包项目中扮演着重要角色。由此可以得到该项目的关键路径图。国网S 公司办公信息化平台外包项目关键路径如图4.2所示。

![](images/e00dca79de946bf7fdc4ffbc867da8e4a2ba445bcf001cd5655782d7a0bd2d97.jpg)  
图 4.2 项目关键路径图  
Fig. 4.2 Project Critical path method

# 第5 章 国网S 公司办公信息化平台外包项目进度控制措施

# 5.1 项目进度控制流程

项目进度管理主要包括进度计划的编制和进度计划的控制两个方面。其中进度计划编制是根据项目的资源配置、目标要求等对项目的实施进度的总体设计，在项目实施过程中，项目能否按照进度计划顺利、有序完成，需要对项目的进度实施过程进行控制，通过项目进度控制手段，对项目实施过程进行监测、偏差控制及关键进程控制等，从而保证项目按照进度计划的设计完成。

国网 S 公司办公信息化平台开发项目进度控制实际工作中，必须要根据实际情况来进行制定进度控制流程，及时发现出现的问题以及隐藏的风险，尽可能的降低偏差所造成的影响。将各类不可控情况考虑在内，分析不可控发生所带来的影响，以及如何有效的处置，从而实现对项目进度科学准确的控制，以保证按照计划落实项目。项目进度控制流程如图5.1所示。

![](images/c805d2dba508db2ef77d3ed45a52f94ca06552f7afd65421fbac77d54a03ea27.jpg)  
图 5.1 项目进度控制流程图  
Fig. 5.1 Control-flow diagram of Project Progress

# 5.2 项目需求变更及文档管控

# 5.2.1 项目需求变更管控

在软件开发中，经常会出现需求变更的情况，而其原因也是多种多样的。在国网 S 公司办公信息化平台开发项目中，需求变更会对项目的进度产生较大的影响。针对该项目设计情况，在项目需求变更上提出有效的控制方法。

（1）需求变更影响评估

在该项目开发中，避免也面临着葛总项目变更，必须要对项目变更进行科学准确的评估，了解其对整个项目带来的风险和影响。在出现项目变更时，软件开发人员需要做好估算，而他们因为自身情绪或者偷懒等原因，可能会夸大变更带来的影响，从而规避变更。在在国网 S 公司办公信息化平台开发项目中，项目总负责人也就是 S 公司副总经理要组织做好需求变更的评估工作，由国网 S 公司信息部和软件开发公司共同对需求变更进行研判，使国网 S 公司清楚认识到变更的影响，有利于软件设计目标的实现。

（2）严格执行需求变更上报和审批

在项目活动中，对于所出现的需求变更，要开展上报评审工作，如果频繁变更则会导致计划无法开展，增加整体成本，并且严重拖延项目的完成进度等。在国网 S 公司办公信息化平台开发项目的实施过程中，一旦需求发生重大变化，需要在对变更风险及影响进行全面分析的基础上，上报项目管理部，形成报表文档，更好的开展变更管理。

# （3）需求变更确认

在需求需要变更时，要与国网 S 公司相关单位和部门进行沟通，确认变更所产生的影响以及对应的风险，或了解是否存在变更替代方案，或将变更推入到下次计划。若必须要开展变更的，需要甲方签字确认变更，然后开展项目变更。

# （4）制定需求变更表制度

对于项目变更要制定项目需求变更表，将相应的变更内容填入其中，主要包括：变更项目模块，合同号，变更理由，变更人，变更时间，变更内容，变更风险等级，甲方意见及签字等。

变更项目计划，要对项目需求文档、项目计划、项目风险表做好变更工作，并通知相关人员变更的情况，完成变更。

# 5.2.2 项目文档管理

软件项目管理中，文档管理是经常忽视的部分，不少项目人员认为编写文档完全是一种浪费时间和精力的事情，所以一般都会忽略掉。不过，在软件项目进度管理中，项目文档是非常重要的依据，这也是很好的一种监督形式。在本项目

中，项目文档管理包含了如下几类：

（1）项目需求文档

在项目的开展过程中，项目需求文档是非常重要的一个文档，这也是国网 S公司的重要工作内容。在这一文档中，还需要对变更内容表明出来，并且注明变更时间、原因，变更对应的负责人。另外，在整个项目开展落实中，开发人员正是基于这一文档来推动开发工作，确保开发软件能够满足需求。

（2）软件项目系统架构文档

系统架构文档是在需求分析基础上设计编制的，在这一文档中，也包括了设计方案内容，以及数据流程图、估算架构压力能力等。这一文档可以确保不会由于需求变更而带来失控影响。

# （3）软件接口文档

本项目系统的接口非常多，并且接口类型各异，参数也存在着差别，接口功能也各不相同，这就导致了开发人员必须要慎重对待接口的开发工作，要将接口相关的参数、功能等信息一一对应的标注在上面，避免出现错误而带来影响。开发人员通过软件接口文档，可以准确的估算出工作量的大小，这样能够为工作的开展，工作的评估，以及项目进度的控制提供指导支持。如通过了解接口的开发效率情况，可以评估是否能够按时开展后续工作。而由于这也是一类开发工作，所以乙方应该负责向甲方提供这一文档。

（4）测试文档

在开展功能模块测试时，测试人员需要将测试结果记录下来，而这就是测试文档，并且还需要将测试用例记录其中。如：登录账号的类型、怎么操作的，结果怎么样。研发人员通过测试文档可以复现存在的bug，而且也能够作为一种监督形式，避免研发人员赶工而不关心产品质量。并且，根据测试结果也可以对开发人员的能力进行量化评估，更好的对开发人员进行评价分析，为后续的项目进度控制提供依据。

（5）研发人员工作文档

这一文档是将研发人员的每日工作、进展情况记录下来。而管理人员则依据文档，就可以清楚的了解到研发人员每日是否完成了相应的工作任务目标，工作的质量如何，是否面临问题等。项目经理也可以通过文档内容，来分析是否有资源处于闲置状态，以更好的优化资源。而且这也是考核的重要依据。

# 5.3 项目进度监测与报告

# 5.3.1 项目进度监测

（1）项目内部进度会议

每日下午5—6点，项目组所有成员举行项目进度会，会议持续一个小时。在户以上，所有成员将自身的任务进行简单的描述出来，并且及时汇报完成情况，以及面临的问题，如果是属于一些不重要的非业务性或非技术性问题，跳过这些问题并不会影响到项目进度，则可以单独记录下来，推动任务的继续落实。而如果是会产生重要的影响，无法直接跳过的问题，则通过会议讨论的刑事，积极地找出解决的办法。在汇报完当日的进度后，则还需要分配次日任务。另外，通过每日召开会议的方式，也能够团结项目组成员，协调配合好内部工作。

（2）项目周报

项目管理者在每周五下午三点前，完成对项目进度的整体工作，并通过报告的形式提交给项目管理小组，由后者对报告内容进行审查。在这一汇总报告中，包括每周任务量，以及每一项任务活动的完成情况，以及在本周任务进程中面临的问题，是否存在内部无法解决的问题。还需要对进度风险进行预测。在报告的最后，做好下周任务安排，预计下周的工作进度。

# （3）项目管理软件

项目组当前在项目进度管理方面使用 MicrosoftProject。在每日工作下班结束前，所有人进入内网，将本日的任务进度填写进来。这样就能够使得项目经理可以全面了解所有任务完成情况，做好统筹安排，并根据汇报的进度，及时更新甘特图，然后下发给所有成员。次日，所有成员可以了解到更新进度，并查看当天的任务安排。

# 5.3.2 项目进度报告

项目经理通过例会，查看项目最新进度情况，并且结合周报，对本周的进度有一个基本的掌握和测度，并对项目进程进行总结，具体步骤如下：

（1）项目组成员在当天下班前在组内进度表上填写本日任务进度，对于没有  
完成任务的情况，则需要将问题填写清楚。（2）项目经理对每周进度进行测量，将成员进度实际与计划对比，了解进度  
是否达标。（3）在核对完数据后，及时的进行甘特图的更新，以能够对项目活动开展实

现动态跟踪。

（4）项目管理者根据最终项目进度数据，对项目各阶段和里程碑进度进行计算，了解其中的进度偏差，并汇总分析结果，以报告的形式发送给相关人员了解。

# 5.4 项目进度分析与纠偏

# 5.4.1 项目进度分析

在编制完成项目进度报告后，项目经理通过项目组内部会议的方式，对组内成员说明报告中的业务和技术进度问题，从而让所有人了解到项目进度情况，以及其中面临的风险。在周会上根据项目进度偏差的结果，与组内成员讨论分析关键路径和非关键路径，并且确认相应的缓冲消耗成都，计算分析本周进度和存在的风险。若当前不存在延期风险，则无需增加其他的措施；而如果分析发现了延期风险，必须要进一步分析风险出现的路径，或者是哪一条路径严重消耗了缓冲，在确定路径活动之后，与相关负责人员探讨分析问题原因，提出解决的有效方案。

项目经理召开周会，并对上周存在的风险，协同团队来进行风险定位，找出风险的路径所在，并分析风险原因，了解是否已经反馈和解决了前期的风险问题。并且从现阶段来评估前一周的风险点，如果当前阶段并未发此类风险点，则对本周偏差进行计算，分析在其中所产生的新的风险点。

项目经理汇总项目进度报告，并且去掉其中的一些专业性内容，仅保留项目进度情况，汇报给直系领导，如果存在着内部无法解决的问题，则直接反映给领导，提出希望得到的资源和支持。

# 5.4.2 项目进度纠偏

通常对于非关键链，如果出现了接驳缓冲消耗，若是这种消耗不会对关键链路带来影响，则不需要增加相关措施。而若这种消耗对关键链路带来了影响，则必须要采取相应的措施，避免带来的影响延误项目工期。若关键链路对项目缓冲产生了严重消耗，这就表示必须要开展分析，了解问题的具体原因，并采取相应的调整措施，若是情况特殊，则还需要变更进度计划。

结合国网S公司办公信息化平台开发项目的特点，在该项目进度出现偏差时，可采取以下措施进行纠偏。

（1）加班

在项目进程中，并非对于所有的延迟情况，都必须要采取措施来调整应对。

对于偶然出现的短期延迟的情况，如在某一阶段，人员突然短缺而带来的暂时性延迟，并不会对缓冲带来严重消耗，可以等待恢复正常，不需要制定相应的调整措施。而如果出现的情况会对关键路径带来影响，或者是问题直接发生在关键路径上，则需要安排人员加班工作，以能够将延误的工期通过赶工的方式补回来。不过，要明确一点，加班只是在特殊情况下的短期操作方式，而不能够长期加班，这不仅会影响到工作人员的积极性，而且导致整体工作效率降低，最终产生负面作用。

# （2）改变资源分配

在多数情况下，项目活动的依赖关系，会随着人力资源的分配变化而发生变化。如增加人力资源的分配，则原本因为人力资源限制而需要前后开展的活动就可以同时进行，这样能够缩短一些路径的消耗时间，从而缩短链路上的工期。

# 第6 章 国网S 公司办公信息化平台外包项目进度管理保障措施

# 6.1 组织保障

# 6.1.1 成立 S公司项目领导小组

国网 S 公司办公信息化平台开发项目的实施过程中，国网 S 公司与软件开发企业成立了联合项目组，负责项目的管理与实施工作。由于国网 S 公司办公信息化平台开发项目在实施过程中，需求分析、系统设计、培训、数据准备、试运行等各个过程都需要国网 S 公司各相关部门的配合，需要国网 S 公司各相关部门负责人及员工的重视和积极认真参与，为了提高国网 S 公司各部门负责人及员工对该项目的重视程度，国网S 公司应成立项目领导小组。项目领导小组组长由国网S公司总经理担任，副组长为各副总经理，领导小组成员为各部门负责人，领导小组下设办公室，办公室设在科技互联网部。科技互联网部部长既担任联合项目组的甲方负责人，也是国网 S 公司项目领导小组的办公室主任，具体负责项目的沟通协调、组织工作。国网 S 公司办公信息化平台开发项目领导小组组织结构如图6.1 所示。

![](images/1f89d90e91bc3a3c888e75270a475534797421a8ed75787a8b3e0d70c0f31e90.jpg)  
图 6.1 项目领导小组组织结构图  
Fig. 6.1 Organizational chart of Project Leading Group

# 6.1.2 明确各参与方责任

作为技术人员密集型产业，国网 S 公司办公信息化平台开发项目中的核心在于人，因此，项目人员管理工作是任务开展的关键和前提。同时，由于国网 S 公司办公信息化平台开发项目涉及到国网 S 公司和软件开发企业两个公司，以及国网 S 公司的各部门，在项目实施各阶段，还需要两个公司以及国网 S 公司各部门间的配合，所以必须要从组织层面明确项目参与方的责任，明确责任部门和协助部门，从而使各项工作的开展任务清晰、权责明确，避免出现推诿扯皮和责任不清导致的项目进度受到影响。

国网 S 公司在办公信息化平台开发项目中的职责是制定项目总体目标，通过调研、招投标确定软件开发企业，签订软件开发协议，制定项目管理制度和准则，对项目实施进行整体协调和管理，统筹安排好项目各方工作，组织各阶段评审，以及项目完成后的最终评审，推动项目落实，最终实现项目完工。

国网 S 公司各部门、各分公司和业务支撑机构是办公信息化平台的最终用户，也是项目非常重要的干系人。所以，相关部门参与项目建设，重点参与到需求、验收和试运行阶段，各相关部门可结合自身情况调派业务骨干对办公信息化平台参与需求分析、试运行和验收等工作。

软件开发企业是国网 S 公司通过招投标确定的办公信息化平台软件开发的第三方科技公司，根据国网 S 公司指导要求以及合同规定，来开发软件产品，并将开发完成的软件交付运营。首先，对国网 S 公司的情况和需求进行调研，充分的利用自身的专业技术能力和开发经验，提供有效针对性的解决方案，主要是需求调研和分析、架构设计、软件开发、配合测试、项目验收等工作，推动设计方案的落实，并开展产品培训，向公司提供系统运维服务。

下表 6.1 所示的是国网 S 公司办公信息化平台开发项目责任分配情况。

表 6.1 国网 S公司办公信息化平台开发项目责任分配表  
Table 6.1 Allocation of Responsibilities for the Office Information Platform Development Project of State Grid S Company   

<table><tr><td>编号</td><td>任务名称</td><td>S公司管理层</td><td>1 科技互联网 部</td><td>软件开发公 司</td><td>S公司各相关 部门</td></tr><tr><td>1</td><td>项目调研</td><td></td><td>→</td><td></td><td></td></tr><tr><td>2</td><td>总体策划</td><td></td><td>→</td><td></td><td></td></tr><tr><td>3</td><td>策划方案审批</td><td>■</td><td>&gt;</td><td></td><td></td></tr><tr><td>4</td><td>编制招标文件</td><td></td><td>→</td><td></td><td></td></tr><tr><td>5</td><td>项目招投标</td><td></td><td>→</td><td></td><td></td></tr><tr><td>6</td><td>成立项目组</td><td></td><td>&gt;</td><td></td><td></td></tr><tr><td>7</td><td>总体方案设计</td><td></td><td></td><td></td><td></td></tr><tr><td>8</td><td>总体方案确认</td><td></td><td></td><td></td><td></td></tr><tr><td>9</td><td>需求调研</td><td></td><td></td><td></td><td>&gt;&gt;</td></tr><tr><td>10</td><td>需求确认</td><td></td><td></td><td></td><td></td></tr><tr><td>11</td><td>编制需求报告</td><td></td><td></td><td></td><td></td></tr><tr><td>12</td><td>系统设计</td><td></td><td></td><td></td><td>&gt;</td></tr><tr><td>13</td><td>详细设计</td><td></td><td></td><td></td><td></td></tr><tr><td>14</td><td>编码</td><td></td><td></td><td></td><td></td></tr><tr><td>15</td><td>内部测试</td><td></td><td></td><td></td><td></td></tr><tr><td>16</td><td>综合测试</td><td></td><td></td><td></td><td></td></tr><tr><td>17</td><td>管理员培训</td><td></td><td></td><td>■</td><td></td></tr><tr><td>18</td><td>用户培训</td><td></td><td></td><td></td><td></td></tr><tr><td>19</td><td>系统信息录入</td><td></td><td></td><td></td><td></td></tr><tr><td>20</td><td>数据迁移</td><td></td><td></td><td></td><td>→</td></tr><tr><td>21</td><td>试运行</td><td></td><td></td><td></td><td></td></tr><tr><td>22</td><td>试运行总结</td><td></td><td></td><td></td><td></td></tr><tr><td>23</td><td>软件验收</td><td></td><td></td><td></td><td></td></tr><tr><td>24</td><td>用户说明书移交</td><td></td><td></td><td></td><td></td></tr><tr><td>25</td><td>上线运行</td><td></td><td></td><td></td><td></td></tr><tr><td></td><td>责任部门：</td><td></td><td></td><td></td><td></td></tr><tr><td></td><td>配合部门：</td><td></td><td></td><td></td><td></td></tr></table>

# 6.2 制度保障

# 6.2.1 建立健全项目管理制度

一是建立《国网 S 公司办公信息化平台外包项目建设指导意见》，在这一指导意见中，不仅包含了项目整体管理，而且还有需求、开发、测试、部署等几个方面的细化管理，对建设过程进行规范，保证工作效率和质量，还能够对过程风险起到很好的防范作用。二是建立《国网S 公司办公信息化平台配置管理规范》，主要是为了对配置管理活动加以明确，指导项目相关人员有效规范的开展日常配置管理工作。三是优化《国网S公司办公信息化平台配置管理实施细则》，设定专人来制定配置管理体系，动态跟踪执行情况，优化配置相关的人员和职责内容，规范项目配置管理，确保实现配置的一致性、完整性和可追溯性。

# 6.2.1 明确需求变更管理流程

对于软件开发项目来讲，需求分析是开发过程中最为关键和核心的环节，系统分析、详细设计以及后续的编码等活动都建立在需求分析的基础上，所以需求变更将会对软件开发的后续环节产生较大影响，也会对软件开发项目进度产生较大影响，所以，为了确保国网 S 公司办公信息化平台外包项目的实施进度，联合项目组要重视和加强需求变更的管理。联合项目组要建立《国网 S 公司办公信息化平台外包项目需求变更实施规范》，包括变更分类及流程，目的是规范需求变更实施过程，构建规范的需求变更控制管理流程，避免出现随意变更，尽可能的减少由变更多带来的风险和不利影响，有效防范项目实施风险，确保项目的实施进度。国网 S 公司办公信息化平台外包项目需求变更流程如图6.2所示。

![](images/e625f6a1ba89d6ab1a0431f1e030cd63e48eb9a572988bfd76a0281166a2eb91.jpg)  
图 6.2 需求变更流程图  
Fig. 6.2 Requirements Change Flow Chart

# 6.3 人力保障

# 6.3.1 加强内外部的沟通协调

一是加强与国网 S 公司各部门、各单位的协调沟通，由于国网 S 公司办公信息化平台是公司办公自动化项目，在项目实施过程中，特别是需求分析、系统设计等环节中，需要公司各部门相关人员的配合，为了保证项目进度，在项目开始前，项目负责人也就是科技互联网部要与各部门做好沟通，根据项目实际需求和工作安排，做好人员安排计划，提前让相关人员做好时间安排，保证项目需要时及时参与项目；二是加强与软件开发企业的沟通协调，确保软件开发公司能够按照项目实施需求做好人力资源配置，确保安排具有相应能力和足够数量的人员开展项目，同时，国网 S 公司还要在项目实施期间，定期对软件开发公司的人力资源配置情况进行调研，确保项目按照进度计划有序实施。

# 6.3.2 实施有针对性地人员培训

软件开发是一个非常专业化的项目，国网 S 公司的大多数员工对于软件开发的流程、需求分析方法等并不了解。而国网 S 公司办公信息化平台外包项目的软件开发工作中，需求分析、系统设计、软件测试等都需要国网 S 公司各部门、各单位相关人员的配合，甚至是起到关键作用的。比如在需求分析阶段，国网 S 公司相关部门的员工要能够清晰地、明确地、系统地描述出业务需求、业务流程等，而且需求分析不是简单地对于业务操作流程的一个说明，所以，要想让公司员工配合软件开发企业做好需求分析等工作，需要对国网 S 公司员工开展针对性地培训，使公司员工具有系统性思维，对软件开发流程、分析方法有所了解，保证需求分析、系统设计等活动的顺利进行，从而保障项目进度的完成。

# 6.4 技术保障

# 6.4.1 加强项目的规范性管理

为了提高国网S公司办公信息化平台外包项目的开发效率及平台的可维护性，联合项目组可以通过制定符合该平台实际的开发规范来提高项目质量，建立《国网 S 公司办公信息化平台开发基础组件库》、《国网 S 公司办公信息化平台编程手册》等技术规范和流程制度。规范包括图标设计规范、命名规范、接口规范等相关的规范内容。通过设置统一规范标准，使得软件开发人员有遵循的方向，基于标准来开发设计，提高国网 S 公司办公信息化平台开发的规范性，提高代码的可维护性和可读性。设置专门的配置管理员，联合项目小组根据规范和评审标准来开展版本管理，对于开发成果，规定每 2 周进行一次汇总，初步检查当前的开发结果，若果基线符合标准，则在评审后签字确认。

# 6.4.2 加强项目技术管理

对于国网 S 公司办公信息化平台外包项目，从其本身来看，技术架构非常复杂，需要加强技术管理，着重强调质量把关。为了达到这一目的，必须要设定技术保障措施：一是构建项目技术信息共享库，为项目开发提供相关的技术解决措施；二是国网 S 公司根据技术开发人员的基本情况，以及项目技术难度，派遣专家对人员进行培训，并协助处理一些技术上的难点；三是引入自动化测试平台，通过这种自动化测试的实施，有效的提升了测试效率；四是强化项目的质量管理，统一质量管理标准，提升管理水平和质量，根据实际情况制定《项目质量管理标准》，在项目的各环节、各阶段对质量进行检查，确保项目的质量，从而减少返工，确保项目按照进度计划完成。

# 第 7 章 结论与展望

# 7.1 研究结论

论文基于项目管理和进度管理相关理论，以国网 S 公司办公信息化平台外包项目为研究对象，在对该项目基本情况、建设主要内容及实施流程进行分析的基础上，编制了国网 S 公司办公信息化平台外包项目进度计划，制定了项目进度控制措施，以及项目进度管理的保障措施。主要研究结论如下。

（1）确定了项目实施流程

通过对国网 S 公司办公信息化平台外包项目基本情况的梳理，以及项目建设的主要内容，确定了该项目实施的主要流程。首先，国网 S 公司针对该项目进行前期的调研，形成总体策划方案，并报公司管理层审批；其次，是项目的软件开发阶段，包括总体方案设计、需求分析、系统设计、详细设计、编码和软件测试等内容；再次，要对国网 S 公司员工进行软件使用、维护等内容进行培训；然后，要将公司基本数据录入和迁移到新开发的办公信息化平台中；最后，要进行国网S公司办公信息化平台的试运行和验收。

（2）编制了项目进度计划

根据国网 S 公司办公信息化平台外包项目的主要流程，对该项目进行了工作结构分解，确定项目活动逻辑关系，对项目活动时间进行估算，确定了项目的关键路径及项目总工期，确定了项目里程碑计划。通过进度计划的编制，该项目于2024年1月4日开始实施，8月23日完成，能够满足国网S公司拟定的2024年9月1日正式上线运行的计划。

（3）制定了项目进度控制措施

为了确保国网 S 公司办公信息化平台外包项目在实施过程中，按照制定的项目进度计划实施并完成，制定了项目进度控制的措施，包括项目进度控制流程，项目进度监测与报告的方法，项目进度分析与纠偏的措施。通过制定的进度控制措施可以进一步保障项目按照进度计划完成。

# 7.2 不足与展望

在对国网 S 公司办公信息化平台外包项目进度管理进行研究的过程中，由于之前对于软件开发项目流程，特别是外部项目的管理和实践方面经验不是很多，同时不同的项目也具有不同的特点，其他项目的进度计划及管理只具备一定的参

考性，所以在对项目流程进行分析以及工作结构分解时，还有需要深入研究的空间，以提升其科学性和合理性。之后，将在工作中加强调研和学习，为公司之后的项目管理提出更加科学的建议和理论支撑。

#

参考文献   
[1] Nazimko V V, Zakharova L M. Project schedule expediting under structural and parametric uncertainty[J]. Engineering Management Journal, 2023, 35(1): 29-49.   
[2] Acebes F; Poza D; Gonzalez V J M. Stochastic Earned Duration Analysis for Project Schedule Management[J] Engineering, 2022, 9(2) : 150-163.   
[3] Ansari R; Khalilzadeh M; Hosseini M R. A Multi-objective Dynamic Optimization Approach to Project Schedule Management: A Case Study of a Gas Field Construction[J] KSCE Journal of Civil Engineering, 2021, (prepublish) : 1-9.   
[4] Yu Z; Hui D. Improved Critical Chain Based R&D Project Schedule Management Optimization[C]//Construction Industry Committee, China-Asia Economic Development Association,Institute of Engineering Management, Beijing University of Civil Engineering and Architecture,Intelligent Construction and Automation of Construction Engineering.Conference Proceedings of the 8th International Symposium on Project Management, China (ISPM2020).Aussino Academic Publishing House, 2020:6.   
[5] Zou J; Wang H; Lei Y. Application of Project Progress Management Based on Building Information Modeling[C]//Singapore Management University. Proceedings of 3rd International Conference on Education,Economics and Management Research(ICEEMR 2019)(Advances in Social Science,Education and Humanities Research, VOL. 385). Atlantis Press, 2019: 3.   
[6] Suci M; Sugarindra M. Utilizing project management software in project scheduling: a case study[J] IOP Conference Series: Materials Science and Engineering, 2019, 528(1) : 012037.   
[7] Mu R; Wu P; Haershan M. Pre-contractual relational governance for public–private partnerships: how can ex-ante relational governance help formal contracting in smart city outsourcing projects?[J] International Review of Administrative Sciences, 2023, 89(1) : 112-128.   
[8] Li X; Chen Y; Sun Y. Research on collaborative supervision mechanism of project process management and control of scientific research outsourcing projects[C]//广 东 外 语 外 贸 大 学 , AEIC Academic Exchange Information Center(China). Proceedings of International Conference on Management Science and Industrial Economy(MSIE 2019)(Advances in Economics, Business and Management Research, VOL. 118). Atlantis Press, 2019: 5.   
[9] Al A S; Mohiuddin M; Su Z. The Client and Service Provider Relationship in IT Outsourcing Project Success: The Moderating Effects of Organizational Attitudes on Knowledge Sharing and Partnership Quality[J] Journal of Global Information Management (JGIM), 2022, 30(1): 1-27.   
[10] Gupta R S. Management of Innovation and Intellectual Property in Outsourcing Projects[J] International Journal of Innovation and Technology Management, 2021, 18(06) : 23-29.   
[11] Large R O; Pache G; Merminod N. Managers’ intention to participate in logistics outsourcing project groups: the influence of personal characteristics[J] Supply Chain Forum: An International Journal, 2021, $2 2 ( 1 ) : 1 \AA 1 5$ .   
[12] Kim S; Cho N; Yoo M. Narcotics information management system in South Korea: system development and innovation[J] BMC health services research, 2023, 23(1) : 73.   
[13] Scott E; Campo M. An adaptive 3D virtual learning environment for training software developers in scrum[J] Interactive Learning Environments, 2023, 31(8) : 5200-5218.   
[14] Di F R; Mayes M L; Richard R M. A Perspective on Sustainable Computational Chemistry Software Development and Integration.[J] Journal of chemical theory and computation, 2023, 19(20) : 7056-7076.   
[15] Kassaymeh S; Abdullah S; Al B M A. An enhanced salp swarm optimizer boosted by local search algorithm for modelling prediction problems in software engineering[J] Artificial Intelligence Review, 2023, 56(3) : 3877-3925.   
[16] Yue M; Feng H. Optimization and practice of requirement analysis based on prototype portrait in software development process[J] Journal of Computational Methods in Sciences and Engineering, 2021, 21(5) : 1339-1347.   
[17] 杨 凯 . 运 用 管 理 学 工 具 进 行 工 程 进 度 管 理 的 研 究 [J]. 中 国 工 程 咨

询,2022,269(10):50-55.

[18] 吴云峰,王科文,董鹏.Project 软件在舰船维修进度管理中的应用[J].现代商贸工业,2022,43(24):243-245.  
[19] 徐希鹏.信息技术背景下 BIM 软件在项目进度管理中的应用探析[J].现代信息科技,2023,7(03):127-130.  
[20] 刘伟煜,王磊,张虎等.基于机器学习的 IT 项目进度管理系统的设计与实现[J].电脑知识与技术,2023,19(05):26-29.  
[21] 杨秋林. J 市农村人居环境综合监管平台项目进度管理研究[D].电子科技大学,2022.  
[22] 赵思奇. XX 公司 A 游戏项目开发进度管理研究[D].电子科技大学,2022.  
[23] 马逸宇,蒋黎晅,陈江红等.基于扎根理论的现代综合医院项目进度管理研究[J].工程管理学报,2022,36(06):133-137.  
[24] 吴建新.房地产建筑项目进度管理策略[J].大众标准化,2023, 385(01):145-147.  
[25] 叶明珠,汪明,彭礼勇.基于BIM 的地铁施工进度管理方案研究与应用[J].铁路计算机应用,2023,32(01):63-67.  
[26] 李 含 宇 . 基 于 计 划 评 审 技 术 的 工程项目进度管理[J]. 中国建筑装饰装修,2022,244(16):60-62.  
[27] 郭江海.国有企业科研项目进度管理问题与对策研究[J].企业科技与发展,2022,490(08):154-156.  
[28] 陈 丽 , 李 晓 利 , 余 俊 等 . 软 件 外 包 项 目 管 理 关 键 问 题 研 究 [J]. 科 技 视界,2021,360(30):152-154.  
[29] 王 秀 梅 . 浅 谈 高 校 软 件 外 包 项 目 的 优 化 管 理 [J]. 中 国 管 理 信 息化,2021,24(03):95-96.  
[30] 李卫福.发电企业外包项目管理实践[J].科技风,2020,417(13):193.  
[31] 李海涛,甄慧琳.档案数字化外包项目管理现状问题及对策研究——以广州市调研为例[J].档案学研究,2019,171(06):86-93.  
[32] 张 莉 妹 . 软 件 测 试 外 包 驻 场 测 试 中 信 息 安 全 问 题 的 研 究 [J]. 科 技风,2021,462(22):144-145.  
[33] 陈 国 卫 , 周 雨 菁 . 装 备 软 件 外 包 全 过 程 分 析 [J]. 装 备 制 造 技术,2020,309(09):182-184.  
[34] 喻 珠 晗 . 浅 谈 软 件 服 务 外 包 过 程 存 在 的 风 险 及 管 控 措 施 [J]. 商讯,2020,214(24): $1 5 5 { + } 1 5 7$ .  
[35] 冉姝玲,杨柳青.软件工程技术在系统软件开发过程的应用研究[J].长江信息通信,2022,35(04):132-134.  
[36] 李 映 晟 . 软 件 开 发 过 程 中 对 项 目 管 理 技 术 的 有 效 运 用 研 究 [J]. 软件,2021,42(10):96-98.  
[37] 程明霞.基于互联网的远程办公软件应用[J].电子技术,2022,51(09):304-305.  
[38] 王 虹 . 计 算 机 办 公 软 件 在 办 公 室 的 应 用 [J]. 现 代 工 业 经 济 和 信 息化,2021,11(12):121-122.  
[39] 邢嘉舒,徐硕博.安卓手机移动办公 App 软件的开发及应用研究[J].科技资讯,2021,19(32):20-22.  
[40] 张萍.工程化的软件开发管理方法研究[J].中国信息化,2019,304(08):58-59.

# 致谢

时间总是在悄悄逝去之后才让人回味感到它的匆匆，正如这三年的研究生生活竟如此突然地接近尾声，回首这三年的时光，有迷茫、有憧憬，但更多的是收获，这些收获离不开各位老师、朋友和家人的支持与帮助，虽然我的文字在大家于我的帮助面前一定是苍白的，但我仍愿将心中的感谢刻印于此，并铭记于心。

第一份感谢要送给东北大学的各位老师，特别是我的导师侯卉教授，是你们在我的求学生涯中毫无保留地将知识传授，并以极大的耐心和细心来让一个一边工作一边求学的人能够接受、消化，特别是在毕业论文的写作期间，从论文的选题、起草到后来多次的修改，导师侯卉教授都给予了高屋建瓴的指导和严谨务实的审阅，使得论文得以顺利完成。同时，要感谢各位评审老师，是你们每次对我论文的悉心指导才让我文章逐步完美，你们的建议也让我对论文的研究意义有了更加深刻的理解。正是东北大学良好的学术氛围和各位老师严谨的学术精神，才让知识的光芒照得更远、更亮，衷心地祝福各位老师万事顺遂、桃李芬芳。

第二份感谢要送给我的同事——国网公司的各位领导和同仁，在我从象牙塔初入社会之时，是各位不吝言传身教，而我再入求学之门，又是各位倾囊襄助，使我在工作中不断进步、羽翼逐渐丰满，这实在是我的幸运，感激之情如此无法表达万一。

第三份感谢要送给我的父母、家人，二十多年来，从学走路到学识字，从学说话到学做人，每一天、每一时，都是我的父母、亲人精心呵护、培育，在我的成长之路上，在我的生活点滴中，是你们不言苦累，默默地支持我，这一份爱更是无法用我拙笨的文字所能表达的，唯有用我的努力和成长来验证你们的付出！

应该感谢的人很多，我只有在以后的工作生活中，踏实、严谨，不断进步，来回报每一个爱我的人！
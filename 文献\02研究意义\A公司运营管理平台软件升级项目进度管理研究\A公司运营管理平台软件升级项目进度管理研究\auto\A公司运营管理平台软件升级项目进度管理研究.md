# A公司运营管理平台软件升级项目进度管理研究

作者姓名 董 婷指导教师姓名、职称 张蔚虹 教授申请学位类别 工商管理硕士

# 西安电子科技大学

# 硕士学位论文

# A 公司运营管理平台软件升级项目进度管理研究

作者姓名：董婷

领 域：工商管理

学位类别：工商管理硕士

指导教师姓名、职称：张蔚虹 教授

学 院：经济与管理学院提交日期：2020 年 8 月

# Research on the Project Schedule Management of A company operation management platform software upgrade

A thesissubmitted to   
XIDIAN UNIVERSITY   
in partial fulfillment of the requirements   
for the degree of Master   
in Business Administration

By

Dong Ting Supervisor: Zhang Weihong Title:Professor August 2020

# 西安电子科技大学学位论文独创性（或创新性）声明

秉承学校严谨的学风和优良的科学道德，本人声明所呈交的论文是我个人在导师指导下进行的研究工作及取得的研究成果。尽我所知，除了文中特别加以标注和致谢中所罗列的内容以外，论文中不包含其他人已经发表或撰写过的研究成果；也不包含为获得西安电子科技大学或其它教育机构的学位或证书而使用过的材料。与我一同工作的同事对本研究所做的任何贡献均已在论文中作了明确的说明并表示了谢意。

学位论文若有不实之处，本人承担一切法律责任。

本人签名： 日 期：

# 西安电子科技大学关于论文使用授权的说明

本人完全了解西安电子科技大学有关保留和使用学位论文的规定，即：研究生在校攻读学位期间论文工作的知识产权属于西安电子科技大学。学校有权保留送交论文的复印件，允许查阅、借阅论文；学校可以公布论文的全部或部分内容，允许采用影印、缩印或其它复制手段保存论文。同时本人保证，结合学位论文研究成果完成的论文、发明专利等成果，署名单位为西安电子科技大学。

保密的学位论文在年解密后适用本授权书。

本人签名： 导师签名：日 期： 日 期：

# 摘要

随着信息技术的不断发展及大数据时代的到来，研究如何运用科学的项目进度管理方法管理具有复杂度高、需求不明确、风险高、开发周期长等特点的大型软件开发项目具有一定的实践意义。A公司以提供软件开发服务为主营业务，在软件开发项目的执行过程中时常面临项目进度的滞后，影响企业在行业内多年积累的口碑。导致进度滞后的主要原因分析，首先，未全面了解项目需求，工作分解不到位。其次，项目工作量估算不准确。最后，项目执行过程采用的管理和控制方法不科学。

在阅读大量文献的基础上，本文以 A 公司和 A 公司的运营管理平台软件开发项目、运营管理平台软件升级项目的进度计划执行的管控为研究对象，陈述了本次研究所涉及的项目管理和项目进度管理的理论知识。然后采用问卷调查方法针对 A 公司和 A 公司运营管理平台软件开发项目中存在的问题进行调研和分析总结，在 A 公司运营管理平台软件升级项目的执行中从项目工作的分解、项目工作时间估算、项目计划编制和项目监控及纠偏四个方面进行优化。研究运用工作分解结构、三点估算法和关键链法有效的解决项目进度执行中存在的问题，并采用基于关键链法和挣值法的软件项目监控模型，在软件项目实施过程中持续监控项目执行情况，分别计算关键链和非关键链的挣值，分析并找出项目进度执行偏差的原因，通过合理的改善措施确保了项目按期保质完成，有力的验证了本文改进的项目进度管理方法的有效性；最后，本论文研究总结了基于工作分解、计划编制、里程碑监控、进度监控、计算纠偏的软件开发项目进度管控的工作方法，同时为项目提供组织、技术和人员的的保障措施，确保软件开发项目的顺利实施和完成交付。

本文认为项目进度管理首先制定出合理、经济的进度计划，然后在该计划的执行中，采用科学的监控方法检查实际项目进度与计划项目进度是否相一致，以确保项目按期完成。因而，项目进度计划和项目进度监控方法是项目进度管理的重中之重。

通过对 A 公司运营管理平台软件升级项目的深入研究，证实了关键链法、挣值法能够合理有效的进行项目成本与进度的同步控制，能够动态实时监控项目进度，及时反馈项目进度与成本执行情况，也能够为 A 公司及同行业其他同类项目的进度管理提供参考依据和理论支撑。

关键词：运营管理平台， 项目进度管理， 三点估算法， 关键链， 挣值法

# ABSTRACT

With the continuous development of information technology and the advent of the era of big data, research on how to use scientific project schedule management methods to manage large software development projects with high complexity, unclear requirements, high risks, and long development cycles has certain practices significance. Company A is mainly engaged in the provision of software development services. During the implementation of software development projects, it often faces lag in project progress, which affects the company’s reputation in the industry for many years. Analysis of the main reasons leading to the lag in progress. First of all, the project requirements are not fully understood and the work breakdown is not in place. Second, the project workload estimate is not accurate. Finally, the management and control methods used in the project execution process are not scientific.

On the basis of reading a lot of literature, this article takes the management and control of the execution plan of company A and company A's operation management platform software development project and operation management platform software upgrade project as the research object, and states the project management and Theoretical knowledge of project schedule management. Then use the questionnaire survey method to investigate and analyze the problems existing in the software development projects of Company A and Company A's operation management platform. From the decomposition of project work, the estimation of project working time in the execution of software upgrade projects of Company A's operation management platform, The four aspects of project planning, project monitoring and correction are optimized. Study and use the work breakdown structure, three-point estimation method and key chain method to effectively solve the problems in project progress execution, and use the software chain monitoring model based on the key chain method and earned value method to continuously monitor the project during the software project implementation process Implementation, calculate the earned value of the critical chain and non-critical chain separately, analyze and find out the reasons for the deviation of the project schedule execution, through reasonable improvement measures to ensure that the project is completed on time and quality, effectively verify the improved project schedule management method in this article Effectiveness; Finally, this paper studies and summarizes the working methods of software development project progress management and control based on work decomposition, planning, milestone monitoring, progress monitoring, and calculation correction. At the same time, it provides organizational, technical and personnel safeguards for the project to ensure The smooth implementation and delivery of software development projects.

This article believes that the project schedule management first formulates a reasonable and economical schedule, and then in the implementation of the plan, scientific monitoring methods are used to check whether the actual project schedule is consistent with the planned project schedule to ensure that the project is completed on schedule. Therefore, project schedule planning and project schedule monitoring methods are the top priority of project schedule management.

Through in-depth research on the software upgrade project of company A’s operation management platform, it is confirmed that the critical chain method and the earned value method can reasonably and effectively control the project cost and schedule synchronously, can dynamically monitor the project schedule in real time, and provide timely feedback on the project schedule and cost execution The situation can also provide reference and theoretical support for the progress management of Company A and other similar projects in the same industry.

Keywords: Operations Management Software,Project schedule management,Three-point estimation,Critical chain,Earned value method

# 插图索引

图1. 1 论文结构框架图.  
图2. 1 项目进度管理过程.. . 11  
图2. 2 三点估算法正态分布 .15  
图 2. 3 挣值分析法示意图..... ..19  
图3. 1 天源迪科业务发展图 .22  
图3. 2 公司组织架构图.. .23  
图3. 3 问卷填写人性别、年龄、学历、职称分析. .25  
图3. 4 问卷填写人参与项目数和工作年限分析.. .26  
图3. 5 A公司软件开发项目进度延误情况调查.. .27  
图 4. 1 A 公司软件开发项目进度管理方案总体设计.. ..33  
图4. 2 A公司软件开发项目进度管理方案设计总体框架 .34  
图4. 3 A公司项目进度管理流程图... ..35  
图4. 4 WBS工作分解结构示意图. ..36  
图 4. 5 工作分解过程图. ..37  
图4. 6 运营管理平台软件升级项目未分解网络计划图. .38  
图 4. 7 分解后的项目 WBS ... ..39  
图4. 8 工作计划编制作业图. ..43  
图4. 9 基于关键链的运营管理平台软件升级项目网络计划图 ...45  
图4. 10 基于关键链的网络计划图. .46  
图 4. 11 项目里程碑设置图 ...... ....49  
图4. 12 进度及成本控制流程图 .50  
图4. 13 基于关键链法的挣值分析流程图 .52  
图 4. 14 软件项目成本构成图.. .52  
图 5. 1 项目组织结构... ....60  
图 5. 2 项目进度视图.. ..61  
图 5. 3 项目管理工作环节.. ..62

# 表格索引

表2. 1 关键链技术与传统网络计划技术的特点比对[11]. .17  
表3. 1 问卷回收情况统计表. .25  
表 $3 . 2 \mathfrak { a }$ 系数与信度高低关系对照表[23] ..26  
表 3. 3 调查问卷信度检验结果表[23]. ..27  
表3. 4 A公司计划编制存在问题. ..28  
表3. 5 A公司项目实施过程中存在问题. ..28  
表3. 6 A公司进度管理存在问题汇总. ..29  
表3. 7 数据治理平台工作量估算偏差表. ..30  
表 4. 1 运营管理平台软件升级项目活动逻辑关系列表. ..39  
表4. 2 基于三点估算法的工作量估算表.. ..41  
表4. 3 运营管理平台软件升级项目里程碑计划表. ..48  
表4. 4 运营管理平台软件升级项目计划工作量表. ..53  
表4. 5 各工序计划预算费用表. ..54  
表 5. 1 项目岗位职责表... ..63  
表5. 2 项目执行过程中监控点数据收集分析. ..65  
表5. 3 A公司软件开发项目进度管理效果比对. ..65  
表 6. 1 任务计划工期和实际工期比对表. ..68

# 符号对照表

符号 符号名称/ 除× 乘^2 求平方

# 缩略语对照表

缩略语 英文全称 中文对照IPMA International Project Management Association 国际项目管理组织PMBOK Project Management Body of Knowledge 项目管理知识体系OPM Overall Planning Method 统筹法PSM Project Schedule Management 项目进度管理WBS Work Breakdown Structure 工作分解结构ADM ArrowDiagrammingMethod 双代号绘图法AOA Activity-on-arrow 箭线工作法LOC Line of Code 代码行PERT Program Evaluation and Review Technique 计划评审技术CPM Critical Path Method 关键路径法CCPM Critical Chain Project Management 关键链项目管理BCWS Budgeted Cost for Work Scheduled 项目计划工作预算BCWP Budgeted Cost for Work Performed 项目已完工作预算ACWP Actual Cost for Work Performed 项目实际成本CV Cost Variance 费用偏差SV Schedule Variance 进度偏差CPI Cost Performed Index 费用执行指标SPI Schedule Performed Index 进度执行指标

目录  
摘要..  
ABSTRACT ..... ..III  
插图索引... .... V  
表格索引... . VII  
符号对照表.. ... IX  
缩略语对照表. ... XI  
目录....... . XIII  
第一章 绪论... .11.1 研究的背景及意义.. .11.1.1 研究的背景..1.1.2 研究的意义...1.2 国内外研究现状..... ...................................................................... ...31.2.1 国外研究现状.... ...31.2.2 国内研究现状... ..41.3 研究思路与方法.. .51.3.1 研究思路.. ..51.3.2 研究方法... ...51.4 论文的框架安排.. ..6  
第二章 相关概念及理论基础. ....92.1 项目进度管理相关概念. ....92.1.1 项目的内涵及特点. ...92.1.2 项目管理的三大约束. ..102.1.3 项目进度管理流程 ...102.2 软件开发项目..... .... 112.2.1 软件开发项目的特点. ....122.2.2 软件开发项目的流程. ......122.3 软件开发项目进度管理的工具及方法. ....132.3.1 工作分解.. ..132.3.2 三点估算法.. ...132.3.3 甘特图 .... ...152.3.4 里程碑 ..... ....16

# 2.3.5 关键链. 16

# 2.3.6 挣值法.. 18

# 第三章 A 公司运营管理平台软件开发项目进度管理现状分析.. . 21

# 3.1 A 公司基本情况.. 21

# 第四章 A 公司运营管理平台软件升级项目进度管理优化方案 . 33

# 4.1 A公司运营管理平台软件升级项目进度管理总体方案设计 . 33

# 4.2 基于WBS的运营管理平台软件升级项目工作量优化. 35

# 4.3 基于关键链技术的运营管理平台软件升级项目计划编制优化 . .. 42

# 第五章 保障措施及效果分析.. 59

# 5.1 组织保障... . 59

# 5.2 技术保障.. .. 61

5.2.2 建立难点技术攻坚团队 62  
5.3 人员保障 ... .62  
5.3.1 队伍培训及日常管理 .62  
5.3.2 建立项目团队配备和管理原则. .63  
5.4 效果分析 .. .64  
第六章 结论... .67  
6.1 研究结论 . .67  
6.2 不足与展望 .69  
附录... 7  
参考文献.. 73  
致谢.... 7  
作者简介.. 79

# 第一章 绪论

# 1.1 研究的背景及意义

在项目管理中，进度作为软件项目“三大目标”（进度、成本、质量）之一，是项目管理实施阶段的主要工作内容之一，也是项目管理的重中之重，特别是在软件项目中，项目进度往往是项目管理的首要目标。项目进度管理的失控，必将导致资源的浪费、影响项目的经济效益，甚至直接影响项目最终交付失败。

# 1.1.1 研究的背景

项目是为提供某种独特的产品或服务所付出的临时性工作。项目涵盖了与我们日常生活息息相关的安全、科教、文化、生产、经济、建设乃至国防等所有的重要领域，例如公共安全、医疗卫生、应急调度、通信、金融、交通运输、水利水电等基础类建设项目，科研和教育类的项目，农牧林的发展类项目，环境监测和环境保护类项目、政府和企事业的扶贫项目，企业和事业单位体质改革项目、各行各业的信息系统建设项目等等。

项目管理的实质是运用专业的知识、工具和科学的管理方法以及相关的技能技术对项目的完整执行过程进行管理，其目的是让项目运行能够在有限资源限定的情况下，按期完成或超预期完成项目目标的动态管控过程。项目管理需要有领导项目团队的强烈欲望[1]、具有独特管理技能的人。

随着大数据、AI、5G 等新型信息技术的飞速发展，项目管理在软件行业的应用和需求越来越广泛。软件项目管理的目标是使项目顺利地在预期的项目范围、项目成本、项目进度和项目质量约束条件下完成，从而达到监控、分析和管理项目进度的目的[2]。随着信息技术的快速发展和大数据技术时代的来临，软件项目的建设也日趋复杂，客户对项目建设周期要求更短、按时交付要求严格、客户成本降低，因此，粗放的软件项目管理方法已经不再适用于当下的大型信息化项目建设的管控需求，亟需运用科学、有效的管理方法、技术和工具对信息建设项目进行精细化的项目进度管理，有效控制项目工期和项目投入成本，确保项目顺利地完成和交付[5]。

# 1.1.2 研究的意义

软件项目管理和其它类型的项目管理相比有很大程度的特殊性。软件项目管理的目标是使项目能够顺利地在计划的成本、进度、质量的约束内完成交付，从而对成本、人员、进度、质量和风险进行动态监测和一系列分析、管理的活动。软件是纯知识产品，开发的进度和质量很难准确度量，软件开发人员的工作效率因个人工作经验、知识储备、工作技能的不同而难以预测。软件产品的功能需要满足一群人的用户需求，因此软件项目的需求非一次性，在项目活动的过程中往往因产品使用对象的不同而产生需求的不断调整和需求范围的变化，项目周期难以管控。软件系统的变数多、周期长、复杂度高等因素导致软件项目开发过程中出现各种难以预见和控制的风险。因此，软件开发项目的项目管理是项目管理人员通过对软件项目全生命周期的管理（包含软件需求调研、分析、设计、开发、测试、交付、实施维护等），使项目按照预定的成本、工期和质量完成，并将软件产品交付用户使用。

# （1）论文研究意义

笔者在 A 公司有 9 年的工作经历，期间担任软件产品开发项目经理角色 5 年，参与了 A 公司众多的软件产品开发项目的管理、需求和设计工作，了解 A 公司在项目管理方面的运营模式，对 A 公司在软件开发产品的项目进度管理方面的问题有深刻的思考和总结。

本论文以 A 公司项目管理的现状为研究对象，通过对 118 名软件开发项目经理和开发人员为调查对象设置调查问卷，意图深入调查 A 公司软件项目管理的现状，结合对运营管理平台软件开发项目执行进度管理过程中的问题分析，了解软件项目管理和项目进度管理的研究和应用现状，查阅大量的文献及组织项目成员讨论提出相应的进度管理的改进建议。以 A 公司运营管理软件升级项目为对象，运用管理学及项目管理的理论知识，制定项目进度计划及监控跟踪机制和对偏差的分析及纠偏，在项目活动过程中以及项目初期对项目过程中可能出现的问题及风险进行识别，了解潜在问题和风险点产生的影响因素及环境条件，制定出相应的管控措施及方案，并预留风险应对时间，从而使项目进度管理处于可控状态。

通过本论文的研究过程和结果论证，笔者提出以企业项目管理和项目进度管理的体系方法，为 A 公司开展软件开发类项目提供理论依据和工作准则。因此，本论文的研究成果具有非常强的指引性和实践性，提升了 A 公司在软件开发项目管理方面的能力和水平，增强了企业竞争力和持久发展。

# （2）进度管理的重要性

项目管理包含项目进度控制、项目范围控、项目成本控制，因此项目进度控制是项目管理三大目标控制之一，对项目的成败起着至关重要的作用。项目执行过程中如果出现进度管理的偏差，在需要采取必要措施的情况下未采取措施，将导致项目工作进度无序进行，甚至出现不可控的情况发生，严重的情况有可能导致项目最终失败。如果运用良好的项目进度管理措施对项目执行过程中出现的偏差予以处理，则确保了项目剩余工作的有序和有质的完成，为企业赢得持续的经济效益。

项目进度管理的重要性从项目工期合理管控、项目成本投入管控、企业管理水平提升3个方面进行论述。首先，通过对项目进度科学、合理的管控可以保证项目如期完成。缺少进度管理的项目往往会产生诸如项目执行中的流程混乱、项目进度延后、采用不合理的追赶工期措施、项目交付延期等问题，这些问题的发生都会影响按期交付项目给客户、影响企业信誉、增加项目成本等不良结果；其次，合理管控项目成本投入。一旦项目进度管理产生问题，出现滞后偏差发生，没有采取必要的进度管控措施，必将导致项目成本投入的增加。软件项目具有需求范围的不确定性的特征，企业要想从项目中获取合理的利润，需要在项目的初期制定成本计划，并在整个项目过程中严格控制项目成本。如果项目进度的管理出现问题，就很有可能在项目具体的开发过程中进度比预期进度落后，这样就导致项目的开发工期延长，增加了项目的开发成本，这样对项目和企业都造成巨大的损失；第三，提高企业管理水平。软件项目进度管理是软件项目管理中的重点和难点，每一个工作环节都与项目的整体进度密切相关，实施有效的管理和控制可以提高软件企业的项目管理水平，科学有效的管理方法会为公司获得利益，促进企业的利润获取和可持续发展。

当项目进度出现偏差时，分析导致项目进度出现偏差的原因，并及时采取相应的措施，根据项目进度滞后情况，决定是否调整项目进度计划，以确保项目实施能如期完成。同时，整理相关资料[11]，在项目实施过程中做好相应的工作笔记，记录项目过程中遇到的问题，并从中总结经验教训，作为该项目或者其它项目的历史参考资料，为本公司其它项目管理提供参考意见。

因此，软件项目进度管理作为软件公司的生命线，其地位的重要性值得企业重视。企业必须重视并建立项目进度管理制度和体系。

# 1.2 国内外研究现状

项目指的是在一定时间、成本、资源的限定条件内，进行有明确目标的，一系列独特，复杂的相关联的活动。项目，自有人类活动时就有了，但是真正的现代项目管理起源和推广应用于20世纪初期到20世纪的 60年代。

# 1.2.1 国外研究现状

20世纪80年代以前，项目管理工作主要集中在军方和建筑行业，其管理内容主要是向高层主管人员提供进度信息和资源数据。现在的项目管理所包含的内容比以前要多一些，每一个国家从事每一种行业的人们都在进行着项目管理。计算机硬件、软件、网络以及遍布全球的工作团队已经彻底改变了人们的工作环境。

大多数人认为，现代意义上的项目管理是从“曼哈顿计划”[8]开始的。

项目管理是在20世纪30年代为应对超大型项目的实际需求而产生的。在二次世界大战的后期，一些大型的、复杂度极高并且协调难度大的项目需要先进项目管理知识的支撑，在一些极具复杂度的项目中发挥了很好的管理和协调作用，这个阶段项目管理理论得以迅速发展，具有这一阶段项目管理的项目是美国原子弹制造的曼哈顿计划，项目管理理论发挥了重要作用[3]。

国际项目管理协会（IPMA）于 1965 年成立于瑞士洛桑，该组织代表了现代项目管理资格认证的最高国际水平，在国际上具有广泛的认可度和专业权威性[3]。1969年美国成立美国项目管理学会，该机构于 1987 年提出了项目管理知识体系的项目管理的标准，将项目管理划分为 9 个知识领域和 5 个项目管理流程。9 个知识领域包括项目范围管理、项目时间管理、项目成本管理、项目质量管理、人力资源管理、沟通管理、采购管理、风险管理及整体管理[8]。5个项目管理流程包含项目初始化、计划、执行、控制、收尾。国际标准组织以该管理体系为框架支撑，制定了 IOS10006 项目管理的质量标准。

到了20世纪早期，美国的H.Gantt[4]重视工业项目中对人的管理，发明了甘特图。甘特图具有易于理解、操作简单、灵活直观的优势，是最早被应用于项目管理和进度控制方面的工具[3]，直到今天的各类项目管理中仍被广泛使用。

1956 年美国杜邦公司的 Morgan Walker 和雷明顿兰德公司的数学家 James E.Kell[5]共同研究在减少工期的情况下如何采取正确的措施，尽可能的减少增加的费用，提出了关键路径法；1957年Kelly借用线性规划的方法解决每个活动的工期和活动间的逻辑关系。1959 年，Kelly 和 Walker 提出了关键路径的基本原理并且提出了资源分配与平衡、费用计划的方法。1958 年美国海军特种计划局在研制北极星导弹项目中提出计划评审技术[6]（PERT），该技术原理与关键路径法一致，都是采用网络图来表示项目中各作业活动的逻辑关系和进度计划，通过估算各工作的持续时间，确定关键路线及其关键工作，在项目实施过程中根据实际情况，不断优化调整工作计划。到了 1997年，以色列物理学家 Goldratt[7]在《关键链》一书中提出了将约束理论应用于项目进度管理的关键链进度管理方法，用该方法取代了以往管理使用的关键路径法，增加的缓冲区帮助解决项目执行中的资源约束的协调和进度控制的管理，这一管理方法被广泛应用。

# 1.2.2 国内研究现状

中国的项目管理体系建立的里程碑是 20 世纪 60 年代初期，著名科学家和数学家华罗庚教授引进和推广了网络计划技术，即“统筹法”，统筹法的逐渐应用标志着我国的项目管理有了一套科学的系统方法，适合我国当时项目管理的实际需求。这一方法的运用，在后续由华罗庚教授带领的铁路、桥梁、隧道等大型项目中被不断实践和提升，到了 20 世纪 80 年代以两淮煤矿开发项目为实践依据，我国的项目管理水平已经有了明显的提升。

随着现代管理方法在我国的推广应用，20世纪80年代，从鲁布革工程经验开始，我国开始了学习借鉴国际项目管理先进做法的探索研究阶段。到了 20 世纪 90 年代，

项目管理的应用领域进一步扩大。

李晔，谭晓慧等在进度-费用联合控制模型在污水处理厂工程中的应用提出，从系统理论观点出发，利用挣值法和层次分析法构建进度-费用联合控制模型，通过层次分析软件得到影响进度、费用的各影响因素权值，针对不同的影响因素采取不同的措施进行纠偏控制，以达到良好的项目管理结果[12]。

李旭东在北京银行信息系统项目进度控制的研究中指出，根据项目估算、资源等因素对项目进度的纠偏措施，并提出了“量化偏差标准，设定里程碑”的概念，利用数理理论，量化偏差标准的相关指标，最终结合实际项目，验证了“量化偏差标准、设定里程碑”的正确性与可行性，为项目进度管理提供了有效的控制方法[13]。

# 1.3 研究思路与方法

项目进度的管理对项目的成败起关键作用，本论文采用关键链技术和挣值法结合对项目进度进行管理，试图解决传统项目管理方法的不足。

# 1.3.1 研究思路

在阅读大量文献的基础上，通过调查问卷的形式，得知 A 公司在项目管过程中遇到的问题，本论文采用基于约束理论的关键链法编制项目计划，结合笔者的调查和项目计划进行输入缓冲和项目缓冲设计；在传统挣值法理论上进行研究，提出基于关键链法和挣值法相结合的软件项目监控模型，分别计算关键链路和非关键链路以及整个项目的挣值，分析项目进度，在项目开展过程中进行有效的控制。

# 1.3.2 研究方法

本论文通过理论与实践项目研究、定性与定量分析相结合对研究对象进行研究和论证，论文研究所用到的方法有以下5种：

# （1）文献法

本论文通过阅读、分析和整理项目进度管理理论相关研究的书籍、期刊、网络、文献，获取有利于本论文项目进度管理研究的思路、观点和方法。

# （2）问卷调查法

本论文以A公司的软件开发项目经理、软件开发人员为调查对象，设计《基于 A公司软件开发项目进度管理及实施跟踪调查问卷》和调查问题，确定问卷量表，通过线下和线上同步发送问卷的方式，以获得一定的问卷数据。

# （3）访谈法

笔者通过对 A 公司项目总监、项目主管、及众多开发技术人员进行一对一访谈，深入了解他们对A公司在项目进度管理方面的技术水平和存在的问题。

# （4）数据统计分析法

笔者运用数据描述、Project、Excle 等方法和软件，对收集的调查问卷和 A 公司在运用管理平台软件开发项目中所收集的资料和数据进行分析，得出的数据结论供A公司进度计划管理的提升和计划制定做参考。

# （5）多学科组合研究

本论文在分析项目进度计划管理过程时运用了管理学、行为学和心理学等学科知识，综合归纳分析 A 公司在软件开发项目进度管理中存在的问题，运用数学的方法构建基于关键链和挣值法结合应用的项目进度计划管理模型及进度纠偏管控方案。

# 1.4 论文的框架安排

本论文运用现代项目管理理论和方法，以 A 公司项目进度管理体系和运营管理平台软件升级项目为研究对象，研究在企业在软件开发项目进度管理中存在的问题，运用所学习的管理学知识体系提出一套科学、有效的管理体系。本论文的主要研究内容分为六个部分：

第一章 绪论。简述了本论文研究的背景意义及A公司在项目进度管理方面所面临的问题，了解目前国内外项目进度管理的研究现状及阐述本论文研究的思路和方法。

第二章 相关概念及理论基础。本章节介绍了论文研究所涉及的相关理论概念，从项目工期合理管控、项目成本管控、企业提升等方面论述进度管理对企业整体管理的影响。

第三章 A 公司运营管理平台软件升级项目进度管理现状分析。首先介绍本论文研究主体，其次通过问卷调查的方式深入了解 A 公司项目进度管理现状及存在的问题，结合 A 公司运营管理软件开发项目进行分析，找出问题的根本原因。

第四章 A公司运营管理平台软件升级项目进度管理优化方案。运用前文对A公司进度管理中存在的问题分析，优化提升 A 公司运营管理平台软件升级项目编制工作计划，并制定项目进度的跟踪控制措施。

第五章 保障措施及效果分析。本章节包含组织保障、技术保障、人员保障和效果分析四个部分。

第六章 结论。对本论文的研究成果进行总结，并指出下一步的研究方向。

论文研究框架图详见图 1.1。

![](images/8cb65df5c5dd5b46bd1478b475df8be49513816dcb32cc7632a631d45b145e53.jpg)  
图1. 1 论文结构框架图

# 第二章 相关概念及理论基础

本章节主要介绍项目进度管理的相关概念及理论基础知识，首先从项目的内涵及特点、项目管理的三大约束以及项目进度管理流程三方面介绍项目进度管理的相关概念；其次从软件开发项目的特点及流程介绍软件开发项目；最后介绍软件项目进度管理的工具及方法，包括工作分解、三点估算法、甘特图、里程碑、关键链、挣值法等相关概念。

# 2.1 项目进度管理相关概念

项目进度管理是指运用科学的方法在规定的时间内确定项目进度目标，编制项目进度计划，依此在项目实际进行的过程中检查执行进度和计划进度的偏差，如果项目进度执行过程中出现偏差，应及时分析偏差产生的原因并决定是否采取相应的进度管控措施，持续监控剩余项目工作执行情况。项目进度管理是一个动态的、持续的管控过程，项目管理的根本目标是在满足项目时间和资源约束的两种条件下尽可能的以最优工期完成项目。

# 2.1.1 项目的内涵及特点

项目具有独特性、一次性的特征，是为执行某项工作而进行的临时性工作，项目具有开始日期和结束日期、资源约束、整体目标，项目的执行过程有目的性、时间性、一次性、独特性、相互依赖性、不确定性。

通过以上定义描述可总结出项目的5个基本特征。第一时间性，项目具有明确的开始时间、结束时间和项目周期。第二目的性，项目具有明确的目标，如项目需求范围、实现的内容、产品性能、客户满意度等。第三制约性，项目具有明确的制约性，常见的制约限制有时间、资金、人力、生产资料的等。第四独特性，一般项目均根据客户需求进行定制，项目的实施条件和成果与其它项目均不相同[9]。第五不可逆性，一旦项目进行实施，项目成果不可逆转。

项目管理因人而异议，对不同的人意味着不同的事情。项目管理从曼哈顿计划发展而来，到现在已发展成为专业学科，被广泛的应用到工业、基础建设、信息化系统等领域的项目管理中，并取得了一定的成效。

项目管理对不同的人意味着不同的事情。现代“项目管理”通常意义上被认定为第二次世界大战的产物，从上世纪八十年代开始，项目管理的应用开始扩展到工业领域，并逐步扩展到计算机、电子通讯、金融等现代工业领域。目前，项目管理已经成为一门专业学科，在世界以及中国范围内均得到了广泛认可[2][9]。

项目管理的内容涵盖计划和监控。项目计划是分解项目工作、定义项目工作间的前后依赖关系、定义工作量和工作难度、定义需要的资源。项目监控：在项目的执行过程中，监控项目进度状态，不断的比对项目进度是否与项目计划一致。

项目管理需要实现的项目目标有 5 点：第一，在规定的时间内完成项目；第二，在规定的成本内完成项目；第三，达到期望的性能/技术水平；第四，有效且高效地使用已分配的资源；第五，为客户所接受。

# 2.1.2 项目管理的三大约束

项目管理的实践中会遇到项目范围、时间和成本三个因素的制约，这三个制约构成了项目管理的铁三角，其相互影响、彼此关联。项目管理的根本目标是在项目执行的过程中项目管理人员运用科学、有效的管理方法，对项目执行动态管控的过程，不断协调这三个因素使其达到平衡[32]。项目三大约束的阐述如下。

（1）项目范围约束，明确项目内容和目标

项目范围，尤其是软件产品的项目范围不是一次确定的，随着项目的执行会产生需求的调整或大的需求变更，由此导致范围与时间和成本的管控不均衡。因此，为完成项目计划和企业利润，需要项目管理人员对项目范围的变化做好科学的管控。

（2）项目时间约束，规定项目的完成时间

在预先限定的时间范围内合理安排项目各项活动的顺序，制定项目进度计划。项目执行过程中如果出现执行进度与计划进度发生差异时，需要重新调整项目计划或调整项目工期，确保项目计划和质量。

（3）项目成本约束，设定项目执行的投入成本

项目执行过程中，项目范围的变化或者计划的调整，必然产生成本的变化。项目管理人员需要判断通过成本的追加或执行项目计划的变更对项目进行管控[14]。

# 2.1.3 项目进度管理流程

以时间约束为考量项目进度管理，要求项目按期完成，在项目的执行过程中，对项目的进展和完成时间进行管控，按照项目开发事前、事中、事后工作活动安排，项目进度管理流程表述如下：

（1）项目开发之前

理解项目的整体需求范围、需求业务流程、项目工期，对项目整体工作进行分解，梳理工作间的逻辑关系，并识别项目开发过程中可能出现的问题和风险，综合各项约束制定科学合理、经济可行的项目进度计划。

# （2）项目开发过程中

及时监控项目开发过程中的进度执行情况，并与计划进行比较，这是一个不断重复的管控过程，目的是使项目在满足时间约束的条件下实现项目总目标的完成，运用科学有效的管控方法进行指挥、协调、控制的一系列活动[32]。

软件项目的实施环境是动态的，因此软件项目进度管理也是一个动态的管控过程[7]。其动态管控过程包括为确保项目按期完成需要具备的工作分解、时间估算、制定计划、计划监控纠偏[11]的所有工作环节。

项目进度管控过程如图 2.1 所示：

![](images/0fb114d8c906bd8aebb92aeb71ed1b6e55abb0f3599a7b3e6e8144c9620f04ec.jpg)  
图2. 1 项目进度管理过程

工作分解是指将项目的整体工作分解为更小、更易于实施的、包含项目交付所需的所有工作；工作顺序安排即工作间的依赖关系，在软件项目开发中，工作间的依赖关系取决于工作所涉及的软件模块间存在的依赖关系；工作时间估计估计完成各项工作所需的时间；工作计划编制：以网络的形式明确各项工作的开始时间、结束时间及其持续时间，并且在项目实施过程中，工作计划需不断的优化调整；工作进度监控及纠偏：在项目执行过程中通过对项目执行进度的监控判断进度偏差，了解产生偏差的原因及影响因素制定相应的管控措施予以纠偏。纠偏的目的是确保项目进度计划和项目整体工作目标的实现。

# 2.2 软件开发项目

软件开发项目是将用户的业务需求、管理流程、生产数据等内容通过编码的方式以计算机为载体，呈现给用户使用，帮助用户以更快的方式解决生活和工作中面临的问题。软件开发是一种解决某个问题或实现某个需求的活动。

软件开发项目是指软件开发人员为了满足人们的工作生活需求，通过理解分析，依据一定的开发原理采用软件开发工具，最终产生满足用户需求的软件。而这个需求就是我们所说的软件开发项目。

# 2.2.1 软件开发项目的特点

软件产品是无形的知识产品，不具备看得见、摸得着的物理属性，软件产品难以理解。软件产品的这些特点决定了软件项目开发的不确定性、独特性、阶段性和创新性的特点。

首先，软件项目是抽象的、难理解的纯知识产品，在项目初期用户对项目需求难以明确，随着项目的开发进行用户对产品的理解也在逐渐的细化，因此导致需求范围的变化和频繁需求变更，这些因素决定了软件开发项目的不确定性；其次，软件项目是纯知识产品，不同的客户对产品有不同的需求，因此软件开发项目具有独特性，项目的独特性对项目管理和实际的开发提升难度，有较高的要求；第三，软件开发项目结合客户要求和企业管理往往是迭代开发，非一次性完整交付，因此将软件开发项目的整个周期按照不同的阶段目标设立里程碑进行管理，因此软件开发项目也具有阶段性；最后，软件开发项目是劳动密集型和智力密集型相结合的产品，其开发过程需要非常细致和复杂的劳动要求，同时，软件开发项目需要与新兴技术相融合，因此软件开发项目具有一定的创新性。

# 2.2.2 软件开发项目的流程

软件开发项目的流程一般分为：需求分析、概要设计、详细设计、程序编码、程序测试、业务确认测试、软件交付、客户验收、实施维护[52]，流程内容介绍如下。

# （1）需求分析

需求是软件开发项目的源头，每个项目都从一个需求开始，需求是用户想实现什么样的功能，到达什么目的。在此基础上，需求分析就是根据用户的描述，进行更深入的挖掘，将大概的功能描述出来，更进一步的分析，弄清楚用户的目的，实现用户的需求，需求分析需要完全分析软件项目的整体需求。

# （2）概要设计

概要设计阶段综合考虑用户对系统运行的软硬件环境、产品开发语言、与外部系统的关联关系等要求，以及需求阶段对用户需求分析，从而确定软件产品的开发工具和产品概要设计。概要设计包含整体架构设计、网络部署设计、子系统的划分和业务模块的划分。

# （3）详细设计

在概要设计完成的基础上，软件开发人员对软件系统进行详细设计。详细设计包含描述业务需求所涉及的所有业务模块的算法、数据模型、数据结构、数据接口和各子系统、模块间的逻辑调用关系的设计。详细设计应足够详细，指导后续的程序编码。

# （4）程序编码

按照已经完成的需求分析、概要设计和详细设计进行程序编码，程序编码需要统

一开发标准和规范。

（5）程序测试及业务确认测试

按模块或活动完成程序编码后提交给测试人员和业务人员进行功能测试和业务确认测试，其目的是在功能发布之前发现编码过程中存在的问题，确保系统上线后正常运行。

# （6）软件交付

软件交付即完成程序编码和程序测试后将产品部署实施。部署完成后软件产品即可交付给客户使用。

# （7）运行维护

部署实施不是软件开发的终点，需要对部署的软件系统有一段时间的运行维护，确保系统的可用性。

# 2.3 软件开发项目进度管理的工具及方法

# 2.3.1 工作分解

工作分解结构（WBS）最先由美国国防部提出，自上个世纪的70年代以来，WBS作为一种基础方法被广泛的应用于国内外各个行业的项目管理工作的开展。以可交付成果为导向对所有项目范围进行逻辑分组与层级分解，工作分解必须覆盖全部项目范围。工作分解每向下分解一层，意味着对需求更细的理解和定义，由此全面掌握项目的细节，为做出精准的项目计划做准备。

WBS 最低层次的项目可交付成果称为工作包，定义工作包时应考虑 80 小时法则或者两周法则，也就是一个工作包的完成时间不能超过 10 个工作日，这样每两周对工作包完成情况进行检查，以便控制项目的变化。WBS 中某项任务的内容是其下所有 WBS 项的总和，同时一个工作包虽然可以由多人参与，但是责任人必须只有一个，这样不会造成相互推卸责任，责任划分不清楚的状况。

结合笔者多年的工作经验和阅读相关文献后分析发现，在软件项目开发中，WBS的底层工作包是经过分解后的分配需求，完成这最小颗粒的需求所涉及的设计、学习、编码、编译环境的搭建、代码走查、单元测试、系统测试、系统匹配联调等工作均属于工作的范畴。

本文4.2.1运营管理平台软件升级项目的WBS 中，应用WBS解决了该项目的工作分解的问题。

# 2.3.2 三点估算法

三点估算法是是基于计划评审技术的延伸应用，是一种对项目工作时间估算的方法，在计划评审技术的基础上增加了对项目实施事前管理，该方法由美国海军特种计划局于1958年在计划和控制北极星导弹的研制项目中提出[11]。

传统项目工序工作时间的估算是由项目经理估计大概时间范围，这样会造成两种结果：对项目中的新技术风险考虑不到或者为某一简单工作预留过多的缓冲时间，这样的时间评估势必造成工作计划毫无意义。PERT 方法在弥补了项目进度时间估计不准确的缺陷，降低了项目执行过程中的风险，为项目计划的实施提供了坚实的理论依据。

在 PERT 中，近似的估算出工作完成的乐观、悲观和最可能持续时间的时间值，据此计算出加权平均的期望值作为该项工作的持续工作时间[11]，这样的计算方式将乐观、悲观和最可能三个不确定的时间值转化为一项确定的时间值，计算公式如下：

$$
t _ { - } i = ( a _ { - } i + 4 c _ { - } i + b _ { - } i ) / 6
$$

公式中 ai 为 i 工作乐观估计时间，bi为 i 工作悲观估计时间，ci 为 i 工作正常持续时间，可由施工定额估算，ti 为 i 工作的平均持续时间；通常情况下 ai，bi 两个工作时间值由统计法获得。对时间的偏差分析采用方差估算：

$$
\left[ ( \alpha _ { - } \mathrm { i } ) \right] \hat { \mathbf { \Omega } } ^ { } 2 = ( ( b i - a _ { - } i ) / 6 ) ^ { \wedge } 2
$$

标准差计算公式为：

$$
\alpha _ { - } \mathrm { i } = \sqrt ( ( ( b _ { - } i - a _ { - } i ) / 6 ) ^ { \wedge } 2 ) { = } ( b _ { - } i - a _ { - } i ) / 6
$$

按规定工期时间完成的概率，可通过下面的公式和查函数表求得。

$$
\begin{array} { r } { \lambda = \frac { Q - M } { \sigma } } \end{array}
$$

式中：Q 为网络计划规定的完工日期或目标时间；M 为关键线路上各项工作平均持续时间的总和； $\sigma$ 为关键线路的标准差； $\lambda$ 为概率系数[53]。

PERT 方法假定各项工作的持续时间服从 $\beta$ 分布，正态分布图如图 2.2 所示：

![](images/fc878fd830d87e5e401fa7e11537eda076fbd18ecca6cb7d1f42b1f230ff48ae.jpg)  
图2. 2 三点估算法正态分布

正态分布图中期望值两边1个标准差的范围内，曲线下面积约占总面积的 $6 8 . 2 6 \%$ ；2个标准差范围内，曲线下面积约占总面积的 $9 5 . 4 4 \%$ ；3个标准差的范围内，曲线下面积约占总面积的 $9 9 . 7 2 \%$ 。

因此我们可以知道：项目在期望工期完成的概率是 $50 \%$ ；(可能值 $+ 1$ 个标准差)时间内完成的概率是 $8 4 . 1 3 \%$ ；在(可能值 $+ 2$ 个标准差)时间内完成的概率是 $9 7 . 7 2 \%$ ；在(可能值 $^ { + 3 }$ 个标准差)时间内完成的概率是 $9 9 . 8 6 \% ^ { [ 1 6 ] }$ 。

有专家学者指出：PERT 的三点估算法所估算的时间会存在较大的误差，项目实施阶段具有一次性、复杂性和创新性的特点，如果执行团队不具备足够的经验和知识储备，不能在事前对可能产生的因素尽可能全的考虑到，无法实现应对问题的资源和时间的准确估算[17]。

本文 4.2.2 工作量估算优化中，应用三点估算法解决了运营管理平台软件升级项目的工作量估算的问题。

# 2.3.3 甘特图

甘特图法（Gantt chart），也称为条状图（Bar chart）或横道图，是 1817 年由亨利·劳伦斯·甘特提出。是以横线表示每项活动的起止时间，用横轴时间纵轴项目的条线图反馈出项目活动的顺序和持续时间，线条表示在项目执行期间计划进度和实际活动完成进度[18]，便于管理者了解剩余项目任务和时间，合理安排项目剩余工作。

采用甘特图的项目进度管理简单、直观、便于编制，因此，到现在为止甘特图仍然是企业项目管理中使用较多的管理工具。甘特图简单明了的展现方式，在很多的大型项目被用于企业管理层对项目全局、基础工作安排和项目整体工作进度的视窗。

甘特图的局限性体现在事实甘特图主要关注进程管理（时间），因此在图上仅仅部分地反映项目管理的三重约束（时间、成本和范围）；甘特图的不足是在一些大型项目中通过甘特图绘制的项目活动关系因活动太多，降低了甘特图的可读性。

本文 4.4.1 设置运营管理平台软件升级项目的关键控制点中，应用甘特图向管理层直观展现项目整体工作进度。

# 2.3.4 里程碑

里程碑，原始含义为高速公路上的里程碑，就是我们平时看到的公路边对路线距离记录标记的一块石碑，石碑上标注着行驶到此的公路里程。引申到项目的里程碑，就是指项目中的一些关键事件，使用关键事件的完成来丈量整个项目的进度。里程碑在项目过程往往不占用资源，是一个时间点，通常指一个可交付成果的完成。

里程碑可以成为项目一部分工作的输出结果或者项目中间产物[13]。在项目的实际执行过程中，项目管理人员增加里程碑管理制定项目各个阶段的目标，设定里程碑计划，以确保项目目标完成。项目里程碑的制定采用倒推的方法，首先确定项目最后一个里程碑。其余里程碑按照关键工作节点、客户关注点、团队头脑风暴等方法确定项目里程碑，每一个里程碑都有完成的交付物和标准。

里程碑的设定和管理遵从 4 个原则。首先，以里程碑为项目进度管理的关键点，把项目的执行阶段进行切分，及时监控里程碑的完成情况，必要的时候予以调整，对整个项目进度实行动态调整。其次，每一个具体的里程碑与具体角色责权关联。里程碑一旦确定，各模块负责人应确保按时完成该里程碑内容和确保里程碑完成的质量，达到了对软件项目质量的整体控制。第三，制定每个里程碑验收标准，给出一个清晰的里程碑验收准则，依此判断里程碑工作的执行结果。最后，所有里程碑的完成代表了整个项目工作的完成，因此每一个里程碑的制定都需要与整个项目的进度计划相结合，通过对里程碑完成的检验可以掌握项目整体工作的完成情况。

通过项目里程碑的设立，项目经理和项目团队通过对项目实施进度的监控和对比里程碑计划，了解项目是否按照目标执行，有利于项目进度的管控。通过对里程碑成果的检查，可以分阶段提前发现项目需求、设计和进度控制中存在的问题，判断项目里程碑的状态，为剩余项目工作做准备，避免产生不可管控的严重后果。

本文 4.4.1 设置运营管理平台软件升级项目的关键控制点中，通过设置项目里程碑监控点，解决了项目进度监控不及时、不准确，进度状态管控不合理的问题。

# 2.3.5 关键链

现代工程项目中经常使用的是关键路径法，关键路径法已经成为项目管理人员所熟知的进度计划编制方法。但是根据关键路径法制定项目进度计划时，每项工作的时间都包含安全时间，也就是说，每项工作的时间太宽松，使员工没有时间紧迫感，这样员工势必会因学生综合症和帕金森综合症，在项目开发过程中项目安全时间被白白浪费掉，因此也造成资源的巨大浪费。由于关键路径法的该问题存在会导致项目进度滞后或者项目直接破产失败，基于约束理论的关键链法应运而生[28]。

1997 年，以色列物理学家艾利·高德拉特博士 Goldratt[7][29][30]博士提出基于约束理论下的项目进度管理方法：关键链法。关键链法与关键路径法最大的不同就是在关键链法缩短了每个工作的计划时间，这样使员工更具有时间紧迫感，同时为了降低项目的执行风险，在项目计划编制中增加了缓冲时间，以确保项目能如期完工。

在项目基于局部最优的总和并不能导致全局最优的约束理论，关键链技术从全局出发考虑问题，在项目进度计划制定阶段既考虑活动的工作时间、活动间的约束关系和逻辑依赖关系以外，同时还必须考虑现实情况下的资源约束。通过增加项目的缓冲区和输入缓冲区来降低项目进度管理中的风险，确保项目的进度在计划内有效完成，同时也可以延缓现金流出以提高项目的净现值[28]。

关键链技术较关键路径技术有其明显的优势，两个技术在安全时间、开工时间、资源争夺、项目不确定性问题的应对方面的特点和优势分析见表2.1。

表 2. 1 关键链技术与传统网络计划技术的特点比对[11]  

<table><tr><td></td><td>关键链技术</td><td>关键路径技术</td><td>关键链技术的优点</td></tr><tr><td>安全时间</td><td>为整个项目设置缓 冲区和输入缓冲区</td><td>为每一道工序设置安全 时间</td><td>运用项目缓冲对项目的安全时 间统一管理，减少工期的浪费</td></tr><tr><td>开始时间</td><td>合适的时候启动该 工序的开发</td><td>工序越早开工越好</td><td>优先确保关键链上工作的完成， 不以工序的先后顺序进行安排</td></tr><tr><td>资源争夺</td><td>最大化的实现了资 源冲突的平衡</td><td>不考虑资源争夺</td><td>资源出现冲突时，优先分配资源 到关键工序上，并对其进行重点 监控</td></tr><tr><td>项目风险的 管控</td><td>动态的监控缓冲区 消耗情况</td><td>进度出现偏差后，重新 调整项目计划</td><td>项目的进度计划有可行性，不需 要在项目实施过程中不断改变 项目计划，降低项目的不确定性</td></tr></table>

关键链法主要内容如下：

（1）关键链的确定

关键链技术将资源约束与工作间的逻辑关系作为同等重要的制约项目总工期的因素。关键链技术综合考虑了各工作间逻辑关系和资源约束，取最长的线路作为关键线路，关键线路各工作的持续时间、各工作间的逻辑关系和其之间的资源分配存在着相互作用的关系，上述因素共同确定项目的总工期。项目关键链的确定是一个不断循环优化的过程。

# （2）缓冲区的确定

借助风险聚合思想，减少设定在各工作上的风险应对时间作为缓冲区汇聚到关键链后或非关键链关键链连接处，缩减项目总工期。缓冲区的设置在项目执行过程中起到预警的作用，遇到不确定风险时缓冲区将诶占用，依此引起项目管理人员对项目进度计划的监控和重视。因此，确认关键链缓冲区大小就是一项十分重要的工作，缓冲区的大小是否科学合理，将直接反映了项目计划编制的合理性，同时会影响项目实施的可行性。为解决学生综合症和帕金森现象、多任务效应资源冲突及其项目工期滞后问题，Goldratt博士提出通过设置缓冲区来解决以上问题[29]。

项目缓冲区：以往项目计划管理的项目工期中的安全时间设置，与项目能否如期完成存在很大关系。由于学生综合症和帕金森综合症的存在，我们可以看出，当项目工期比较宽松时，人没有时间压迫感，不会高效率开展工作，往往当项目工期被使用殆尽时，才开始加倍努力工作，这样每项工作的安全时间并没有起到应有的作用，所以根据关键链理论，将每个工作的安全时间去除，结余的安全时间集中在一起，放在项目关键链路的最后，这样确保项目整体目标的完成。

输入缓冲区：在非关键线路与关键线路汇合处设置输入缓冲区 $\mathrm { F B } ^ { [ 1 1 ] }$ ，输入缓冲区主要是为了不使非关键路线延迟，以保证项目能够按期完成。

常用的设置项目缓冲区的方法为Goldratt提出的 $50 \%$ 法，也称为剪切粘贴法。首先依据传统计算工期的方法计算项目每个工作的工期，然后将每个项目工期缩短一半，节约下来的工期集中在一起，累加乘以 $50 \%$ 即可得到项目缓冲时间；同理，输入缓冲为项目中非关键链路的工期的 $50 \%$ ，累加乘以 $50 \%$ 。

（3）基于关键链技术的项目进度管理缓冲区的应用

项目执行中要及时对项目缓冲和输入缓冲的消耗进行监控，实际项目进度中项目缓冲消耗比例的定义和管控如下：第一，当项目滞后时间在缓冲区的 1/3 以下时，表示项目进度正常，可以不采取措施。第二，当项目滞后时间在缓冲区的 1/3-2/3 之间时，此时表示项目管理过程中出现问题，需要项目管理调查导致项目滞后的原因，针对原因采取相应的措施，比如加班或者增加资源，但是此时不需要修改原来的项目计划。第三，当项目滞后的时间在缓冲区的2/3 以上时，此时说明此项目进度管理已出现严重问题，此时必须要重点关注，调查出导致此问题原因，及时采取相应措施，如果置之不理，势必造成项目的进度的延迟，甚至是项目被迫终结。

关键链方法考虑资源约束因素，按照完成概率 $50 \%$ 的可能性估算工期，从而缩小了项目工作的时间，同时也激发了项目团队的工作激情，使员工工作有紧迫感及适当的压力，另外通过设置输入缓冲和项目缓冲时间，从措施上确保了项目开发过程中由意外因素或者不确定因素导致的项目延期[19]。因此，关键链法尤其适合新研项目。

本文4.3基于关键链技术的运营管理平台软件升级项目计划编制优化中，应用关键链技术解决了项目计划编制不科学的问题。

# 2.3.6 挣值法

挣值法是通过项目预算的方式反映项目进度，是用来分析项目实施与项目目标期望之间差异的一种方法，依据差异来对判断项目进度，同时对项目实施过程进行控制及其计划调整，以确保项目如期完成。挣值法又叫挣得值法，核心目的是通过三个参数和四个评价指标比较项目执行的范围、时间、质量和进度与计划的差异，依据这些差异对剩余项目工作进行预测、调整和控制。

1967 年美国国防部首次开发并成功应用到国防项目中，后来此方法被广泛应用到各个项目领域进行项目进度管理。挣值法的最大优点是能随时监控项目进行的状态，确保项目处于受控状态。示意图如图2.3所示：

![](images/37e80d393c92f51525a68a2bc053553a895b244a931e9a56e2ce0aac8a27f8b7.jpg)  
图2. 3 挣值分析法示意图

挣值法由三个基本参数和四个评价指标两个大的部分组成。三个基本参数为：项目计划工作预算费用(BCWS)、项目已完工作预算费用(BCWP）、项目实际成本(ACWP)[54]；四个评价指标为：费用偏差（CV）、进度偏差（SV）、费用执行指标（CPI）和进度执行指标SPI）。

本论文仅涉及进度偏差指标，即反映项目进度的三种状态：提前、等于预期还是滞后。进度偏差的定义为项目已完工作预算费用-项目计划工作预算费用，即SV $\fallingdotseq$ BCWP-BCWS，若 $\mathrm { S V } { = } 0$ ，则表示项目进度与计划进度同步；若 $\mathrm { S V } { < } 0$ ，则表示项目实际进度慢与项目计划进度，即项目滞后，此时联系关键链法的缓冲区监控方法，判断实际占用缓冲区的百分比，决定是否采取相应的措施，以确保项目如期完工；若$\mathrm { S V } { > } 0$ ，则表示项目进度提前，此时不需采取措施。

挣值分析法的实质是BCWP和BCWS比较和评估，通过定量的比较得出项目执行结果是提前还是延后，预测项目资源追加情况和按期完成情况。

通过对项目执行的挣值曲线分析可以随时掌握项目的进展，尽可能早的发现影响项目进度的问题，降低项目交付的风险，调查造成项目滞后的主要原因，采取相应的措施，对资源进行合理的分配，确保在节约成本的前提下，采取相应的纠偏措施对资源进行分配，确保项目如期完工。

本文 4.4.2 基于关键链法和挣值法的关键点进度纠偏中，应用关键链法和挣值法解决了项目进度计划监测和控制的问题。

综上，本章节提出的6个软件开发项目进度管理工具及方法在论文第四章节分别进行了使用原理及使用具体操作的应用。其中 4.2.1 章节采用了 WBS 进行工作结构分解，4.2.2 章节采用了三点估算法进行工作量评估，4.3.1 和 4.3.2 采用了关键链法编制项目进度计划，4.4.1 采用了里程碑和甘特图设置项目关键监控点，4.4.2 采用关键链法和挣值法相结合管控项目执行过程。

# 第三章 A 公司运营管理平台软件开发项目进度管理现状分析

A公司是国内计算机软件开发和大数据挖掘的领先企业，长期致力于新品研发及技术创新，不断研发推出满足市场需求的新产品。本章主要介绍本论文的研究主体A公司的基本情况，通过调研问卷的方式对 A 公司在软件开发项目中进度管理的现状及存在的问题进行调查和分析，通过运营管理平台软件开发项目进度计划及进度管理中存在的问题分析反映 A 公司在软件开发项目的进度计划及进度管控中存在问题的论证。

# 3.1 A 公司基本情况

深圳天源迪科信息技术股份有限公司于 1993 年在深圳成立。公司自创建以来在运营商业务领域不断深耕，随着技术的创新也业务的累积，公司的业务领域逐渐扩展到公共安全、政务服务、金融领域的大数据平台建设、应用软件的研发和产品解决方案的提供。现阶段天源迪科已发展成为国内电信运营商行业以客户关系管理系统支撑软件的主要厂商和公共安全领域智能研判指挥系统领域的领先企业，在新兴技术领域也成为行业的领先企业，如大数据技术、人工智能技术等。

# 3.1.1 历史沿革

深圳天源迪科信息技术股份有限公司（本论文中简称：A 公司）成立于 1993 年1月18日，于2010年 1月20日在创业板上市（股票代码：300047），是首批国家级高新技术企业和重点软件企业，公司拥有软件开发项目包含信息系统集成一级资质、质量管理体系 ISO9001、信息安全管理体系 ISO27001 和行业顶级 CMMI5 认证，并且拥有近千项的科技成果和软件著作权。天源迪科总部位于深圳，目前拥有8家全资子公司、5 家控股子公司、3 家参股公司，在全国 26 个省会和主要城市设立分支机构，建立集团公司和设立在各省份机构的两级客户服务体系。

天源迪科以为国内外各行各业的客户提供行业解决方案和软件产品开发维护为主营业务，企业在新兴技术领域的核心能力有基于云计算的运营支撑系统和以大数据、人工智能推动传统产业和信息技术的“生态融合”。公司以电信运营商软件产品的研发为企业的根基，不断拓展其业务领域，逐步形成了覆盖公安、交通、运输、政务、能源等多个行业的业务布局，为企业的长效发展奠定基础。公司坚持以创新提升企业核心竞争力，以深圳集团公司为引领，相继建设了合肥研发中心、北京研发中心、上海研发中心、西安研发中心、武汉研发中心等 5 大研发中心，致力于成为国内信息化应用研发和先进技术的引领者。

天源迪科公司一直奉行"顾客至上"的宗旨，全面贯彻"以客户为中心"的服务理念，并在质量方针中明确提出"强化全程的服务模式"，建立了总部和分支机构两级客户服务体系。服务网络覆盖了华南，华中，华东，西南，西北等区域，在深圳、珠海、北京、贵阳、拉萨、合肥、青海、甘肃、上海、西安、成都、武汉、河南、湖南、河北、新疆等地成立分公司或办事处，建立由区域统一管控，向周边省份辐射的客户服务机构，为服务对象提供及时、优质的服务支撑。

未来，公司将继续秉承“诚信、专业、协作、创新”的精神，充分把握云计算、大数据、移动互联网应用发展的契机，扎根信息技术服务领域，勇于探索和创新，助力传统产业互联化和管理能力的大幅提升。用科技创造梦想，专注信息产业，努力将公司打造成为中国顶尖的、受人尊敬的IT 企业[21]。

# 3.1.2 主营业务

深圳天源迪科信息技术股份有限公司为客户提供信息系统的研发、综合解决方案的提供、技术支持等服务。公司除了传统信息系统的研发服务以外，还致力于创新技术的研发和应用，专注于大数据平台建设和大数据创新应用的解决方案提供。运用信息系统的建设和使用提升服务对象的业务管理能力和效率，推动企业组织结构的优化变革。天源迪科为客户提供包括基于云计算的运营支撑、大数据和互联网 $^ +$ 的综合解决方案。

在 IT 服务方面公司的主要业务范围涵盖：顾问服务（KPO）、业务流程服务（BPO）、IT工程服务（ITO）、产品工程服务（PES）、人力派遣服务（TSS）和IT人材培训（ITT）等多种信息技术服务；公司的业务领域以电信运营商和公共安全支撑为基础，拓展了政务、金融、信息安全以及新兴业务领域的云计算、智慧城市、水利建设等领域。天源迪科业务领域发展图如图3.1所示。

![](images/3526ca3084dc5a53cd36481ec912c2b9ee780639d5460d6df91fb2af658c9ce1.jpg)  
图3. 1 天源迪科业务发展图

电信运营商行业。1995 年，天源迪科正式进入电信行业，是中国电信和中国联通 IT 支撑领域核心合作伙伴，拥有了几十项自主研发的知识产权和软件产品，完成上百项大型电信业务支撑系统的项目建设[21]，公司自主研发的“融合计费软件”、“客户关系管理软件”、“高端客户管理与服务软件”、“电信行业经营分析与决策支持软件”、“终端交易平台”等软件产品的市场占有率在全国均名列前茅，创造了良好的经济效益和社会效益。

政府及公共安全行业。天源迪科自成立伊始就积极参与政府行业信息化建设，在二十多年不断建设积累过程中，持续推出专业解决方案，覆盖公安、海关等行业的多个业务领域。在智慧城市建设过程中，天源迪科秉承“软件即服务”理念，凭借扎实的业务知识，应用智慧技术锐意创新，以智能、互联、物联为核心，拓展高端指挥决策应用，利用智能设备创新业务模式，形成以指挥中心、运管中心、数据中心建设为代表的系列业务应用解决方案。

金融保险行业。天源迪科也是专注于金融保险行业的 IT 运营服务商。

# 3.1.3 组织架构

A公司在深圳、合肥、西安和上海几地分别设有集中的电信和公安软件研发中心，配备先进的软件研发设备和人性化的软件研发环境。汇集了千名系统架构师、业务专家、系统分析员、软件开发工程师和专业测试工程师，现有高中级项目经理 219 名，其中开发人员在 3000 人以上，技术人员占员工总人数的 $90 \%$ 。

A公司的组织架构现状为事业部制组织架构，按照研发产品行业划分为：电信事业部、联通事业部、公共与安全产品事业部、金融事业部、物联网事业部、云网事业部、大数据事业部、交管事业部等 7 大事业部及事业部制子公司，共计 12 个。各事业部内部以矩阵架构组建各细分业务部门，公司组织架构图如图3.2所示。

![](images/58d883f2b7312e3623fd595193c83ae0f384b1309b22d15bfe7b9dc93abfe827.jpg)  
图3. 2 公司组织架构图

# 3.2 A 公司运营管理平台软件开发项目进度管理存在的问题

本节的研究目的是通过对 A 公司软件开发项目进度管理问卷调查数据的描述和统计分析，系统、客观地分析 A 公司软件开发项目进度管理中的问题及影响因素，并为后续开展科学、有效的项目进度管理机制及改进措施的调查研究奠定基础。

# 3.2.1 调查问卷的设计

（1）调查目的与对象

本次问卷调查的目的是为了全面把握与了解 A 公司软件开发项目进度管理基本情况及造成进度延期情况在进度计划编制和实施过程中遇到的问题，为后续开展科学、有效的项目进度管理机制及改进措施的调查研究奠定基础。

本文选择的调查对象主要是针对A公司的开发项目经理及软件项目建设的团队，因此，调查对象设计包括三部分：第一，A 公司 40 位高级研发项目经理。第二，参与A公司运营管理平台软件开发项目工作16 人，包括项目经理、技术经理、产品经理和各开发小组长及团队成员。第三，A 公司资深开发人员 60 人。

（2）调查问卷的设计和发放

A 公司软件开发项目造成进度延期情况在进度计划编制和实施过程中遇到的问题的调查采用“李克特 5 级分析法”反馈调查项信息描述的认可度，数值 1 代表“完全不同意”，数值 5代表“完全同意”，1-5之间的选项代表从完全不认同到完全认同表述程度的过度。

调查问卷设计。调查问卷的内容设计包含两个部分：第一问卷调查对象的个人基本信息；第二对A公司（所在软件公司）的软件项目进度管理现状反馈及改进措施。

问卷填写人的基本信息对了解 A 公司软件开发项目的项目经理构成有重要的影响。填写的个人基本信息包括年龄、性别、学历、从事软件开发领域工作时间、担任项目经理角色的软件项目数量等。对 A 公司的软件项目进度管理现状反馈，让受访人根据自己的实际项目经验、工作体会与认识作出判断选择，以正确测量每个影因素对对A公司的软件项目进度延期的影响程度情况。具体调查问卷见附录。

调查问卷的发放。调查问卷发放的方式采用两种方式、三种渠道。发放方式采用线下纸质调查问卷 $^ +$ 线上电子调查问卷的方式。发放对象为：首先 A 公司软件开发项目经理，线下发放纸质调查问卷，共 40 份；其次 A 公司参与运营管理平台软件开发项目建设的项目团队，包含需求人员、开发人员、测试人员、实施人员，线下发放纸质调查问卷，共18份；第三线上发放对象为 A公司其他人员，线上发放电子调查问卷，共60份，合计发放调查问卷 118份。

# 3.2.2 调查问卷回收和数据统计分析

本次调查问卷线下回收纸质调查问卷58份，线上回收电子调查问卷60份，问卷回收率为 $100 \%$ ，其中有效调查问卷数 118 份（线上 60 份 $^ +$ 线下 58 份），无效调查问卷0份，问卷回收有效率 $100 \%$ 。问卷回收情况统计表见表3.1。

表3. 1 问卷回收情况统计表  

<table><tr><td>调查问卷发放方式</td><td>问卷回收</td><td>有效问卷</td><td>问卷回收率</td></tr><tr><td>线下发放</td><td>58</td><td>58</td><td>100%</td></tr><tr><td>线上发放</td><td>60</td><td>60</td><td>100%</td></tr><tr><td>合计</td><td>118</td><td>118</td><td>100%</td></tr></table>

（1）基础信息分析描述

在对A公司的软件项目进度管理现状的了解和制定改进措施进行深入分析之前，先对问卷调查人的个人基本情况数据分析和描述。个人基本信息描述如图 3.3 所示。

![](images/43a108fc8baf9ee3c77c56c046d21521374b41d7f03adefe5c8a99e655724a09.jpg)  
图3. 3 问卷填写人性别、年龄、学历、职称分析

由图上可知，本次问卷调查对象中 $9 2 \%$ 为男性， $8 \%$ 为女性，这一比例符合A公司员工整体男女性的占比，也与软件公司的整体情况基本相符。调查对象的年龄分布集中在29-36岁，占比为 $6 5 . 3 \%$ ，30岁左右且受教育程度以本科和硕士居多，占比为$9 7 . 4 \%$ （其中本科为 $7 9 . 7 \%$ ）。关于职称的调查项分析，了解到调查对象中获得了项目经理管理相关中高级职称或专业证书的占比超过 $70 \%$ 。通过这些数据描述可以掌握A公司软件开发项目项目经理角色的构成呈现出年富力强的状态，核心团队以青年男性为主，并且都具备了较高的受教育水平，这些良好的现实条件和具备很大的提升空间使A公司在项目进度管理的提升方面具备了良好的人力资源基础和力量储备。

下图显示，此次受调查人参与项目数超过 10 个的有人，占 $6 1 \%$ ， $94 \%$ 的受调查人员有 2 年以上工作经历，其中 6-8 年工作经历的人最多，占 $6 8 . 6 \%$ 。分析结果如图3.4 所示。

![](images/aa304563f2de8f87ca2671c0716e20619d4be5f869efd71d4628e7c338044747.jpg)  
图3. 4 问卷填写人参与项目数和工作年限分析

由此可知，受调查人对 A 公司的软件项目进度管理现状反馈及改进措施是基于长时间工作实践中和成熟深刻的思考。通过以上调查对象的基本信息分析标明本次问卷调查的调查结果具备高度可靠性，同时也标明了关于软件开发项目进度延误及如何科学有效的提升项目进度管理有一些较成熟的经验和认知的积累。以上这些为本论文后续的研究工作和A公司的项目管理体系提升提供了一定的基础。

# （2）信度检验

信度检验是通过采用相同的测量方法得出一定的结果数据，其目的是检查调查问卷中受调查人反馈数据和信息的可靠性和稳定性。李克特量表数据的信度检验采用L. J. Cronbach 发明的 $\mathfrak { a }$ 系数法[23]， $\mathfrak { a }$ 系数法与信度关系如表3.2所示（表中有 M表示 Cronbach $\mathbf { \bar { a } }$ 系数）：

表 $3 . 2 \mathfrak { a }$ 系数与信度高低关系对照表[23]  

<table><tr><td>Cronbach&#x27;α系数</td><td>可信度</td><td>正规量表</td></tr><tr><td>M&lt;0.500</td><td>舍弃不用</td><td>非常不理想，舍弃不用</td></tr><tr><td>0.500≤M≤0.599</td><td>可以接受</td><td>不理想，重新编制</td></tr><tr><td>0.600≤M≤0.699</td><td>尚佳</td><td>尚可接受</td></tr><tr><td>0.700≤M≤0.799</td><td>相当好</td><td>可以接受</td></tr><tr><td>0.800≤M≤0.899</td><td>甚佳</td><td>佳</td></tr><tr><td>M≥0.900</td><td>非常理想</td><td>甚佳</td></tr></table>

本论文采用 SPSS 软件对问卷回收的样本数据进行信度检验，结果如表 3.3。

表3. 3 调查问卷信度检验结果表[23]  

<table><tr><td>量表名称</td><td>Cronbach&#x27;a系数</td><td>基于标准化项的Cronbach&#x27;α系数</td><td>项数</td></tr><tr><td>项目进度计划编制存在问题表</td><td>0.821</td><td>0.821</td><td>8</td></tr><tr><td>项目实施过程中存在问题表</td><td>0.822</td><td>0.821</td><td>5</td></tr></table>

根据表 3.3 中的数据可知，此次问卷调查在项目进度计划编制存在问题信度检验系数为 0.821，标准化后的 $\mathfrak { a }$ 系数为 0.821；在实施过程中实际困难的信度检验系数为 0.822，标准化后的 $\mathfrak { a }$ 系数为 0.821；通过 $\mathfrak { a }$ 系数我们可以看出本次调查问卷的数据可性度非常高。

（3）参与的软件项目进度情况分析

通过对回收调查问卷数据分析结果显示，在受调查者中， $8 5 . 2 3 \%$ 的受调查者所参与的软件开发项目中存在进度延误问题，有 $1 4 . 7 7 \%$ 的受调查者表示参与的项目未出现进度延误。做了更新一层的分析后了解到，有 $58 \%$ 人表示项目进度延误率不超过$1 5 \%$ 。详细数据统计结果为： $3 1 . 2 7 \%$ 人员表示项目进度延误在0到 $10 \%$ 之间； $2 9 . 4 9 \%$ 的人表示在 $1 6 \%$ 到 $2 5 \%$ 之间； $1 4 . 7 7 \%$ 的人表示在 $2 6 \%$ 到 $3 5 \%$ 之间；

通过对以上数据统计分析后结果的描述得到 A 公司软件开发项目的进度延误问题比较严峻。

（4）A 公司项目进度计划编制存在问题分析。

使用SPSS软件对回收的问卷结果反馈进行分析，计算A公司在项目进度计划编制方面存在的问题及问题的均值和标准差。

![](images/b89a92f01886fa4a2550e22d9b121b7ee252c29ef0433ada1d5e898370957f96.jpg)  
图 3. 5A 公司软件开发项目进度延误情况调查

将回收的调查问卷导入 SPSS 软件进行分析，对产生项目进度延误影响因素的统计采用 RII 进行计算，计算结果如表 3.4 所示。

表3. 4A 公司计划编制存在问题  

<table><tr><td>序号</td><td>计划编制存在问题</td><td>平均值</td><td>标准偏差</td><td>RII</td></tr><tr><td>1</td><td>进度计划由项目经理片面制定，未与各角色负责 人充分沟通</td><td>3.823</td><td>1.094</td><td>0.765</td></tr><tr><td>2</td><td>项目工作分解不彻底、颗粒度太大</td><td>3.716</td><td>0.869</td><td>0.743</td></tr><tr><td>3</td><td>项目中工作量估计不合理</td><td>3.696</td><td>1.024</td><td>0.739</td></tr><tr><td>4</td><td>项目各工作间的依赖关系考虑不彻底、不清晰</td><td>3.618</td><td>0.879</td><td>0.724</td></tr><tr><td>5</td><td>项目进度目标设立不明确</td><td>3.608</td><td>0.952</td><td>0.722</td></tr><tr><td>6</td><td>任务和职责划分不够清晰或存在遗漏，未按照任 务划分清晰每个人的责任</td><td>3.549</td><td>0.993</td><td>0.710</td></tr><tr><td>7</td><td>对总体计划和项目阶段计划的关系处理不当，不 重视或忽视阶段计划的制定</td><td>3.343</td><td>0.949</td><td>0.668</td></tr><tr><td>8</td><td>计划编制过程中未充分识别项目中的技术难点</td><td>3.206</td><td>1.088</td><td>0.641</td></tr></table>

根据上表中的数据可以看出： A 公司计划编制存在的问题 8 项中前 3 为进度计划由项目经理片面制定，未与各角色负责人充分沟通；项目工作分解不彻底、颗粒度太大；项目中工作量估计不合理。也就是说 A 公司在软件开发计划编制阶段普遍存在这三项问题，且这三项问题对项目进度影响很大。

（5）A公司项目实施过程中存在问题分析

针对调查问卷中 A 公司在项目实施过程中存在问题的调查数据使用 SPSS 软件进行分析，得出此数据的平均值和标准差，同样采用相对重要性指数方法，计算RII，计算结果如表 3.5。

表 3. 5A 公司项目实施过程中存在问题  

<table><tr><td>序号</td><td>A公司项目实施过程中存在问题</td><td>平均值</td><td>标准偏差</td><td>RII</td></tr><tr><td>1</td><td>项目进度不能准确获取</td><td>3.736</td><td>0.903</td><td>0.747</td></tr><tr><td>2</td><td>出现项目进度滞后时，无应急预案</td><td>3.676</td><td>0.950</td><td>0.735</td></tr><tr><td>3</td><td>需求变更太随机，范围不断变更</td><td>3.637</td><td>0.865</td><td>0.727</td></tr><tr><td>4</td><td>人员随意调用</td><td>3.206</td><td>1.008</td><td>0.641</td></tr><tr><td>5</td><td>项目团队缺乏有效沟通</td><td>3.059</td><td>1.097</td><td>0.612</td></tr></table>

根据上表中的数据可以看出： A 公司项目实施过程中存在问题 5 项中前 3 为项目进度不能准确获取；出现项目进度滞后时,无应急预案；需求变更太随机，范围不断变更。也就是说 A 公司在项目实施过程中普遍存在这三项问题，且这三项问题对项目进度影响很大。

# 3.2.3 存在的问题汇总

通过 3.2.2 对调查问卷回收和数据统计分析结果汇总可得，A 公司软件开发项目进度管理存在问题从进度计划编制和进度控制两个方面进度分析汇总，分析和汇总结果如下图所示。

表3. 6A 公司进度管理存在问题汇总  

<table><tr><td>序号</td><td>工作环节</td><td>具体问题</td><td>汇总</td></tr><tr><td>1</td><td rowspan="3">进度计划编制</td><td>进度计划由项目经理片面制定，未与各角色负责人 充分沟通</td><td rowspan="3">进度计划编制 缺乏量化</td></tr><tr><td>2</td><td>项目工作分解不彻底、颗粒度太大</td></tr><tr><td>3</td><td>项目中工作量估计不合理</td></tr><tr><td>4</td><td rowspan="3">进度控制</td><td>项目进度不能准确获取</td><td rowspan="3">项目跟踪执行 过程中发现进 度偏差滞后</td></tr><tr><td>5</td><td>出现项目进度滞后时，无应急预案</td></tr><tr><td>6</td><td>需求变更太随机，范围不断变更</td></tr></table>

经过以上汇总可得，影响 A 公司软件开发项目进度管理最大的是项目进度计划编制缺乏量化和项目跟踪执行过程中发现进度偏差滞后两个问题。

# 3.3 A 公司运营管理平台软件开发项目进度管理问题成因分析

通过采用调查问卷的形式及对问卷收集数据的分析和笔者参与项目经验，我们了解到 A 公司在运营管理平台软件开发项目进度管理主要存在项目进度计划编制缺乏有效性和项目实施过程中缺乏跟踪控制管理两个问题。

# 3.3.1 项目进度计划编制缺乏量化

A公司运营管理平台软件开发项目在项目计划编制初期，项目工作分解、工作量估算和进度计划的编制由项目经理根据个人工作经验和客户要求折衷制定。由于项目经理一人的认知和思维有局限性，无法科学客观的制定出合理的项目进度计划，另外迫于客户侧和 A 公司领导侧的压力做出的计划也缺乏可执行性，因此导致当项目进度出现拖延时，当项目经理监测到进度延后时采用项目团队高强度加班来追赶进度，一味地通过加班的方式追赶进度就产生了无效率的加班现象，最终出现项目团队疲于应付的现象。

A 公司运营管理平台软件开发项目在项目进度计划编制方面存在以下 2 问题：

（1）工作分解不彻底

项目经理从个人工作经验角度进行的工作分解不够彻底，以数据治理模块工作分解为例，该工作分解为数据资源目录、数据资源池、数据接入、大数据计算平台、数据共享交换，没有考虑到对数据标准规范的工作分解，导致开发人员在开发的过程中耗费大量的时间和精力进行标准沟通和代码的修改。

# （2）工作量估算过长

A公司项目经理在工作计划编制的时候考虑了墨菲现象，为每一项工作预留了一定的安全时间，导致了每项工作计划时间过长，同时又没有为整个项目预留安全时间应对执行过程的突发状况。

表 3. 7 数据治理平台工作量估算偏差表  

<table><tr><td>任务编号</td><td>任务名称</td><td>任务计划天数</td><td>实际活动历时</td><td>偏差率</td></tr><tr><td>500</td><td>数据治理</td><td></td><td></td><td></td></tr><tr><td>501</td><td>数据资源目录</td><td>16</td><td>18</td><td>12.5%</td></tr><tr><td>502</td><td>数据资源池</td><td>20</td><td>24</td><td>20%</td></tr><tr><td>503</td><td>数据接入</td><td>10</td><td>12</td><td>20%</td></tr><tr><td>504</td><td>大数据计算平台</td><td>30</td><td>38</td><td>26.7%</td></tr><tr><td>505</td><td>数据共享交换</td><td>30</td><td>34</td><td>13.3%</td></tr></table>

经过以上问题分析，项目经理对项目整体未做完整的任务分析和分解，对任务的划分颗粒度较粗，并且项目经理制定项目进度计划后没有组织团队内部进行评审，直接将计划汇报给公司领导和可以，这样会引发两个问题：第一，颗粒度大势必造成任务划分有交集，也就是工作划分不明确，导致各任务间工作推脱，各模块负责人在工作边界的划分上消耗大量时间；第二，颗粒度大有时候会出现工作遗漏的情况，在项目进行过程中才发现工作遗漏，大大增加了项目按期完成的风险。

A 公司运营管理平台软件开发项目中各工作包的工期估算依靠项目经理大致估算，没有采用科学的方法，这样根据经验大致估算工作量无法准确反映项目工作量，同时也无法激发大家的工作积极性，使大家在接收到工作任务后具有时间紧迫感。不准确的工作量评估，往往会导致依此制定的项目计划失去意义，项目执行过程中，项目总是偏离项目计划，造成项目进程失控。

# 3.3.2 项目跟踪执行过程中发现进度偏差滞后

在项目实施过程中，项目经理不能及时、准确的获取项目进度情况，项目进度执行情况依靠各模块负责人汇报给项目经理，若各分负责人了解信息有误，或者比如程序员汇报进度会有报喜不报忧的现象，有可能小问题变成大问题，影响项目进度。

运营管理平台软件开发项目的进度计划制定完成后，没有对影响项目进度的关键工作进行识别，没有设定项目里程碑，导致在项目实施过程中，不能及时的发现项目进度滞后，也不能准确估算该部分任务滞后对项目整体进度的影响有多大，未能采取有效的应急预案，导致这些问题伴随着项目进度执行过程不断滚雪球，越积越大，最后项目经理才发现项目进度早已脱离当初制定的项目计划的轨道，无论采取什么措施，都挽回不了项目进度滞后的情况，不得不客户和公司内部沟通延长交付期限的方式换取项目最终得以交付。

运营管理平台软件开发项目在进度计划制定阶段没有考虑到该项目在 A 公司和全行业都是一个新领域项目，没有对项目执行中可能产生的客户需求变更制定应对需求变更的管控措施和预留安全时间。该项目执行中客户不断的提出对需求的“小”变更，项目经理没有对客户提出来的日常小的需求变更加以控制，也没有做好客户需求变更的相关记录，导致项目范围不断变化，开发人员熟悉了一个工作后还没开始编码工作就要变更该需求或暂缓该需求的开发去执行另外一个工作的开发，这样导致项目成员工作态度严重被影响，项目进度延期后客户对此又有很大的意见，项目经理疲于内外部协调。

由于项目执行初期未编制行之有效的项目进度计划，没有对计划组织评审，也就意味着项目执行中可能遇到的风险未被充分识别，因此在项目执行过程中，由于计划编制不合理、工作工期估算不准确等问题，以及项目经理对项目执行进度监控不及时，没有及时发现项目执行中的问题及进度的偏差，导致项目工期延期后项目采用增加资源（申请增加项目成员、团队加班赶工）的方式追赶项目进度，影响该项目的利润，最终使得项目比计划工期晚 42 天时完成交付。

# 第四章 A 公司运营管理平台软件升级项目进度管理优化方案

论文第三章通过问卷调查的形式发现 A 公司运营管理平台软件开发项目进度管理存在问题，通过问卷回收与问卷调查结果总结的方法，总结出一期项目在进度管理中存在项目进度计划编制缺乏量化、项目跟踪执行过程中发现进度偏差滞后两个问题。为了解决以上问题，本论文就 A 公司软件开发项目设计了一套进度管理方案，并将该方案应用于运营管理平台软件升级项目的进度管理中。

# 4.1 A 公司运营管理平台软件升级项目进度管理总体方案设计

项目进度管理是项目管理三大约束之一，为了充分发挥现代项目进度管理技术在软件开发项目进度管理中的功能，必须对软件开发项目进度设计科学、有效的总体方案，方案设计包含方案设计原理及目标、方案设计总体框架、方案设计流程。

# 4.1.1 方案设计原理及目标

第三章调查问卷的结果数据分析可知 A 公司在软件开发项目的进度管理方面虽有系统的管理方法和流程，但随着软件项目体量不断增大及多样化，在实际的项目执行工作中，这种粗放的管理方法和流程已经存在明显的弊端，往往导致项目进度严重延期，对企业的效益和信誉产生极大影响，亟需一套新的软件开发项目进度管控方案改进A公司的软件开发项目进度管理机制和管理流程。

软件开发项目进度管理在确保项目进度按期交付的同时，也需要实现企业利润的最大化。想要在实际执行过程中实现较好的进度控制效果，保证项目可以按照预期的工期计划合理完成，就需要对各阶段的工作进展程度和项目最终完成的期限所进行的管理。因此，制定一套科学的项目进度管理计划和项目执行管控方案，可以实现对项目执行的事前、事中、事后全流程管控，实现对项目进度的动态控制和主动控制。

![](images/399f252ac47d9907c1e6545e175ff3d18c54503431aca772b4f4f312203ee396.jpg)  
图 4. 1 A 公司软件开发项目进度管理方案总体设计

A 公司软件开发项目进度管理方案设计从项目工作量的优化、项目计划编制和项目跟踪控制三个方面进行设计，研究运用工作分解结构、三点估算法和挣值法有效的解决运营管理平台软件升级项目的进度管理问题，同时，为运营管理平台软件升级项目设置关键工作的里程碑控制点，采用基于挣值法的关键点进度纠偏方法对运营管理平台软件升级项目进行跟踪控制，分析并找出项目进度执行偏差的原因，通过合理的改善措施确保了项目按期保质完成并实现企业效益最大化的目标。

# 4.1.2 方案设计总体框架

结合第三章对项目经理的调查结果分析及对运营管理平台软件升级项目进度管理研究，针对A公司运营管理平台软件开发项目进度管理方案设计从基于WBS的项目工作量优化、基于关键链技术的项目计划编制和基于挣值法的项目跟踪控制三个层次展开，如图4.2所示。

![](images/77d2250c29ffadcf7c778aedb80effd5607832dd89a93361cd25a50a598f4d83.jpg)  
图 4. 2A 公司软件开发项目进度管理方案设计总体框架

（1）基于 WBS 的项目工作量优化

WBS能够明确项目所包含的工作任务，辅助项目经理控制项目过程，为项目工作报告提供依据。基于WBS的项目工作量优化的关键内容包含了解项目需求内容，按照功能系统进行项目的WBS分解，将复杂的项目按照特定的方法分解为一系列明朗直观的项目工作。基于确定的WBS工作分解结构采用三点估算法进行项目工作量估算。

# （2）基于关键链技术的项目计划编制

关键链技术来源于约束理论，用系统的思想来研究问题。关键链技术同时考虑了工序间的逻辑关系约束和工序间的资源冲突，通过缓冲区的设置保证项目按期完成。基于关键链技术的项目计划编制工作内容包含确定任务间的逻辑关系及资源约束情

况，将最长工作链确定为项目的关键链；设置项目缓冲区和输入缓冲区用来消除项目中不确定因素对项目执行计划的影响，依此确定最终的进度计划。

（3）基于挣值法的项目跟踪控制

挣值法通过定量分析三个基础指标和四个评价指标，可以分析项目进度和成本的执行情况，实时发现项目执行中的问题，调整执行偏差，确保项目目标的实现。

# 4.1.3 方案设计流程

通过笔者在A公司的工作情况及第三章对 A公司项目进度管理情况进行的调研结果表明，公司目前的粗放管理方法和管理流程已经存在明显的弊端，往往导致软件开发项目进度滞后和实际成本与计划产生偏离的情况。本章 4.1.1和4.1.2对软件开发项目进度管理方案设计，其优化后的进度管理流程如图4.3所示。

A公司现行项目管理流程

![](images/a6a079c0380b19669c724084eab89f83b0b8f469b63834e81e378f416f968e8c.jpg)  
图4. 3A 公司项目进度管理流程图

A公司现有软件项目管理流程为项目启动、计划制定、项目执行、项目监控、项目结束5个环节。改进后的 A公司软件项目管理流程为项目启动、计划、计划评审、执行和归档，在原流程环节的基础上增加了项目计划的评审环节；合并项目执行和监控环节为项目执行，新定义的项目执行将资源和进度的监控、执行结合起来，动态管控调整；另外，在项目结束后对执行项目进行知识归档，为企业项目管理的不断优化提升提供帮助和量化依据。

# 4.2 基于 WBS 的运营管理平台软件升级项目工作量优化

# 4.2.1 运营管理平台软件升级项目的 WBS

（1）WBS的工作原理详细、明确的工作分解是项目成功的关键影响因素之一。工作分解由项目成员共同参与。项目经理、需求人员、各工作组长分别负责项目的工作分解，各项工作分解后统一汇总为整体项目的完整工作分解，由项目成员对对分解的WBS的正确性和合理性进行评审。

项目工作分解在项目启动后进行，是采用自上而下的分解方式将整个项目一层一层的细分为更小工作的一个过程。工作作分解的最底层工作包，就是最小的可交付成果。工作分解结构示意图如图4.4所示。

![](images/58cc4ec89d018bb5def5b2c1a0f8e29e8f2b086e3573f6b757f1a1aab0728446.jpg)  
图 4. 4WBS工作分解结构示意图

项目工作分解的过程包含8个步骤。1）获取项目范围说明书；2）召集项目有关人员，确定项目主要工作和工作分解方式；3）分解项目工作；4）自上而下的画出WBS的分解层次图[49]；5）将项目的主要可交付成果进行细分，分解为更小、更易于管理的工作包；6）组织多次评审，反复验证上述分解的正确性；7）建立工作分解后的各工作包编号；8）随着项目的进行，不断地对 WBS 进行更新或者修改，直到该WBS覆盖项目所有工作。项目工作分解的流程图如图4.5所示。

![](images/36a332ca4d302e96acf5e1ceef4647a83c784fdd3a7680a709863abdd0eb99de.jpg)  
图4. 5 工作分解过程图

需要注意的是第五步“项目的主要可交付成果进行细分，分解为更小、更易于管理的工作包”时，工作包的分解必须详细到可以对该工作包进行成本和工时的估算、进度安排、分配负责人员或组织单位，每一项工作都独立的，且有一个可交付成果。工作分解必须覆盖项目所有工作。

项目工作分解针对一些复杂工作进行细化和分解，将工作内容分解到对应的责任人。注重将原本预留在各工作开发环节的测试方案设计和用例编写单独分解出来，并增加对方案和测试用例的评审。

（2）运营管理平台软件升级项目WBS的具体操作

通过需求人员和开发团队对项目更深的理解，本项目的 WEB 端系统与企业其它信息系统存在 10 多个接口，与每一个品牌的充电桩存在 26 个接口，本项目涉及 5个品牌充电桩，每个品牌可用于调试的充电桩设备只有 1 台，并且只有 2 个品牌设备在项目初期采购到位，另外3个品牌设备在项目进行到后期的时候才能供货，如果在软件平台开发的过程中同步进行接口的调试，则会因为资源等待和调试等待耗费大量工期，因此考虑将软件接口在项目初期进行设计规划，对WEB端平台提前开发接口，针对硬件接口在软件功能开发完成后统一集中资源调试；另外，项目初期的工作分解没有将每项任务的模块测试分离出来，与功能的开发打包在一起进行分解和工作量估算，这样导致估算者会为每项活动预留安全时间，导致工期被估算过长，产生成本浪费。

基于以上分析，需要在对项目整体内容了解尽可能全面的情况下对运营管理平台软件升级项目进行分解，为项目工作量的准确估算和更科学的项目计划编制打好基础，也便于项目进度的安排、跟踪和人员投入的管控。

图4.6为重新调整项目开展思路，分离出硬件配合改造和系统测试的未分解的网络计划图，项目共分为需求调研、总体架构设计、企业服务总线开发与测试、充电桩运营管理系统、移动客户端、系统初验、硬件配合改造、系统测试、系统上线运行、系统终验10个任务大项。

![](images/160c19055398ab03d089ae60d9fb7181a91e6d99b5928df515bcc1ec437e9c42.jpg)  
图 4. 6 运营管理平台软件升级项目未分解网络计划图

基于图4.6的项目未分解网络计划图进行项目工作分解，运营管理平台软件升级项目的工作分解图见图4.7。在对企业服务总线开发法和测试、充电桩运营管理系统、移动客户端三个模块进行工作分解的时候，对每项任务均增加模块测试，并且为确保系统上线运行的顺利进行，将系统业务测试单独分解出来，该模块由客户完成系统业务确认测试。

![](images/ed6c6ca6561f6c23ab6e5900893cc47a35a26854bdf67b0cfbfb2d78a4ce9e19.jpg)  
图 4. 7 分解后的项目WBS

确定活动之间的逻辑关系，如表 4.1 所示。

表 4. 1 运营管理平台软件升级项目活动逻辑关系列表  

<table><tr><td>任务编号</td><td>任务名称</td><td>紧前任务</td></tr><tr><td>000</td><td>需求调研</td><td></td></tr><tr><td>001</td><td>需求调研</td><td></td></tr><tr><td>002</td><td>需求分析</td><td>001</td></tr><tr><td>003</td><td>需求确认</td><td>002</td></tr><tr><td>100</td><td>总体架构设计</td><td></td></tr><tr><td>101</td><td>WEB端架构设计</td><td>003</td></tr><tr><td>102</td><td>移动端架构设计</td><td>003</td></tr><tr><td>103</td><td>硬件接口总体设计</td><td>003</td></tr><tr><td>104</td><td>软件接口总体设计</td><td>003</td></tr><tr><td>200</td><td>企业服务总线开发和测试</td><td></td></tr><tr><td>201</td><td>服务提供模块</td><td>003、104</td></tr><tr><td>202</td><td>服务逻辑模块</td><td>201</td></tr><tr><td>203</td><td>服务配置模块</td><td>202</td></tr><tr><td>204</td><td>服务监控模块</td><td>203</td></tr><tr><td>205</td><td>文件交换模块</td><td>204</td></tr><tr><td>206</td><td>模块测试</td><td>205</td></tr><tr><td>300</td><td>充电桩运营管理系统</td><td></td></tr><tr><td>301</td><td>用户管理</td><td>206</td></tr><tr><td>302</td><td>费用管理</td><td>206</td></tr><tr><td>303</td><td>充电桩管理</td><td>206</td></tr><tr><td>304</td><td>公共支撑</td><td>206</td></tr><tr><td>305</td><td>充电流程管理</td><td>206</td></tr><tr><td>306</td><td>钱包管理</td><td>301、302、303、304、305</td></tr></table>

<table><tr><td>307</td><td>营销管理</td><td>306</td></tr><tr><td>308</td><td>统计报表</td><td>306</td></tr><tr><td>309</td><td>多租户管理</td><td>306</td></tr><tr><td>310</td><td>消息管理中心</td><td>307、308、309</td></tr><tr><td>311</td><td>模块测试</td><td>310</td></tr><tr><td>400</td><td>移动客户端（安卓和IOS)</td><td></td></tr><tr><td>401</td><td>整体设计</td><td>206</td></tr><tr><td>402</td><td>用户管理</td><td>401</td></tr><tr><td>403</td><td>首页</td><td>401</td></tr><tr><td>404</td><td>GIS地图</td><td>401</td></tr><tr><td>405</td><td>钱包管理</td><td>401</td></tr><tr><td>406</td><td>充电管理</td><td>401</td></tr><tr><td>407</td><td>消息中心</td><td>402、403、404、405、406</td></tr><tr><td>408</td><td>推荐应用下载</td><td>407</td></tr><tr><td>409</td><td>模块测试</td><td>408</td></tr><tr><td>500</td><td>系统初验</td><td></td></tr><tr><td>600</td><td>配合硬件改造</td><td></td></tr><tr><td>601</td><td>电桩后台管理WEB</td><td>500</td></tr><tr><td>602</td><td>集中监控模拟</td><td>500</td></tr><tr><td>700</td><td>系统业务测试</td><td></td></tr><tr><td>701</td><td>系统连接测试</td><td>601、602</td></tr><tr><td>702</td><td>业务确认测试</td><td>701</td></tr><tr><td>800</td><td>系统上线运行</td><td></td></tr><tr><td>801</td><td>系统上线运行</td><td>702</td></tr><tr><td>802</td><td>运行维护</td><td>801</td></tr><tr><td>900</td><td>系统终验</td><td>802</td></tr></table>

# 4.2.2 工作量估算优化

任何项目的执行过程中都会有特殊情况和潜在风险的发生，为了项目的顺利进行，项目经理需要组织项目成员在项目初期的计划编制阶段对项目过程中可能存在的潜在风险进行识别，分析可能产生的条件及影响因素，提前做好应对，并预留好风险处理时间。运营管理平台软件升级项目任务分解采用三点估算法进行项目工作量估算，增加对项目执行中可能存在的风险进行考虑和估算，体现项目管理中的风险意识。

# （1）三点估算法的工作原理

通过调查问卷项目经理反馈及第四章的项目实践论证，为提高项目工作量估算的科学性、准确性，同时考虑到项目管理人员的可操作性，A公司在对新的软件开发项目在工作量评估时采用三点估算法替代原使用的代码行估算法（经验估算法），即综合对风险的考虑估算项目工作的最长时间、最短时间和最可能时间。三点估算法的采用能统一专家评估标准，减小专家估算的误差，提高项目工作量计算分析的精度，实际可操作性更强。

根据项目的 WBS 分解结构和项目的资源情况，对项目所需要的时间进行估算。为了避免由项目经理或部分负责人因个人经验差异及对项目整体考虑不到而估算的工作量，导致工作量估算不准确，影响项目的实际进度和管理。项目经理在工作量评估时应尽可能的多与项目团队的关键成员、骨干人员进行沟通，对可能存在的潜在风险进行识别[25]，分析可能产生的条件及影响因素，由各模块负责人及骨干人员负责工作量评估。项目经理对各模块的工作量评估组织多次的评审，验证工作量估算的准确性。

（2）A公司运营管理平台软件开发项目工作量估算具体操作

三点估算法的应用步骤如下：第一、项目经理、任务小组长、核心技术人员、需求人员和研发人员通过对项目背景和项目内容的讨论评估出各项活动的最乐观工时、最可能工时、最悲观工时三个持续工时。第二、运用三点估算法计算出一个活动期望工时。基于三点估算法重新估算的工作量清单如表 4.2：

表 4. 2 基于三点估算法的工作量估算表  

<table><tr><td>任务编号</td><td>任务名称</td><td>最可能工作 时长（日)</td><td>最乐观工作 时长（日）</td><td>最悲观工作 时长（日）</td><td>预期活动持 续时长（日）</td></tr><tr><td>000</td><td>需求调研</td><td></td><td></td><td></td><td></td></tr><tr><td>001</td><td>需求调研</td><td>7</td><td>6</td><td>8</td><td>7</td></tr><tr><td>002</td><td>需求分析</td><td>6</td><td>5</td><td>7</td><td>6</td></tr><tr><td>003</td><td>需求确认</td><td>2</td><td>1</td><td>3</td><td>2</td></tr><tr><td>100</td><td>总体架构设计</td><td></td><td></td><td></td><td></td></tr><tr><td>101</td><td>WEB 端架构设计</td><td>3</td><td>2.5</td><td>3.5</td><td>3</td></tr><tr><td>102</td><td>移动端架构设计</td><td>2</td><td>2</td><td>2</td><td>2</td></tr><tr><td>103</td><td>硬件接口总体设计</td><td>2</td><td>1</td><td>3</td><td>2</td></tr><tr><td>104</td><td>软件接口总体设计</td><td>1</td><td>0.5</td><td>1.5</td><td>1</td></tr><tr><td>200</td><td>企业服务总线开发 和测试</td><td></td><td></td><td></td><td></td></tr><tr><td>201</td><td>服务提供模块</td><td>14</td><td>10</td><td>18</td><td>14</td></tr><tr><td>202</td><td>服务逻辑模块</td><td>14</td><td>12</td><td>16</td><td>14</td></tr><tr><td>203</td><td>服务配置模块</td><td>21</td><td>20</td><td>28</td><td>22</td></tr><tr><td>204</td><td>服务监控模块</td><td>7</td><td>7</td><td>13</td><td>8</td></tr><tr><td>205</td><td>文件交换模块</td><td>2</td><td>1.5</td><td>2.5</td><td>2</td></tr><tr><td>206</td><td>模块测试</td><td>1</td><td>1</td><td>1</td><td>1</td></tr><tr><td>300</td><td>充电桩运营管理系 统</td><td></td><td></td><td></td><td></td></tr><tr><td>301</td><td>用户管理</td><td>6</td><td>5</td><td>7</td><td>6</td></tr><tr><td>302</td><td>费用管理</td><td>9</td><td>8</td><td>16</td><td>10</td></tr><tr><td>303</td><td>充电桩管理</td><td>12</td><td>11</td><td>13</td><td>12</td></tr><tr><td>304</td><td>公共支撑</td><td>10</td><td>8</td><td>12</td><td>10</td></tr><tr><td>305</td><td>充电流程管理</td><td>7</td><td>6</td><td>14</td><td>8</td></tr><tr><td>306</td><td>钱包管理</td><td>4</td><td>3</td><td>5</td><td>4</td></tr><tr><td>307</td><td>营销管理</td><td>6</td><td>5</td><td>7</td><td>6</td></tr></table>

<table><tr><td>308</td><td>统计报表</td><td>4</td><td>2.5</td><td>5.5</td><td>4</td></tr><tr><td>309</td><td>多租户管理</td><td>10</td><td>9</td><td>11</td><td>10</td></tr><tr><td>310</td><td>消息管理中心</td><td>10</td><td>8</td><td>12</td><td>10</td></tr><tr><td>311</td><td>模块测试</td><td>4</td><td>3</td><td>5</td><td>4</td></tr><tr><td>400</td><td>移动客户端（安卓 和IOS)</td><td></td><td></td><td></td><td></td></tr><tr><td>401</td><td>整体设计</td><td>6</td><td>5</td><td>7</td><td>6</td></tr><tr><td>402</td><td>用户管理</td><td>4</td><td>3.5</td><td>4.5</td><td>4</td></tr><tr><td>403</td><td>首页</td><td>2</td><td>1</td><td>3</td><td>2</td></tr><tr><td>404</td><td>GIS地图</td><td>2</td><td>1.5</td><td>2.5</td><td>2</td></tr><tr><td>405</td><td>钱包管理</td><td>8</td><td>6</td><td>16</td><td>9</td></tr><tr><td>406</td><td>充电管理</td><td>7.5</td><td>7</td><td>11</td><td>8</td></tr><tr><td>407</td><td>消息中心</td><td>2</td><td>1.5</td><td>2.5</td><td>2</td></tr><tr><td>408</td><td>推荐应用下载</td><td>1</td><td>1</td><td>1</td><td>1</td></tr><tr><td>409</td><td>模块测试</td><td>4</td><td>3</td><td>5</td><td>4</td></tr><tr><td>500</td><td>系统初验</td><td>1</td><td>0.5</td><td>1.5</td><td>1</td></tr><tr><td>600</td><td>配合硬件改造</td><td></td><td></td><td></td><td></td></tr><tr><td>601</td><td>电桩后台管理WEB</td><td>5</td><td>4</td><td>6</td><td>5</td></tr><tr><td>602</td><td>集中监控模拟</td><td>2</td><td>1.5</td><td>2.5</td><td>2</td></tr><tr><td>700</td><td>系统业务测试</td><td></td><td></td><td></td><td></td></tr><tr><td>701</td><td>系统连接测试</td><td>6</td><td>5</td><td>7</td><td>6</td></tr><tr><td>702</td><td>业务确认测试</td><td>2</td><td>2</td><td>2</td><td>2</td></tr><tr><td>800</td><td>系统上线运行</td><td></td><td></td><td></td><td></td></tr><tr><td>801</td><td>系统上线运行</td><td>3</td><td>2.5</td><td>3.5</td><td>3</td></tr><tr><td>802</td><td>运行维护</td><td>7</td><td>6</td><td>8</td><td>7</td></tr><tr><td>900</td><td>系统终验</td><td>1</td><td>0.5</td><td>1.5</td><td>1</td></tr></table>

考虑到项目初期需求人员不可能准确的一次性完成需求调研、设计和确认工作，新项目的开发需求随着开发任务的开展会涉及到部分客户需求的变更，需要在后期项目开发开展的过程中迭代进行模块的需求设计和确认工作，因此，工作量估算时减少了部分项目前期需求调研的工作量，将减少的这部分工作量分配到每个模块的工作量估算中。同时，本次工作量估算时将初期预留到每项活动的测试工作量分离出来单独估算，将上线前的客户确认测试从上线运行模块中分解出来单独估算，这样的思路和估算方法增加了项目工作量估算的准确性。

# 4.3 基于关键链技术的运营管理平台软件升级项目计划编制优化

通过第三章对运营管理平台软件开发项目的问题分析可知，首先由于墨菲定律的考虑，项目计划制定时预留了每项任务的安全时间，导致项目工期估算过长；其次由于学生综合症和帕金森现象，研发人员对分配的任务迟迟不开工以及完工后不及时反馈，导致项目计划工期被浪费；项目实际实施阶段，研发人员出现资源调配问题，反应出计划制定阶段仅仅考虑任务间的逻辑关系而确定项目关键路径，依此估算的项目工期不准确；本项目管理人员经过考虑，为避免以上问题对整体项目开发的持续影响，决定采用关键链法编制项目进度计划，保持项目资源更加均衡的分布，设置项目缓冲区进行项目执行进度的整体管控。

# 4.3.1 基于关键链的网络计划图

（1）基于关键链的计划编制工作原理

项目工作WBS确定之后，结合项目的目标和工期要求，确定各工作间的逻辑关系，确定工作编号，编制项目进度计划，制定活动的开始和结束时间，一旦时间确定之后，就需要由活动负责人对活动计划进行审查，确认活动内容和资源日历是否冲突，进而确定计划的有效性。工作计划编制作业图如图4.8所示。

![](images/ae9c830eb05a68845bb722ef0c967d1c8e0e8b79af9e34243afad1d35e60ca77.jpg)  
图4. 8 工作计划编制作业图

工作计划编制的几个重要前提是 1）项目文件完整；2）项目资源（人员）配置已明确；3）对项目内外环境因素有一定的了解，对项目可能存在的风险做了识别和判断；4）工作分解彻底，已完成；5）各工作项工作量估算完成。A 公司工作计划编制用到的主要工具技术是关键链法和里程碑。工作计划编制的输出是 1）进度基准；2）项目进度计划；3）项目里程碑；4）随着需求的调整和计划的评审，对进度计划进行调整。另外，项目进度计划确定后，管理人员对项目的需求做好管控，避免出现因需求频繁变化或需求有大的变更导致项目进度计划变更，甚至直接导致项目进度延期的情况发生。

通过第三章的问卷调查结果显示，项目进行过程中普遍具有学生综合症和帕金森综合症浪费工作时间，导致项目实际开发过程中会出现因为进度延迟而导致项目失败。因此，A 公司对现有工作计划编制方法进行优化调整，采用关键链法。采用关键链法的网络计划图编制步骤为：1）基于任务分解和确定的任务逻辑关系设计网络计划图；2）为了避免开发人员在任务开发阶段因学生综合征和帕金森线下对项目工期的拖延，减少没想任务的安全工期；3）输出压缩后的网络计划图；4）计算路径的工期，确定项目关键链；5）设置项目缓冲和输入缓冲。

通过关键链法在 A 公司项目进度管理中的运用，弥补 A 公司以往项目进度管理方面的不足，在考虑任务间紧前和紧后关系约束的同时，增加组织行为的应用和对任务间的资源冲突的考虑，注重项目管理的艺术性，提升项目管理的综合水平。

（2）A公司运营管理平台软件开发项目的网络计划图具体操作

综合第二章的理论知识研究分析以及第三章项目执行过程中进度管控存在的问题分析，运营管理平台软件升级项目的进度计划编制采用关键链法。基于关键链法的网络计划图编制步骤如下：

第一步：结合图 4.7 的工作分解 WBS 和表 4.1 确定的任务间逻辑关系，加入对核心开发人员资源约束的考虑。考虑到项目一期因为团队中核心开发人员数量有限，无法对并行的任务同时开展开发工作，因此需将因为核心开发人员资源冲突的并行任务调整为串行任务，设计网络计划图。

第二步：减少每项任务的安全工期。由于开发人员在任务开发时具有学生综合症和帕金森现象，为防止该现象的出现以及该现象对项目工期的影响，对项目任务的计划时间按照 $50 \%$ 完成概率，缩小一半的工期，减少项目整体的计划时间。

第三步：输出网络计划图。第一步对核心开发人员安排的考虑和第二步压缩后的项目计划时间画出新的网络计划图，并确认新的关键路径。新网络计划图的关键路径上除去已经完成的任务，剩余未完成以及未启动任务的计划时间时原计划工作时间的$50 \%$ 。

经过上述三个步骤，画出了网络计划图，网络计划图中的关键路径就确认为项目的关键链，新基于关键链法的网络计划图如图4.9所示：

![](images/f90789f437086646540f9b1ad66e40da0ec4394333abab9302d7aad629ff5a75.jpg)  
图4. 9 基于关键链的运营管理平台软件升级项目网络计划图

# 4.3.2 缓冲区设置

关键链技术在项目管理中运用的重点和难点是项目缓冲区和输入缓冲区的设置和缓冲区值的确定，本设计中采用 2.2.5 章节中介绍的 Goldratt 提出的 $50 \%$ 法计算缓冲区大小。

# （1）缓冲区插入

项目缓冲区 PB：将原本预留给关键链上每项工作的安全时间剥离出来，作为项目缓冲区设置在工序 900 之后，即关键链的最后。输入缓冲区 FB：为了不使关键路线延迟，在非关键线路与关键线路汇合处设置输入缓冲区 FB，即在工序 409 与工序500之间设置输入缓冲区。

# （2）缓冲区大小

计算项目缓冲 PB：关键链中每个工作的安全时间累加和的 $50 \%$ ，即工序001-002-003-101-201-202-203-204-205-206-303-305-306-309-310-311-500-601-701-702-801-802-900 安 全 时 间 累 加 和 的 $50 \%$ 即 $\mathrm { P B } =$ （ $( 7 + 3 + 1 + 1 . 5 + 7 + 7 + 1 1 + 4 + 1 + 0 . 5 + 6 + 4 + 2 + 5 + 5 + 2 + 0 . 5 + 2 . 5 + 3 + 1 + 1 . 5 + 3 . 5 + 0 . 5 )$ ） $\times 5 0 \% { = } 3 9 . 7 5$ ，向上取整， $\mathrm { P B } { = } 4 0$ 。计算输入缓冲区 FB：工序 401 至工序 409 线路上最长路径所有非关键工作安全时间累加和的 $50 \%$ ，即 401-405-407-408-409 安全时间累加和的 $50 \%$ ，即 $\mathrm { F B = }$ （ $3 { + } 4 . 5 { + } 1 { + } 0 . 5 { + } 2 $ ） $\times 5 0 \% { = } 5 . 5$ ，向上取整， $\mathrm { F B } { = } 6$ 。

根据上述计算，加入项目缓冲 PB 和输入缓冲 FB，得到新的网络计划图，新的基于关键链法的网络计划图如图4.10。

![](images/1ac8b410aed09fa6fcd2b54a9ff99e7aa9a6d243f783da31d0b726f63a6e37ef.jpg)  
图4. 10 基于关键链的网络计划图

通 过 计 算 我 们 可 以 得 知 ， 调 整 后 的 网 络 计 划 图 关 键 链 路 为001-002-003-101-201-202-203-204-205-206-303-305-306-309-310-311-500-601-701-702-801-802-900 ， 非 关 键 链 路 为001-002-003-101-201-202-203-204-205-206-401-405-407-408-409-500-601-701-702-801-802-900 ， 项 目 计 划 工 期 为7 $+ 3 + 1 + 1 . 5 + 7 + 7 + 1 1 + 4 + 1 + 0 . 5 + 6 + 4 + 2 + 5 + 5 + 2 + 0 . 5 + 2 . 5 + 3 + 1 + 1 . 5 + 3 . 5 + 0 . 5 + 4 0 = 1 1 9 . 5$ 天。

# 4.4 基于挣值法的运营管理平台软件升级项目的跟踪控制

针对实施阶段存在的问题，对项目进度监控及其纠偏措施进行优化，采用里程碑计划和基于关键链法和挣值法的软件项目监控模型对运营管理平台软件升级项目执行进度监控和纠偏。首先项目关键点上设立里程碑作为项目进度监控点，然后根据基于关键链和挣值法的项目监控模型对里程碑节点进行监控考核，两种方法结合使用监控项目进度来采取纠偏措施。

# 4.4.1 设置运营管理平台软件升级项目的关键控制点

设置运营管理平台软件升级项目的关键控制点从里程碑设置工作原理和在本项目中的具体应用两个方面进行编写。

（1）项目里程碑设置的工作原理

里程碑是项目经理进行项目整体工作进度管控的最主要依据。A公司软件开发类项目管理增加里程碑管理，制定项目各个阶段的目标，实现对项目整体工作进度的监控和管理。

项目里程碑的制定，首先确定项目最后一个里程碑。其余里程碑按照关键工作节点、客户关注点确定项目的里程碑检查点，采用 Microsoft Project 软件制作甘特图，确定和细化项目目标，将项目设定为实际的、具体的工作，每一个里程碑都有完成的交付物和标准。

对里程碑的监控遵从 4 个原则。第一，以里程碑为项目进度管理的关键点，把项目的执行阶段进行切分，及时监控里程碑的完成情况，必要的时候予以调整，对整个项目进度实行动态调整。第二，每一个具体的里程碑与具体角色责权关联。里程碑一旦确定，各模块负责人应确保按时完成该里程碑内容和确保里程碑完成的质量，达到了对软件项目质量的整体控制。第三，制定每个里程碑验收标准，给出一个清晰的里程碑验收准则，依此判断里程碑工作的执行结果。第四，所有里程碑的完成代表了整个项目工作的完成，因此每一个里程碑的制定都需要与整个项目的进度计划相结合，通过对里程碑完成的检验可以掌握项目整体工作的进展。

设立项目里程碑，以及对项目里程碑的良好管控，可以使项目管理人员和项目干系人（项目经理、项目总监、项目成员、客户项目干系人）直观的了解项目整体实施进度，了解项目执行与目标是否一致。里程碑交付物和成果的检查验证，项目经理可以分阶段提前发现项目需求、设计和进度控制中存在的问题，为剩余项目工作做准备。

（2）A 公司运营管理平台软件开发项目关键控制点设置的具体操作

运营管理平台软件升级项目，制定了项目各个阶段的完成目标，并且在关键点上设立了里程碑，以确保项目的顺利完成。通过设立里程碑，项目团队可以通过不断的检查当前项目实施进度是否与项目编制计划存在出入，是否按照当初的计划进行实施，对项目进度进行更好的控制。通过检测里程碑完成效果，可以判断项目的进度与质量，为后续工作做准备，以高效的完成项目任务。

运营管理平台软件升级项目里程碑计划是由项目的管理者和关键项目事件相关的负责人召开项目启动会共同讨论并制定的，这种集体讨论的形式要比项目经理一人拍板决定的里程碑计划更为合理、可行。同时参会的人为项目各关键阶段负责人，后期计划的实施，各负责人可以做到心中有数，以减少项目实施过程中的阻力，使项目实施得到更大范围的支持。参会人员共 7 名：客户技术经理、项目总监、项目经理、产品经理、架构设计师、开发经理、测试经理。

A公司运营管理平台软件升级项目，通过以上会议表决的形式，并结合各专家的以往项目经验设定项目里程碑，最后由项目管理人员进行最后的整理汇总，得出最终的项目里程碑如下：

里程碑1：需求确认完毕。需求是软件开发项目的源头，每个项目都从一个需求开始。需求是用户想实现什么样的功能，到达什么目的。在此基础上，需求分析就是根据用户的描述，进行更深入的挖掘，将大概的功能描述出来，更进一步的分析，弄

清楚用户的目的，实现用户的需求。

需求确认完毕的完成标准是《业务需求说明书》符合《项目工作说明书》的内容，且通过会议评审。

里程碑2：总体架构设计完毕。A公司运营管理平台软件升级项目的总体架构设计参考了项目合同、整体项目需求、系统运行的内外部环境（包含内部流程、外部承载系统部署的硬件环境及与周边系统的关联关系）、软件开发标准、数据标准等要求。

运营管理平台软件升级项目总体架构设计包括WEB端架构设计、移动端架构设计、硬件接口总体设计、软件接口总体设计，总体架构设计的完成标志为，形成《总体架构设计说明书》，并通过会议评审。

里程碑3：企业服务总线开发和测试完毕。企业服务总线开发和测试完成标准为《企业服务总线开发和测试模块测试报告》测试用例通过率 $100 \%$ 。

里程碑4：移动客户端（安卓和IOS）开发和测试完毕。移动客户端（安卓和IOS）开发和测试完成标准为《充电桩运营管理系统模块测试报告》测试用例通过率 $100 \%$ 。

里程碑5：充电桩运营管理系统开发和测试完毕。充电桩运营管理系统开发和测试完成标准为《充电桩运营管理系统模块测试报告》测试用例通过率 $100 \%$ 。

根据运营管理平台软件升级项目工期要求和已编制计划，最终的里程碑计划如表4.3 所示，里程碑计划图如图 4.11 所示。

表4. 3 运营管理平台软件升级项目里程碑计划表  

<table><tr><td>里程碑编号</td><td>里程碑时间点</td><td>可交付物</td><td>完成标准</td></tr><tr><td>里程碑1</td><td>2020年3月11日需求 确认完毕</td><td>《业务需求说明书》</td><td>《业务需求说明书》符合 《项目工作说明书》的内 容，且通过会议评审</td></tr><tr><td>里程碑2</td><td>2020年3月13日总体 架构设计完毕</td><td>《总体架构设计说明书》</td><td>《总体架构设计说明书》 通过会议评审</td></tr><tr><td>里程碑3</td><td>2020年4月24日企业 服务总线开发和测试完 毕</td><td>《企业服务总线开发和测 试模块测试报告》</td><td>《企业服务总线开发和测 试模块测试报告》测试用 例通过率100%</td></tr><tr><td>里程碑4</td><td>2020年5月9日移动客 户端开发和测试完毕</td><td>《移动客户端模块测试报 告》</td><td>《充电桩运营管理系统模 块测试报告》测试用例通 过率100%</td></tr><tr><td>里程碑5</td><td>2020年5月28日充电 桩运营管理系统开发和 测试完毕</td><td>《充电桩运营管理系统模 块测试报告》</td><td>《充电桩运营管理系统模 块测试报告》测试用例通 过率100%</td></tr></table>

![](images/df3889eea73e7becaedb7b8783c0021240fe4e2f0b047496ddeb1aa41d0e7c18.jpg)  
图 4. 11 项目里程碑设置图

里程碑 3、里程碑 4、里程碑 5 均为软件开发和测试阶段，该阶段主要为按照功能设计，分模块、分功能的进行编程开发。此阶段将项目的整体编码和测试单独划分工作进行时间，随着软件开发项目执行过程中出现的需求变更，则项目计划和相关的技术文档也执行动态更改。

# 4.4.2 基于关键链法和挣值法的关键点进度纠偏

基于关键链法和挣值法的关键点进度纠偏分别从关键链挣值法的项目进度纠偏工作原理和在本项目中的具体应用进行编写。

（1）基于关键链法和挣值法的项目进度纠偏工作原理

软件项目进度管理的主要目标是在预先限定的时间范围内，制定出行之有效的项目进度计划，以及在项目实施过程中，动态地监控执行进度与计划是否偏离，目的是保证项目能按时完成。在软件开发项目中，人力成本又是项目的主要成本，而工期与成本关系紧密。因此，软件项目进度管控及纠偏尤为重要，决定了项目的成败及企业经济效益。

A公司以往项目的纠偏只是单一的考虑范围、进度、成本、质量某一个维度进行评价和纠偏。改进提升后在项目进度管控及纠偏管控方面采用关键链与挣值法相结合的方法对项目进度进行监控，通过对关键链和非关键链的缓冲区消耗管控，降低项目进度风险。通过项目缓冲区的监控帮助项目经理和项目干系人及时了解项目实际进展，若发现项目的实际执行情况与制定的计划进度不一致，需要及时分析进度偏差的原因，必要时需要立即采集措施对原制定的项目进度计划进行调整，以使项目的实际工期实现最优，多快好省的完成项目的开发工作。

关键链法与挣值分析结合具体的挣值分析步骤有 7 步：1）建立项目工作计划：确认项目需求，进行工作结构分解；确定工作先后顺序；根据三点估算法对各工作进行工作时间估算；根据关键链法在网络计划图中插入合适的缓冲区，建立项目工作计划；2）建立成本核算系统，为运用挣值法监控项目进度做铺垫；3）收集已完成工作的工作量和实际费用，确认实际工作情况；4）计算关键链路和非关键链路的 SV 指标，分析项目进度；5）根据项目执行情况结合缓冲区消耗情况决定是否采取纠偏措施；6）若采取措施进行纠偏，则纠偏后判断项目是否结束，若未结束则循环进行步骤 3-6；若未采取纠偏措施，则判断项目是否结束，若未结束则同样循环进行步骤 3-6；7）直至项目结束。关键链挣值法的进度及成本管控流程图如图4.12所示。

![](images/356f4fcafd14e47abf9655d7d3abbf4ed7d35fd28cbbdb3d170ee344ded9129e.jpg)  
图4. 12 进度及成本控制流程图

在项目执行的过程中持续收集项目的进度和成本数据，与制定的计划进度和成本进行比较，计算关键链和非关键链的进度偏差，根据项目实际偏差情况判断是否需采集纠偏措施。如需，依据项目进展实际情况采取纠偏措施；如不需，则项目正常进行，持续监控，收集项目进度数据。

关键链挣值法的运用有利于项目经理对项目的整体状态做出判断，时刻了解项目资源分配和实际执行的匹配情况，为项目资源调整提供有力依据。

（2）A公司运营管理平台软件开发项目计算纠偏的具体操作

传统的挣值法在实际应用中存在不少问题，具体表现在以下两方面：首先，忽略了项目内部各工作之间的逻辑关系：从宏观的角度进行问题分析，不考虑项目的工作分解结构，将项目和计划的项目成本作为一个整体进行管控，项目进度的管控与成本管控在工作项中未结合，无法实现同步监控。其次，传统的挣值管理方法忽略了项目内部工作的执行情况，不能对关键链和非关键链上的挣值予以区分，若非关键链路上工作进度较快，即取得的挣值较多，而关键链上工作进度滞后，则非关键链路上的挣值很可能掩盖关键链路上工作延迟的事实，导致项目进度判断错误，最终很可能导致整个项目失败。

鉴于上述两种情况，需对挣值法进行优化，将挣值法与关键链法结合起来，分别计算关键链路和非关键链路挣值，以区分挣值来源于关键链路还是非关键链路，从而更加准确的判断关键链路和非关键链路项目的执行情况。

基于关键链法和挣值法的软件项目监控模型及其分析流程。将关键链法与挣值分析结合，具体的挣值分析步骤：1）建立项目工作计划：熟悉项目情况，确认项目需求，进行工作结构分解；依据各工作间的逻辑关系确定工作之间的先后顺序关系；根据三点估算法对各工作进行工作时间估算；根据关键链法在网络计划图中插入合适的缓冲区，建立项目工作计划；2）建立成本核算系统，为运用挣值法监控项目进度做铺垫；3）收集已完成工作的工作量和实际费用，确认实际工作情况；4）计算关键链路和非关键链路的 SV 指标，分析项目进度；5）根据项目执行情况结合缓冲区消耗情况决定是否采取纠偏措施；6）若采取措施进行纠偏，则纠偏后判断项目是否结束，若未结束则循环进行步骤 3-6；若未采取纠偏措施，则判断项目是否结束，若未结束则同样循环进行步骤3-6；7）直至项目结束。

关键链法的挣值分析流程如图 4.13 所示：

![](images/4a9385ab4f4ceeb77f5379b4b3428aba7815cf7a4f5eca3c3a4cd4364589a3d1.jpg)  
图 4. 13 基于关键链法的挣值分析流程图

建立运营管理平台软件升级项目的成本核算系统。软件项目成本的主要部分是人力成本，人力成本属于项目的直接成本，直接成本是因项目的存在和执行而导致的成本发生，项目取消则成本不再发生的成本。如项目成员的人力资源费用（直接人力成本）、由于项目需要所产生的差旅（直接非人力成本）、培训（直接非人力成本）等费用；而间接成本为：服务于本组织所有研发项目的联合成本，即只要有研发活动成本就会产生，而所有研发活动都取消则成本不再发生。如研发管理人员的费用分摊（间接人力成本）、研发设备/场地的费用分摊（间接非人力成本）。软件项目成本构成如图 4.14 所示：

![](images/572c50f11cd4215f0a4d1b1e2d9f2092a26d3e99a914ac3ee42e211a985fcfc5.jpg)  
图4. 14 软件项目成本构成图

实际项目中常用的软件项目成本评估公式为：工作量 $\mathbf { \partial } =$ 传统工期 $\times 7 5 \% \times$ 该项工作的人力资源配置，运营管理平台软件升级项目的计划工作量如表4.4所示。

表4. 4 运营管理平台软件升级项目计划工作量表  

<table><tr><td>任务编号</td><td>任务名称</td><td>资源名称</td><td>工期</td><td>人资源</td><td>工作量</td></tr><tr><td>000</td><td>需求调研</td><td></td><td></td><td></td><td></td></tr><tr><td>001</td><td>需求调研</td><td>业务需求</td><td>5.25</td><td>3</td><td>15.75</td></tr><tr><td>002</td><td>需求分析</td><td>业务需求</td><td>4.5</td><td>3</td><td>13.5</td></tr><tr><td>003</td><td>需求确认</td><td>业务需求</td><td>1.5</td><td>3</td><td>4.5</td></tr><tr><td>100</td><td>总体架构设计</td><td></td><td></td><td></td><td></td></tr><tr><td>101</td><td>WEB端架构设计</td><td>研发工程师</td><td>2.25</td><td>10</td><td>22.5</td></tr><tr><td>102</td><td>移动端架构设计</td><td>研发工程师</td><td>1.5</td><td>10</td><td>15</td></tr><tr><td>103</td><td>硬件接口总体设计</td><td>研发工程师</td><td>1.5</td><td>10</td><td>15</td></tr><tr><td>104</td><td>软件接口总体设计</td><td>研发工程师</td><td>0.75</td><td>10</td><td>7.5</td></tr><tr><td>200</td><td>企业服务总线开发和测 试</td><td></td><td></td><td></td><td></td></tr><tr><td>201</td><td>服务提供模块</td><td>研发工程师</td><td>10.5</td><td>8</td><td>84</td></tr><tr><td>202</td><td>服务逻辑模块</td><td>研发工程师</td><td>10.5</td><td>9</td><td>94.5</td></tr><tr><td>203</td><td>服务配置模块</td><td>研发工程师</td><td>16.5</td><td>7</td><td>115.5</td></tr><tr><td>204</td><td>服务监控模块</td><td>研发工程师</td><td>6</td><td>8</td><td>48</td></tr><tr><td>205</td><td>文件交换模块</td><td>研发工程师</td><td>1.5</td><td>8</td><td>12</td></tr><tr><td>206</td><td>模块测试</td><td>测试人员</td><td>0.75</td><td>3</td><td>2.25</td></tr><tr><td>300</td><td>充电桩运营管理系统</td><td></td><td></td><td></td><td></td></tr><tr><td>301</td><td>用户管理</td><td>研发工程师</td><td>4.5</td><td>8</td><td>36</td></tr><tr><td>302</td><td>费用管理</td><td>研发工程师</td><td>7.5</td><td>10</td><td>75</td></tr><tr><td>303</td><td>充电桩管理</td><td>研发工程师</td><td>9</td><td>10</td><td>90</td></tr><tr><td>304</td><td>公共支撑</td><td>研发工程师</td><td>7.5</td><td>9</td><td>67.5</td></tr><tr><td>305</td><td>充电流程管理</td><td>研发工程师</td><td>6</td><td>12</td><td>72</td></tr><tr><td>306</td><td>钱包管理</td><td>研发工程师</td><td>3</td><td>10</td><td>30</td></tr><tr><td>307</td><td>营销管理</td><td>研发工程师</td><td>4.5</td><td>9</td><td>40.5</td></tr><tr><td>308</td><td>统计报表</td><td>研发工程师</td><td>3</td><td>9</td><td>27</td></tr><tr><td>309</td><td>多租户管理</td><td>研发工程师</td><td>7.5</td><td>8</td><td>60</td></tr><tr><td>310</td><td>消息管理中心</td><td>研发工程师</td><td>7.5</td><td>9</td><td>67.5</td></tr><tr><td>311 400</td><td>模块测试 移动客户端（安卓和</td><td>测试人员</td><td>3</td><td>5</td><td>15</td></tr><tr><td></td><td>IOS)</td><td></td><td></td><td></td><td></td></tr><tr><td>401</td><td>整体设计</td><td>研发工程师</td><td>4.5</td><td>10</td><td>45</td></tr><tr><td>402</td><td>用户管理</td><td>研发工程师</td><td>3</td><td>7</td><td>21</td></tr><tr><td>403</td><td>首页</td><td>研发工程师</td><td>1.5</td><td>6</td><td>9</td></tr><tr><td>404</td><td>GIS地图</td><td>研发工程师</td><td>1.5</td><td>8</td><td>12</td></tr><tr><td>405</td><td>钱包管理</td><td>研发工程师</td><td>6.75</td><td>5</td><td>33.75</td></tr><tr><td>406</td><td>充电管理</td><td>研发工程师</td><td>6</td><td>10</td><td>60</td></tr><tr><td>407</td><td>消息中心</td><td>研发工程师</td><td>1.5</td><td>6</td><td>9</td></tr><tr><td>408</td><td>推荐应用下载</td><td>研发工程师</td><td>0.75</td><td>6</td><td>4.5</td></tr><tr><td>409</td><td>模块测试</td><td>测试人员</td><td>3</td><td>8</td><td>24</td></tr></table>

<table><tr><td>500</td><td>系统初验</td><td>项目管理</td><td>0.75</td><td>2</td><td>1.5</td></tr><tr><td>600</td><td>配合硬件改造</td><td></td><td></td><td></td><td></td></tr><tr><td>601</td><td>电桩后台管理WEB</td><td>研发工程师</td><td>3.75</td><td>6</td><td>22.5</td></tr><tr><td>602</td><td>集中监控模拟</td><td>研发工程师</td><td>1.5</td><td>8</td><td>12</td></tr><tr><td>700</td><td>系统业务测试</td><td></td><td></td><td></td><td></td></tr><tr><td>701</td><td>系统连接测试</td><td>测试人员</td><td>4.5</td><td>5</td><td>22.5</td></tr><tr><td>702</td><td>业务确认测试</td><td>测试人员</td><td>1.5</td><td>5</td><td>7.5</td></tr><tr><td>800</td><td>系统上线运行</td><td></td><td></td><td></td><td></td></tr><tr><td>801</td><td>系统上线运行</td><td>项目管理</td><td>2.25</td><td>2</td><td>4.5</td></tr><tr><td>802</td><td>运行维护</td><td>项目管理</td><td>5.25</td><td>2</td><td>10.5</td></tr><tr><td>900</td><td>系统终验</td><td>项目管理</td><td>0.75</td><td>2</td><td>1.5</td></tr></table>

软件项目成本 $: = 1$ 工作量 $\times$ 平均人力成本费率（含直接人力成本、间接成本） $^ +$ 直接非人力成本。平均人力成本费率由公司同类工作人员的统计得出，根据项目合同和投标报价清单，运营管理平台软件升级项目的计划预算见表4.5：

表 4. 5 各工序计划预算费用表  

<table><tr><td>任务 编号</td><td>任务名称</td><td>工作量（日）</td><td>平均人力 成本费率 （元/日）</td><td>直接人力 成本+间 接成本</td><td>直接非人 力成本</td><td>预算费 用</td></tr><tr><td>000</td><td>需求调研</td><td></td><td></td><td></td><td></td><td></td></tr><tr><td>001</td><td>需求调研</td><td>15.75</td><td>770</td><td>12127.5</td><td>2400</td><td>14527.5</td></tr><tr><td>002</td><td>需求分析</td><td>13.5</td><td>770</td><td>10395</td><td>2400</td><td>12795</td></tr><tr><td>003</td><td>需求确认</td><td>4.5</td><td>770</td><td>3465</td><td>3000</td><td>6465</td></tr><tr><td>100</td><td>总体架构设计</td><td></td><td></td><td></td><td></td><td></td></tr><tr><td>101</td><td>WEB 端架构设计</td><td>22.5</td><td>900</td><td>20250</td><td>0</td><td>20250</td></tr><tr><td>102</td><td>移动端架构设计</td><td>15</td><td>900</td><td>13500</td><td>0</td><td>13500</td></tr><tr><td>103</td><td>硬件接口总体设 计</td><td>15</td><td>900</td><td>13500</td><td>0</td><td>13500</td></tr><tr><td>104</td><td>软件接口总体设 计</td><td>7.5</td><td>900</td><td>6750</td><td>0</td><td>6750</td></tr><tr><td>200</td><td>企业服务总线开 发和测试</td><td></td><td></td><td></td><td></td><td></td></tr><tr><td>201</td><td>服务提供模块</td><td>84</td><td>900</td><td>75600</td><td>0</td><td>75600</td></tr><tr><td>202</td><td>服务逻辑模块</td><td>94.5</td><td>900</td><td>85050</td><td>0</td><td>85050</td></tr><tr><td>203</td><td>服务配置模块</td><td>115.5</td><td>900</td><td>103950</td><td>0</td><td>103950</td></tr><tr><td>204</td><td>服务监控模块</td><td>48</td><td>900</td><td>43200</td><td>0</td><td>43200</td></tr><tr><td>205</td><td>文件交换模块</td><td>12</td><td>900</td><td>10800</td><td>0</td><td>10800</td></tr><tr><td>206</td><td>模块测试</td><td>2.25</td><td>560</td><td>1260</td><td>0</td><td>1260</td></tr><tr><td>300</td><td>充电桩运营管理 系统</td><td></td><td></td><td></td><td></td><td></td></tr></table>

<table><tr><td>301</td><td>用户管理</td><td>36</td><td>900</td><td>32400</td><td>0</td></tr><tr><td>302</td><td>费用管理</td><td>75</td><td>900</td><td>67500 0</td><td>32400 67500</td></tr><tr><td>303</td><td>充电桩管理</td><td>90</td><td>900</td><td>81000</td><td>0 81000</td></tr><tr><td>304</td><td>公共支撑</td><td>67.5</td><td>900</td><td>60750</td><td>0 60750</td></tr><tr><td>305</td><td>充电流程管理</td><td>72</td><td>900</td><td>64800</td><td>0 64800</td></tr><tr><td>306</td><td>钱包管理</td><td>30</td><td>900</td><td>27000</td><td>0 27000</td></tr><tr><td>307</td><td>营销管理</td><td>40.5</td><td>900</td><td>36450</td><td>0 36450</td></tr><tr><td>308</td><td>统计报表</td><td>27</td><td>900</td><td>24300</td><td>0 24300</td></tr><tr><td>309</td><td>多租户管理</td><td>60</td><td>900</td><td>54000</td><td>0 54000</td></tr><tr><td>310</td><td>消息管理中心</td><td>67.5</td><td>900</td><td>60750</td><td>0 60750</td></tr><tr><td>311</td><td>模块测试</td><td>15</td><td>560</td><td>8400</td><td>0 8400</td></tr><tr><td>400</td><td>移动客户端（安卓 和IOS)</td><td></td><td></td><td></td><td></td></tr><tr><td>401</td><td>整体设计</td><td>45</td><td>900</td><td>40500</td><td>0 40500</td></tr><tr><td>402</td><td>用户管理</td><td>21</td><td>900</td><td>18900 0</td><td>18900</td></tr><tr><td>403</td><td>首页</td><td>9</td><td>900</td><td>8100 0</td><td>8100</td></tr><tr><td>404</td><td>GIS地图</td><td>12</td><td>900</td><td>10800 0</td><td>10800</td></tr><tr><td>405</td><td>钱包管理</td><td>33.75</td><td>900</td><td>30375 0</td><td>30375</td></tr><tr><td>406</td><td>充电管理</td><td>60</td><td>900</td><td>54000 0</td><td>54000</td></tr><tr><td>407</td><td>消息中心</td><td>9</td><td>900</td><td>8100 0</td><td>8100</td></tr><tr><td>408</td><td>推荐应用下载</td><td>4.5</td><td>900</td><td>4050 0</td><td>4050</td></tr><tr><td>409</td><td>模块测试</td><td>24</td><td>560</td><td>13440</td><td>0 13440</td></tr><tr><td>500</td><td>系统初验</td><td>1.5</td><td>1200</td><td>1800</td><td>5000 6800</td></tr><tr><td>600</td><td>配合硬件改造</td><td></td><td></td><td>0</td><td></td></tr><tr><td>601</td><td>电桩后台管理</td><td>22.5</td><td>900</td><td>20250</td><td>0 20250</td></tr><tr><td>602</td><td>WEB 集中监控模拟</td><td>12</td><td>900</td><td>10800</td><td>0 10800</td></tr><tr><td>700</td><td>系统业务测试</td><td></td><td></td><td>0</td><td></td></tr><tr><td>701</td><td>系统连接测试</td><td>22.5</td><td>560</td><td>12600 0</td><td>12600</td></tr><tr><td>702</td><td>业务确认测试</td><td>7.5</td><td>560</td><td>4200 0</td><td>4200</td></tr><tr><td>800</td><td>系统上线运行</td><td></td><td></td><td>0</td><td></td></tr><tr><td>801</td><td>系统上线运行</td><td>4.5</td><td>1200</td><td>5400</td><td>2000 7400</td></tr><tr><td>802</td><td>运行维护</td><td>10.5</td><td>1200</td><td>12600</td><td>2000 14600</td></tr><tr><td>900</td><td>系统终验</td><td>1.5</td><td>1200</td><td>1800</td><td>4000 5800</td></tr><tr><td>合计</td><td></td><td></td><td></td><td></td><td>1217080</td></tr></table>

基于关键链法和挣值法的挣值数据分析。为了避免以往使用的挣值分析中出现的不足与偏差，本论文以传统挣值分析理论为基础，对初期制定的项目进度计划网络图和确定的关键链和非关键链，采用基于关键链的挣值分析方法进行项目进度管控。

为了区分关键链路和非关键路的挣值，另外引入六个重要参数 BCWSc、BCWPc、SVc、BCWSn、BCWPn、SVn，分别表示关键链路的计划工作预算、关键链路的已完成工作预算、关键链路的进度偏差、非关键链路的计划工作预算、非关键链路的已完成工作预算、非关键链路进度偏差，并且有以下等式成立。

SVc=BCWPc-BCWSc；

SVn=BCWPn-BCWSn；

项目进度的管控和分析需要同时分析关键链和非关键链的进度与整体项目进度计划的偏差，除此以外，也要考虑项目执行成本与计划成本的偏差，通过这些指标综合把握项目进度的执行情况，对进度偏差的存在以下几种情况：

$\mathrm { S V } { < } 0$ ， $\mathrm { S V c } { < } 0$ ：项目整体进度滞后，同时关键链路的工作进度也滞后。对于关键链路需要判断关键链路的滞后是否超过项目缓冲的 1/3，若未超过则不需要采取措施，若超过 1/3 但未超过 2/3 则需采取一定措施加快项目进度，但不需修改项目进度计划，若超过 2/3，表示项目执行进度出现问题，严重滞后，此时项目经理需要对项目缓冲区消耗的原因进行分析，了解缓冲区消耗原因后采取必要的管控措施，避免项目总工期的延误；对于非关键链路需分析是否在输入缓冲区的偏差范围内，如果偏差暂不影响项目进度，可不采取措施，对于输入缓冲区消耗的控制方法同项目缓冲区消耗的控制方法。

$\mathrm { S V } { < } 0 , \mathrm { S V } \mathrm { c } { > } 0$ ：项目整体进度滞后，但是关键链路的工作进度提前，说明非关键链路工作进度滞后，此时需要对非关键链路采取措施，比如增加资源等，以防非关键链路变为关键链路，影响整个项目进度。

$\mathrm { S V { > } 0 } { , } \mathrm { S V } \mathrm { c } { > } 0$ ：项目整体进度提前，关键链路进度提前，需要关注非关键链路的工作进度执行情况，同时监控项目执行费用的使用情况，如果项目进度提前但是产生的费用超过计划费用，适当的调整项目工作的执行进度，使项目执行的费用消耗与项目进度同步。

$\mathrm { S V } { > } 0 { , } \mathrm { S V } \mathrm { c } { < } 0$ ：项目整体进度提前，但是关键链路的工作进度滞后，说明非关键链上进度提前挣得的挣值掩盖了关键链路项目进度滞后的事实，此时需要对关键链路采取措施来保证项目进度，比如从非关键链路调取资源以保证关键链路上的工作顺利执行。

本项目进度里程碑节点监控及其纠偏。

里程碑节点1：需求确认完毕。

项目计划工作预算（BCWS）：此时 BCWS ${ } = { }$ 计划工作量 $\times$ 预算定额 $\risingdotseq$ BCWS（001）$^ +$ BCW $\begin{array} { r } { \mathrm { \textbf { S } } ( 0 0 2 ) \ + \mathrm { B C W S } \ ( 0 0 3 ) \ = 1 4 5 2 7 . 5 + 1 2 7 9 5 + 6 4 6 5 = 3 3 7 8 7 . 5 } \end{array}$ 由于工序 001、002、003 均处在关键链路上，故 $\mathrm { B C W S c = B C W S = } 3 3 7 8 7 . 5$ ；项目已完工作预算（BCWP）：

此时工序003已经完成 $7 5 \%$ ，此时实际的工作预算费用BCWP $\vDash$ BCWP（001）+BCWP$\mathrm { ~ ( ~ } 0 0 2 \mathrm { ~ ) ~ + ~ } \mathrm { B C W P }$ （ 003 ） = BCWS （ 001 ） $^ +$ BCWS（002） $^ +$ BCWS （003）$\times 7 5 \% = 1 4 5 2 7 . 5 + 1 2 7 9 5 + 6 4 6 5 \times 7 5 \% =$ 32171.25 ； 项 目 进 度 差 异 ：SV $\fallingdotseq$ BCWP-BCWS 32171.25-33787.5=-1616.25<0，表示当前进度滞后；经过计算滞后时间约为 0.25 天，占用项目缓冲时间（40 天）其中的 0.25 天，未超过 1/3，不需采取措施。

里程碑2：总体架构设计完毕。

项目计划工作预算（BCWS）：此时 BCWS ${ } = { }$ 计划工作量 $\times$ 预算定额 $\risingdotseq$ BCWS（001）$\mathbf { \Sigma } + \mathbf { B C W S } \mathbf { \Phi } ( 0 0 2 ) + \mathbf { B C W S } \mathbf { \Phi } ( 0 0 3 ) + \mathbf { B C W S } \mathbf { \Phi } ( \mathbf { \bar { \Sigma } }$ （101） $^ +$ BCWS（102） $^ +$ BCWS（103） $^ +$ BCWS（ $( 1 0 4 ) = 1 4 5 2 7 . 5 + 1 2 7 9 5 + 6 4 6 5 + 2 0 2 5 0 + 1 3 5 0 0 + 1 3 5 0 0 + 6 7 5 0 = 8 7 7 8 7 . 5$ 由于工序 001、002、003、均处在关键链路上，故 $\mathrm { B C W S c = B C W S = 8 7 7 8 7 . 5 }$ ；项目已完工作预算（BCWP）：此时工序201已经完成 $10 \%$ ，此时实际的工作预算费用BCWP $\vDash$ BCWP（001）+BCWP$\mathrm { ( 0 0 2 ) } + \mathrm { B C W P } \mathrm { ( 0 0 3 ) } + \mathrm { B C W P } \mathrm { ( 1 0 1 ) } + \mathrm { B C W P } \mathrm { ( 1 0 2 ) } + \mathrm { B C W P } \mathrm { ( 1 0 3 ) } + \mathrm { B C W P } \mathrm { ( } 1 0 3 \mathrm { ) } + \mathrm { B C W P } \mathrm { ( } 1 0 3 \mathrm { ) } + \mathrm { B C W P } \mathrm { ( } 1 0 3 \mathrm { ) } .$ （104）$\mathrm { \Delta - B C W P ~ ( 2 0 1 ) ~ = B C W S ~ ( 0 0 1 ) ~ + ~ \Delta ~ B C W S ~ ( 0 0 2 ) ~ + ~ \Delta ~ B C W S ~ ( 0 0 3 ) ~ + ~ B C W S ~ }$ （102）$+ \ B C W S$ （103） $^ +$ BCWS（104） $^ { + + }$ BCWS（201） $\times 1 0 \% { = } 9 5 3 4 7 . 5$ ；项目进度差异：$\mathrm { S V = B C W P \mathrm { - B C W S = 9 5 3 4 7 . 5 - 8 7 7 8 7 . 5 = 7 5 6 0 > 0 } }$ ，表示当前进度提前。

里程碑3：企业服务总线开发和测试完毕。

项目计划工作预算（BCWS）：此时 BCWS ${ } = { }$ 计划工作量 $\times$ 预算定额 $\risingdotseq$ BCWS（001）$^ +$ BCW $\mathrm { ~ 5 ~ ( 0 0 2 ) + B C W S ~ ( 0 0 3 ) + B C W S ~ ( 1 0 1 ) + B C W S ~ ( 1 0 2 ) + B C W S ~ ( 1 0 3 ) + B C W S }$ （104） $^ +$ BCWS（201）+BCWS（202） $+$ BCWS（203）+BCWS（204）+BCWS（205）$+ \mathrm { B C W S }$ （ ${ } ( 2 0 6 ) = 4 0 7 6 4 7 . 5$ ，由于工序 001-003、101-104、201-206 均处在关键链路上，故 $\mathrm { B C W S c { = } B C W S { = } } 4 0 7 6 4 7 . 5$ ；项目已完工作预算（BCWP）：此时工序 204 已经完成$50 \%$ ，此时实际的工作预算费用 BCWP $\vDash$ BCWP（001）+BCWP（002）+ BCWP（003）+BC $\mathrm { V P } \left( 1 0 1 \right) + \mathrm { B C W P } \left( 1 0 2 \right) + \mathrm { B C W P } \left( 1 0 3 \right) + \mathrm { B C W P } \left( 1 0 4 \right) + \mathrm { B C W P } \left( 2 0 1 \right) + \mathrm { B C W P } \left( \left( 2 0 1 \right) + \mathrm { B C W P } \left( \left( 3 0 2 \right) + \mathrm { B } \right) \right)$ （2 $\mathrm { 1 2 ) + B C W P ~ ( 2 0 3 ) ~ + B C W P ~ ( 2 0 4 ) ~ = B C W S ~ ( 0 0 1 ) ~ + ~ B C W S ~ ( 0 0 2 ) ~ + ~ \ B C W S ~ }$ （0 $\mathrm { 1 3 ) + B C W S ~ ( 1 0 2 ) + B C W S ~ ( 1 0 3 ) + B C W S ~ ( 1 0 4 ) ~ + A C W S ~ ( 2 0 1 ) ~ + B C W S ~ }$ $( 2 0 2 ) \ + \ \mathrm { B C W S } ( 2 0 3 ) \ + \ \mathrm { B C W S } ( 2 0 4 ) \times 5 0 ^ { 0 } \% = 3 7 3 9 8 7 . 5 ; \quad \mathbb { M } \sharp \sharp \underline { { \mathtt { M } } } \underline { { \mathtt { M } } } \underline { { \mathtt { M } } } \underline { { \mathtt { M } } } \underline { { \mathtt { M } } } \underline { { \mathtt { M } } } \underline { { \mathtt { M } } } \underline { { \mathtt { M } } } \underline { { \mathtt { M } } } \underline { { \mathtt { M } } } \underline { { \mathtt { M } } } \underline { { \mathtt { M } } } \underline { { \mathtt { M } } } \underline { { \mathtt { M } } } \underline { { \mathtt { M } } } \underline { { \mathtt { M } } } \underline { { \mathtt { M } } } \underline { { \mathtt { M } } } \underline { { \mathtt { M } } } \underline { { \mathtt { M } } } \underline { { \mathtt { M } } } \underline { { \mathtt { M } } } \underline { { \mathtt { M } } } \underline { { \mathtt { M } } } \underline { { \mathtt { M } } } \underline { { \mathtt { M } } } \underline { { \mathtt { M } } } \underline { { \mathtt { M } } } \underline { { \mathtt { M } } } \underline { { \mathtt { M } } } \underline { { \mathtt { M } } } \underline { { \mathtt { M } } } \underline { { \mathtt { M } } } \underline { { \mathtt { M } } } \underline { { \mathtt { M } } } \underline { { \mathtt { M } } } \underline { { \mathtt { M } } } \underline { { \mathtt { M } } } \underline { { \mathtt { M } } } \underline { { \mathtt { M } } } \underline { { \mathtt { M } } } \underline { { \mathtt { M } } } \underline { { \mathtt { M } } } \underline { { \mathtt { M } } } \underline { { \mathtt { M } } } \underline { { \mathtt { M } } } \underline { { \mathtt { M } } } \underline { { \mathtt { M } } } \underline { { \mathtt { M } } } \underline { { \mathtt { M } } } \underline { { \mathtt { M } } } \underline { { \mathtt { M } } } \underline { { \mathtt { M } } } \underline { { \mathtt 0 } } \underline  { M }$ 异 ：$\mathrm { S V = B C W P - B C W S } = 3 7 3 9 8 7 . 5 \mathrm { - } 4 0 7 6 4 7 . 5 \mathrm { = } \mathrm { - } 3 3 6 6 0 \mathrm { < } 0$ ，表示当前进度滞后；经计算滞后时间 3.5 天，占用项目缓冲时间（40 天）其中的 3.5 天，未超过 1/3，不需采取措施。

里程碑4：移动客户端（安卓和IOS）开发和测试完毕。

项目计划工作预算（BCWS）：此时非关键链 ${ \mathrm { B C W S n } } { \mathrm { = } }$ 计划工作量 $\times$ 预算定额=BCWS $\mathrm { ; \left( 0 0 1 \right) + B C W S \left( 0 0 2 \right) + B C W S \left( 0 0 3 \right) + B C W S \left( 1 0 1 \right) + B C W S \left( 1 0 2 \right) + B C W S \left( 2 0 3 \right) } .$ $\mathrm { ( 1 0 3 ) + B C W S ~ ( 1 0 4 ) + B C W S ~ ( 2 0 1 ) + B C W S ~ ( 2 0 2 ) ~ + B C W S ~ ( 2 0 3 ) ~ + B C W S ~ ( 2 0 3 ) }$ 4）$+ \mathrm { B C W S }$ $\mathrm { S W S ~ ( 2 0 5 ) + B C W S ~ ( 2 0 6 ) + B C W S ~ ( 4 0 1 ) + B C W S ~ ( 4 0 2 ) + B C W S ~ ( 4 0 3 ) + B C W S }$ （404） $^ +$ BCWS（405）+BCWS（406） $^ +$ BCWS（407）+BCWS（407）+BCWS（409）

$= 5 9 5 9 1 2 . 5 $ ，此时关键链 $\mathrm { B C W S c } { = } 7 1 6 7 9 7 . 5$ ；项目已完工作预算（BCWP）：此时工序303 已经完成 $100 \%$ ，工序 405 已经完成 $100 \%$ ，此时关键链实际的工作预算费用BCWP $\mathrm { c } { = }$ BCWP（001）+BCWP（002） $^ +$ BCWP（003） $+ \mathrm { B C W P }$ （101） $+$ BCWP（102）$^ +$ BCWP（103）+BCWP（104）+BCWP（201） $^ +$ BCWP（202） $+ \mathrm { B C W P }$ （203） $+ \mathrm { B C W P }$ （204） $+ \bullet \bullet +$ BCWP（205） $+ \mathrm { B C W P }$ （206） $+ \mathrm { B C W P }$ （301） $+ \mathrm { B C W P }$ （302） $+ \mathrm { B C W P }$ （303）588547.5；非关键链实际的工作预算费用 $\mathrm { B C W P n } { = } 5 1 6 3 2 2 . 5$ 项目进度差异：$\mathrm  S V c { = } B C W P c { \mathrm { - } } B C W S c { = } 5 8 8 5 4 7 . 5 { \mathrm { - } } 7 1 6 7 9 7 . 5 { = } { \mathrm { - } } 1 2 8 2 5 0 { < } 0$ ，表示当前进度滞后，滞后约 5天，占用项目缓冲时间（40 天）其中的 5 天，未超过 1/3，不需采取措施；SVn=BCWPn-BCWSn $\equiv$ 516322.5-595912.5<0，表示当前进度滞后，滞后约 3.5 天，占用输入缓冲时间（6天）其中的3.5天，超过1/3未超过2/3，由于输入缓冲时间余量很大，此处不需采取措施。

里程碑 5：充电桩运营管理系统开发和测试完毕。.

项目计划工作预算（BCWS）：此时BCWS $\varepsilon =$ 计划工作量 $\times$ 预算定额 $=$ BCWS（001）$^ +$ BCWS（ $\mathrm { ( 0 0 2 ) + B C W S \ ( 0 0 3 ) + B C W S \ ( 1 0 1 ) + B C W S \ ( 1 0 2 ) + B C W S \ ( 1 0 3 ) + B C W S }$ （104） $^ +$ BCWS（201） $+ \mathrm { B C W S }$ （202） $+ \mathrm { B C W S }$ （203） $+ \mathrm { B C W S }$ （204）+BCWS（205）+BCW $\mathrm { 7 S ~ ( 2 0 6 ) } + \mathrm { B C W S ~ ( 3 0 1 ) } + \mathrm { B C W S ~ ( 3 0 2 ) } + \mathrm { B C W S ~ ( 3 0 3 ) } + \mathrm { B C W S ~ ( 3 0 4 ) } + \mathrm { B C W S ~ ( 3 0 4 ) } + \mathrm { B C W S ~ ( 3 0 4 ) } + \mathrm { B } + \mathrm { B } .$ $\mathrm { ( 3 0 5 ) + B C W S ~ ( 3 0 6 ) + B C W S ~ ( 3 0 7 ) + B C W S ~ ( 3 0 8 ) + B C W S ~ ( 3 0 9 ) + B C W S ~ ( 3 0 9 ) } = 0 . 0 0 0$ （310）$^ +$ BCWS （ 311 ） $= 9 2 4 9 9 7 . 5 $ 由 于 所 有 工 序 均 处 在 关 键 链 路 上 ， 故$\mathrm { B C W S c { = } B C W S { = } 9 2 4 9 9 7 . 5 } ;$ ；项目已完工作预算（BCWP）：此时工序409已经完成 $100 \%$ ，工序 305 完成 $100 \%$ ，此时实际的工作预算费用 $\mathrm { B C W P = } 7 1 4 0 9 7 . 5$ ；项目进度差异：$\mathrm { S V = B C W P \mathrm { - } B C W S } { = } 7 1 4 0 9 7 . 5 { \mathrm { - } } 9 2 4 9 9 7 . 5 { \mathrm { - } } 2 1 0 9 0 0 { < } 0$ ，表示当前进度滞后；经过计算关键链项目工期滞后约14天，占用项目缓冲时间（40天）其中的14天，超过1/3但是不足2/3；同时非关键链项目工期未滞后。经过调查发现关键链路滞后的原因如下：

关键链路滞后的主要原因有两个，其一为工序305是本项目系统开发的难点模块之一，需要非常熟悉费用模型设计的核心开发人员进行开发，原定负责工序 305的核心开发人员被临时抽到其它任务中，解决其它任务的技术难点，导致项目滞后；另外一个原因为项目初期对接充电桩的硬件设备为2台，但是实际开发过程中确认为需要对接5台充电桩硬件设备，开发需求增加，导致在项目开发过程中增加了充电流程开发数量。

针对关键链项目滞后的原因需要采取措施，由于此时非关键链路已经完成工作，故从非关键借调2名高级工程师，由项目经理为其安排具体工作。

# 第五章 保障措施及效果分析

项目综合管理是从全局的观点出发，以项目整体利益最大化为目标，以项目各专项管理的协调与统一为主要内容的综合性管理过程。它主要应用于工期与成本的平衡，工期与质量的平衡，成本与质量的平衡，进度、成本、质量与资源的平衡。

项目的顺利执行和如期交付是保证企业实现利益最大化的前提，因此企业需要制定相应的项目执行的保障制度、组织原则及管控策略，并严格照此措施执行管控。A公司的项目实施保障措施从组织保障、技术保障、人员保障 3个角度进行如下论述。

# 5.1 组织保障

高效的项目团队是项目成功的关键，企业通过优化组织架构和落实团队考核机制来激励团队的成长，不断提升团队成员知识水平和工作能力，促使项目团队和团队成员共同成长[42]。

# 5.1.1 优化企业组织架构

为了项目的顺利开展实施，以及项目成本、效率的合理划配，项目组的人员配备需要高中低人员的组合，高代表高层次的技术带头人，负责项目的整体规划设计和技术难题的攻关；中代表项目研发的中坚力量，有专业的业务知识和较强的开发技术；低代表项目中的一般工作人员，负责简单功能的开发、测试等工作；除此以外，施行项目经理、研发经理负责制，加强技术管理的有效性和开发过程的科学性、准确性。

为确保项目的成功，由甲乙双方共同成立项目管理组，A公司选派工作能力强且经验丰富的管理人员作为项目总监，组件项目团队[42]，由甲方项目经理统筹管理。该项目的组织结构图如图5.1所示：

![](images/063c614eaaa2a616dcbd99cfc23a195073b0954236d2a130c34ed13a4df3f65a.jpg)  
图5. 1 项目组织结构

基于该组织保障，A公司也加强对项目管理的保障，项目管理保障包含项目管控思路、项目管控内容、项目管控制度的保障，具体管理保障措施内容如下。

项目管控思路。A公司的软件开发项管控采取“联合管控、双重管控”的模式，项目建设的管理团队由项目客户方团队和 A 公司的项目团队共同组建，实现对建设项目的联合、统一管理。项目管理组将作为单一的焦点来管理和监督项目工作的执行，保持良好的内部沟通和管理。

项目管控内容。包括项目进度计划的制定和执行跟踪、控制，在项目启动及执行的全过程中进行项目风险识别和判断，项目架构、部署、业务、接口的设计和开发等。

项目管控制度。项目管控制度的制定和实施直接影响项目执行的成败，A公司建设了包含日站会、周例会、周报告、月报告、里程碑报告和联合项目管理组的的管控制度。

# 5.1.2 落实团队考核机制

为了有效的促进以上组织架构在日常工作中充分发挥其效能，企业在优化组织架构的同时应制定行之有效的团队考核机制，其考核机制梳理如下：

一是将项目经理的管理水平纳入项目管理人员的考核范围内，具体包括项目经理负责的项目整体进度计划执行情况、项目进度执行风险影响和项目计划成本执行情况。这三个考核维度直观的量化评估了项目经理对项目整体的管控水平和在项目执行过程中对资源、需求的调配能力。

二是将项目团队成员的能力提升与项目直接关联，设置考核指标。通常情况下，项目团队成员在项目的执行过程中听从项目经理和各小组长的工作安排，以需求人员和架构人员提供的设计为依据开展项目工作（服从型），这种工作模式非常不利于项目工作的进展和团队成员的能力提升，因此，通过设置考核机制反向推动项目成员参与到项目工作方案制定中，一方面提升了个人的工作能力，不断突破和提升，另一方面也使项目工作多元化、更兼容。

# 5.2 技术保障

现代项目进度管理离不开 IT 技术的支持，通过建设迪科研发项目管理工具，充分利用大数据技术，进行项目归类和不断优化企业项目管理方案，为每一个项目的工作开展提供量化、可行的决策支持。

# 5.2.1 研发项目管理工具平台建设

有效的项目管理方法，可以将项目工期减少 $10 \%$ 甚至以上，并能够更好的节约项目资源，为企业效益做出正向的贡献。A 公司在原有项目管理软件 PDDM 的基础上优化系统功能，开发新的项目进度管理工具迪科研发项目管理系统，系统关于项目进度管理展示视图如图5.2所示。

![](images/5c2cdf3d255e1b3bf95fe2801f2b9d6f80bd08306496e0dea9655f8ddeb99aef.jpg)  
图 5. 2 项目进度视图

![](images/d3f0ed615990c4c5daf6f198bae74c1b8602e624a025042ed40b9b724ea4ec27.jpg)  
图 5. 3 项目管理工作环节

科学合理的项目总体计划和详细计划是项目成功的基石，迪科研发项目管理系统将工作任务以简洁的方式下达给指定人员，任务单、工作日志、周报、月报多维度的呈现出项目执行的真实情况，里程碑进度、责任矩阵、重要任务评价、责任人工作完成率等方式分析进度差异、精确定位责任人便于采取进度纠偏措施。

# 5.2.2 建立难点技术攻坚团队

为确保软件开发项目的整体进度和最终交付，研发质量与进度需要同时得到保证。A公司建立针对每一个项目的技术保障措施，设立难点技术攻坚团队。组件企业技术攻坚团队，该团队由企业的技术骨干人员组成，在各项目出现技术难题并且在项目内部不能解决或解决方案不够优良时，将技术问题交由该团队进行讨论攻坚。该团队定期组织新技术（如大数据、物联网、人工智能、5G、区块链）的学习和研究，一方面作为企业新技术新项目的知识储备，另一方面，通过这部分新技术的学习可以使企业引领该行业的发展，提升企业在行业内竞争力。

因为关键链法和挣值法等项目进度管理方法的引入，对企业项目管理人员（项目主管、项目经理）的管理水平有一定高的要求，项目管理知识的缺失往往会造成项目管理人员在实际项目活动中角色的偏差，很难将项目管理角色与技术骨干或负责人的角色分开，因此公司加强了对管理人员管理技术的培训和技术支持。

# 5.3 人员保障

# 5.3.1 队伍培训及日常管理

项目进度的良好执行需要企业建立完备的管理机制和组织的支持，同时也需要更为专业的人才为企业服务，因此，建设完善的企业团队培训机制和规范管理对企业专业人才的培养十分重要。

（1）建立专业技术队伍培训

定期组织技术培训，进一步深化完善专业人才培养体系，按照软件开发能力素质模型设计各职务的培训课程。确保项目经理每年参加的线上线下课程培训不少于 30个学时；开发人员每年接受面授的培训时长不少于 60 个学时，线上培训或课程学习不少于 60 个学时；需求和设计人员每年接受面授的培训时长不少于 30 个学时，线上培训或课程学习不少于60个学时。

（2）项目管控制度。

项目管理制度关系到项目管控的成败，因此本次项目实施同时必须建设一套完备的项目管理制度，主要包括：1）例会制度：每周五召开项目例会，通报项目进展以及存在的问题，确定下周的计划，项目组全员参与；2）报告制度：项目经理每周发送项目周报，包括甲方项目经理、领导和相关负责人；3）紧急问题会议制度：当项目经理认为某个问题需要紧急解决，项目管理委员会应快速召开会议商讨解决方案；4）进度管控：对项目进度进行联合管控，项目需求方（甲方）项目管理人员和 A 公司管理人员同时确认项目进度。

# 5.3.2 建立项目团队配备和管理原则

A 公司结合项目建设的实际情况与要求，并参考公司以往同体量项目实施经验，确定了人员配备策略，安排熟悉相关领域业务，具有该体量项目开发实施经验的人员，具体情况如下：

各项目人员配备包含角色包含：项目经理、实施经理、研发经理、技术经理和QA 组成。项目经理，负责项目的整体管理；一名架构师负责产品整体架构及方向，一名技术专家负责建设过程中重大问题决策及技术难题的解决。各角色的职责描述如表 5.1：

表5. 1 项目岗位职责表  

<table><tr><td>岗位名称</td><td>岗位职责</td></tr><tr><td>项目管理委员会</td><td>最高层级的领导机构，确定项目总体工作方向及重大问题决策、 审批项目交付成果并提供项目以必要的支持</td></tr><tr><td>项目经理</td><td>负责项目整体管理、重大问题管理、监督项目执行情况 向项目总监做项目汇报 负责项目战略部署、总体进度、人员配备与调度、项目范围控制</td></tr><tr><td>实施经理</td><td>以及与项目客户方汇报沟通 负责项目实施过程的管控，对客户、合作厂商、接口厂商协调</td></tr></table>

<table><tr><td></td><td>负责项目资源分配、进度控制和质量控制、系统培训</td></tr><tr><td>研发经理</td><td>项目研发整体管理、重大问题管理、风险控制、跟踪解决</td></tr><tr><td>技术经理</td><td>负责系统的整体架构、建模、系统流程、骨干培养、技术培训</td></tr><tr><td>QA</td><td>确定项目组应使用的管理标准、执行项目组的质量保证计划、指 导项目组的日程管理工作、检查项目组的活动与工作产品、分析 问题的原因，提出改进建议、优化组织级的QA过程与检查单</td></tr></table>

良好的项目管理需要企业制定相应的管理措施，同时建立高效的项目组织。高效项目组织的建立遵从以下2个原则。

（1）项目经理的选拔，同时兼备技术研发能力和项目管理能力

项目经理是项目运行和项目管理的关键，如何通过调用项目经理的主观能动性来推动项目的进展是项目经理管理能力的最直接展现。因此，公司在为某一项目工作的开展选择项目经理时，需要同时考虑项目经理技术研发能力和项目的管理能力，同时企业也要加强对项目经理的管理和技术能力的培养提升。

（2）项目计划的制定科学、可执行性强

制定科学的项目计划是项目执行过程中实施计划控制的基础和有力支撑。项目启动初期建设完整的项目进度计划，可以使项目执行过程有序进行，通过对项目执行进度的监控，当实际执行进度和计划进度出现偏差时可以指导项目管理人员分析偏差出现的原因，采取相应的控制措施予以纠偏，使剩余项目工作按照进度计划执行，确保项目按期完成。

# 5.4 效果分析

项目进度控制是项目管理三大目标控制之一，对项目的成败起着至关重要的作用。A公司运营管理平台软件升级项目中采用WBS进行项目工作分解，采用三点估算法由进行工作任务历时估算，计算工作运行最可行时间，增加了项目团队对评估工作量进行多次评审，从而获得相对可靠的项目工期。在此工作完成的基础上采用关键链法编制项目进度计划，去除预留给每项工作上的风险应对时间（安全时间），将这部分时间累加再乘以 $50 \%$ ，作为项目缓冲区和输入缓冲区，项目执行过程中通过对缓冲区的监控帮助项目经理及时地掌握项目执行进度与计划进度偏差情况，运用挣值法将项目进度与项目成本相结合，计算项目关键路径和非关键路径 EV，在必要的情况下采取了纠偏措施。

最后，该项目于第130天完成系统终验，在项目预期时间内完成，满足了用户业务需求和工期目标，最终项目组得到了集团公司以及客户的表扬和奖励。

下表展示了项目执行过程中 15 个监测点中的部分监测点数据收集和整理分析及

采取措施的策略，详细参见表 5.2。

表 5. 2 项目执行过程中监控点数据收集分析  

<table><tr><td>监测点</td><td>计划进度成本</td><td>实际执行成本</td><td>EV</td><td>是否采取措施</td></tr><tr><td>003</td><td>33787.5</td><td>32171.25</td><td>滞后0.25天</td><td>香</td></tr><tr><td>201</td><td>87787.5</td><td>95347.5</td><td>提前1.16天</td><td>香</td></tr><tr><td>206</td><td>407647.5</td><td>373987.5</td><td>滞后3.5天</td><td>香</td></tr><tr><td>306</td><td>696897.5</td><td>541737.5</td><td>滞后24天</td><td>是调整资源，</td></tr><tr><td>311</td><td>924997.5</td><td>714097.5</td><td>滞后14天</td><td>香</td></tr><tr><td></td><td></td><td>·····</td><td></td><td></td></tr><tr><td>900</td><td>1217080</td><td>1284962.5</td><td>滞后10.5天</td><td>项目结束</td></tr></table>

运营管理平台软件升级项目最终超出计划进度 10.5 天完成交付，项目缓冲区剩余 29.5 天。A 公司同类软件开发项目平均完工时间平均超出计划的 $6 3 \%$ ，平均完工项目成本平均超出预算的 $45 \%$ ，运营管理平台软件升级项目完工时间超出计划的$8 . 7 9 \%$ ，较同类项目交付工期提升了 $5 4 . 2 1 \%$ ，完工成本超出计划的 $5 . 5 8 \%$ ，较同类项目交付成本节约 $3 9 . 4 2 \%$ ，详细比对参见表5.3。

表 5. 3 A 公司软件开发项目进度管理效果比对  

<table><tr><td>比对项</td><td>以往同类项目</td><td>本项目</td></tr><tr><td>工期滞后</td><td>63%</td><td>8.79%</td></tr><tr><td>成本超支</td><td>45%</td><td>5.58%</td></tr></table>

依据项目执行进度和成本管控结果可知，运营管理平台软件升级项目的进度计划编制、管控、优化调整的科学管控，使得项目在初期延迟的情况下，最终还能够在控制的范围内实现项目的交付，获得了客户的高度评价，提升企业信誉度和软件开发项目的交付能力。同时，通过该项目的实施交付也有效的改进了 A 公司软件开发项目中存在的不足，为公司后续同类项目的进度管理提供参考和指导。

# 第六章 结论

# 6.1 研究结论

本文在阅读大量文献的基础上，采用基于约束理论的关键链法编制项目计划，结合笔者的工作经验和项目计划进行输入缓冲和项目缓冲设计；在传统挣值法理论上进行研究，提出基于关键链法和挣值法相结合的软件项目监控模型，分别计算关键链路和非关键链路以及整个项目的挣值，分析成本与进度的关系，在项目开展过程中进行有效的监控。

本文以 A 公司运营管理平台软件开发项目的项目进度计划管控为研究对象，首先介绍了项目管理、软件项目管理和进度管理的理论；其次结合 A 公司和运营管理平台软件开发项目的进度管理现状，分析在项目计划阶段和项目执行过程中存在的问题；然后针对 A 公司运营管理平台软件升级项目从项目工作的分解、项目工作时间估算、项目计划编制和项目监控及纠偏四个方面进行优化，研究运用工作分解结构、三点估算法和关键链法有效的解决项目前期的进度管理问题，并采用基于关键链法和挣值法的软件项目监控模型，在软件项目实施过程中进行三次监控，分别计算关键链和非关键链的挣值，分析并找出项目进度执行偏差的原因，通过合理的改善措施确保了项目按期保质完成，有力的验证了本文改进的项目进度管理方法的有效性；最后，为顺利实施 A 公司运营管理平台软件升级项目从组织、技术和人员三方面制定保障措施，并在项目初期制定组织原则及项目管控策略。

A公司运营管理平台软件升级项目中采用WBS进行项目工作分解，采用三点估算法由项目内多人进行工作任务工作历时估算，计算工作运行最可行时间，增加了项目团队对评估工作量进行多次评审，从而了解到相对可靠的项目工期。在此工作完成的基础上采用关键链法编制项目进度计划，去除每项工作上的风险应对时间（安全时间），将这部分时间累加再乘以 $50 \%$ ，作为项目缓冲区和输入缓冲区，项目执行过程中通过对缓冲区的监控项目经理及时的掌握了项目执行进度与计划进度偏差情况，运用挣值法将项目进度与项目成本相结合，计算项目关键路径和非关键路径 EV，在必要的情况下采取了纠偏措施。

在运营管理平台软件升级项目中增加里程牌的设置，通过对里程碑的监控，使项目团队准确的了解项目整体工作执行进度和完成质量，项目经理通过对里程碑的监控可以为后续项目工作提前做好准备，这为有效的完成剩余项目工作打好基础。项目计划工期与实际执行工期分析比对如表6.1所示。

表 6. 1 任务计划工期和实际工期比对表  

<table><tr><td></td><td>408 407</td><td>406</td><td></td><td>404 403</td><td>402</td><td>10</td><td>00</td><td>310</td><td>309</td><td>308</td><td>307 306</td><td>305</td><td>0</td><td></td><td>00</td><td>0</td><td>206</td><td></td><td>3000000</td><td>0</td><td>104</td><td></td><td>0</td><td>003</td><td></td><td>100</td></tr><tr><td></td><td>乙</td><td>8</td><td>6</td><td>乙 乙</td><td></td><td>9</td><td>移动客户端（安卓和IOS)</td><td>0</td><td>0</td><td>+ 9 s 乙</td><td>→</td><td>8</td><td>0</td><td>u 0</td><td>9</td><td>充电桩运营管理系统</td><td>乙 8</td><td></td><td>1</td><td>企业服务总线开发和测试</td><td>乙</td><td>乙</td><td>总体架构设计 c</td><td>乙 9</td><td>L c L</td><td>任务名称 计划工时</td></tr><tr><td>乙 5</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td>0.5</td><td></td><td></td><td></td><td>0.5</td><td></td><td>5</td><td></td><td></td><td></td><td>实际工时</td></tr></table>

<table><tr><td>500</td><td>系统初验</td><td>1</td><td>0.5</td></tr><tr><td>600</td><td>配合硬件改造</td><td></td><td></td></tr><tr><td>601</td><td>电桩后台管理WEB</td><td>5</td><td>2.5</td></tr><tr><td>602</td><td>集中监控模拟</td><td>2</td><td>1</td></tr><tr><td>700</td><td>系统业务测试</td><td></td><td></td></tr><tr><td>701</td><td>系统连接测试</td><td>6</td><td>3</td></tr><tr><td>702</td><td>业务确认测试</td><td>2</td><td>1</td></tr><tr><td>800</td><td>系统上线运行</td><td></td><td></td></tr><tr><td>801</td><td>系统上线运行</td><td>3</td><td>1.5</td></tr><tr><td>802</td><td>运行维护</td><td>7</td><td>3.5</td></tr><tr><td>900</td><td>系统终验</td><td>1</td><td>0.5</td></tr></table>

项目原计划工期为119.5天，项目缓冲区40天，项目上线运行后所耗工期为130天，比三点估算法估计的工期 233 天缩短了 103 天。基于对 A 公司运营管理平台软件升级项目的深入研究，证实了关键链法、挣值法能够合理有效的进行成本与进度的同步控制，能够动态实时监控项目进度，及时反馈项目进度与成本执行情况，也能够为其他类似项目的进度管理提供参考依据和理论支撑。

# 6.2 不足与展望

由于本人水平和精力所限，本论文只针对 A 公司现阶段项目管理中存在的问题进行研究。长远来看，这些研究还尚不深入，仍需要对一些存在的问题和如何更灵活的管控项目的进度计划做进一步研究：

（1）动态项目计划的研究

在项目执行过程中，工作任务随时都有可能会发生变化，也就是项目进度计划和对进度计划的管控是一个的过程，还需要进一步的研究如何更好地将这一动态特性与进度计划和进度计划的管控相结合运用。

（2）面向大型企业的多项目同时开展执行的进度管理研究

本论文以 A 公司和运营管理平台软件升级项目为研究对象，研究了企业单一项目在执行中的进度计划和进度计划的控制调优。现实工作中，A 公司以及其他的大型企业同时执行多个项目的工作，对如何针对多项目工作的项目进度计划制定和联合进度管控有实际需求，这还需要做进一步的研究和实践。

（3）新兴技术在项目管理中的应用研究

我国的信息化建设程度已经很高，但大数据、物联网、5G、人工智能等新兴技术在蓬勃发展，A 公司是一家拥有大数据、人工智能、5G 等技术人才的软件公司，如何运用新技术的发展与传统项目进度管理相融合，在项目管理的过程中运用大数据技术实现管控注智和人工智能技术支撑的决策建议，这还需要投入更多的研究。

# 附录

# 关于 A 公司软件开发类项目进度管理的问卷调查

为了了解A公司以及同行业内软件开发项目进度管理的情况，我们通过线上 $^ +$ 线下的问卷调查方式开展本次问卷调查，您所填写的信息用于科研分析，题目选项无对错之分，请根据您的工作经验和真实想法填写，感谢您的合作！

# 一、基本信息

1. 您的性别是

A . 男 B. 女

2. 您的年龄是

A. 24-28 B. 29\~32 C. 33\~36 D. 36 以上

3. 您的学历是

A. 大专及以下 B. 本科 C. 硕士 D. 其他

4. 您的职称是

A. 中级管理职称 B. 高级管理职称 C. 专业机构认证 D. 无

5. 您担任项目经理工作年限是

A. 1-2 年 B. 3-5 年 C. 6-8 年 D. 8 年以上

6. 您担任项目经理角色参与的软件开发项目数量是？

A. 1-3 个 B. 4-7 个 C. 8-10 个 D. 10 个以上

# 二、参与的软件项目进度计划编制情况调查

1. 您参与的项目是否存在进度延期现象（）

A. 是 B. 否

2. 您参与的项目进度滞后或提前完工的比例是：（）进度滞后或提前完工的比例：（实际工期-计划工期）/计划工期进度滞后：

A. $0 . 1 0 \%$ B. $1 1 \% - 2 0 \%$ C. $2 1 \% - 3 0 \%$ D. $3 1 \% 4 0 \%$ E. $41 \%$ 以上

提前完工：F. $0 . 1 0 \%$ G. $1 1 \% - 2 0 \%$ H $1 . 2 1 \% - 3 0 \%$ I. $. 3 1 \% - 4 0 \%$ K. $41 \%$ 以上

三、您所负责的项目在进度计划编制阶段主要存在问题是什么？（在您认为最合适的位置打“√”）

<table><tr><td rowspan="2">进度计划编制存在问题表</td><td colspan="5">完全不同意←→完全同意</td></tr><tr><td>1</td><td>2</td><td>3</td><td>4</td><td>5</td></tr><tr><td>1．项目进度目标设立不明确</td><td></td><td></td><td></td><td></td><td></td></tr></table>

<table><tr><td>2．项目工作分解不彻底、颗粒度太大</td><td></td><td></td><td></td><td></td><td></td></tr><tr><td>3．进度计划由项目经理片面制定，未与各角色负责人充分 沟通</td><td></td><td></td><td></td><td></td><td></td></tr><tr><td>4．项目各工作间的依赖关系考虑不彻底、不清晰</td><td></td><td></td><td></td><td></td><td></td></tr><tr><td>5．对总体计划和项目阶段计划的关系处理不当，不重视或 忽视阶段计划的制定</td><td></td><td></td><td></td><td></td><td></td></tr><tr><td>6．任务和职责划分不够清晰或存在遗漏，未按照任务划分 清晰每个人的责任</td><td></td><td></td><td></td><td></td><td></td></tr><tr><td>7．计划编制过程中未充分识别项目中的技术难点</td><td></td><td></td><td></td><td></td><td></td></tr><tr><td>8．项目中工作量估计不合理</td><td></td><td></td><td></td><td></td><td></td></tr></table>

您认为项目进度计划编制阶段的其他问题：

四、项目实施过程中，存在的问题有哪些？（在您认为最合适的位置打“√”）  

<table><tr><td rowspan="2">项目实施过程中存在问题表</td><td colspan="5">完全不同意←→完全同意</td></tr><tr><td></td><td>12</td><td></td><td>4</td><td>5</td></tr><tr><td>1．需求变更太随机，范围不断变更</td><td></td><td></td><td></td><td></td><td></td></tr><tr><td>2．项目团队缺乏有效沟通</td><td></td><td></td><td></td><td></td><td></td></tr><tr><td>3．人员随意调用</td><td></td><td></td><td></td><td></td><td></td></tr><tr><td>4.项目进度不能准确获取</td><td></td><td></td><td></td><td></td><td></td></tr><tr><td>5．出现项目进度滞后时,无应急预案</td><td></td><td></td><td></td><td></td><td></td></tr></table>

您认为项目实施过程中遇到的其他问题：

# 五、在公司项目进度管理方面，您有哪些建议？

# 参考文献

[1] 赖新辉.基于项目进度规划方法集成化的研究[J].企业科技与发展, 2012,0(14):156-158.  
[2] 张聚礼,谢鹏寿,马咸,张秋余.软件项目管理[M].西安:西安电子科技大学出版社, 2014:4-5.  
[3] 高礼.LTE基站软件升级开发项目的进度管理研究[D].四川:电子科技大学, 2016.  
[4] 刘辉,李金海.基于项目进度规划方法集成化的研究[J].工程经济, 2014,(07):62-68.  
[5] Schwalbe,K.Managing Information Technology Projects.6th.US:Cengage Learning PressPublishing Corporation,2010,P126-133[6] 黄伟.计划评审技术(PERT) [J].系统工程与电子技术, 1979(01):86-88.  
[7] Goldratt, E.M.. Critical Chain.2nd.US: North River Press Publishing Corporation,1997,P79-96[8] 孙荣霞.中外项目管理模式比较研究[D].河南郑州:黄河科技学院, 2011.  
[9] 石倩芸.杭州电信市政府指挥中心信息化项目进度管理的研究[D].浙江:浙江工业大学, 2017.  
[10] 邹娟.媒资项目进度控制优化研究[D].北京:中国科学院大学, 2017.  
[11] 刘勇.建筑智能化工程项目施工进度管理[D].上海:东华大学人文学院, 2017.  
[12] 李晔,谭晓慧,高爱丽.进度-费用联合控制模型在污水处理厂工程中的应用[J].市政技术,2014(05):165-166.  
[13] 李旭东.北京银行信息系统项目进度控制的研究[D].北京:北京理工大学, 2016.  
[14] 项目管理中要认清三个约束条件[J].中国建材资讯, 2007, (04):68-68.  
[15] 余淼.沟通管理在软件研发项目全生命周期中的影响[D].北京:北京邮电大学, 2017.  
[16] 江长春.L 公司 A 系列激光测距仪开发项目进度管理研究[D].南京:南京理工大学, 2017.  
[17] 倪永健.航天 W 电动舵机伺服系统研发项目进度控制研究[D].哈尔滨:哈尔滨工业大学,2017.  
[18] 高海翔.K 省动力环境防护系统建设项目管理研究[D].南京:南京邮电大学, 2019.  
[19] 寿涌毅.关键链项目管理方法综述[J].项目管理技术, 2006,(09):28-31.  
[20] 王雨霖.软件工程项目中的经济学[J].中外企业家, 2013,(08):41-44.  
[21] 余若.创业板一周年大检阅[J].广东科技, 2010,(23):62-68.  
[22] 梁斌.基于项目进度规划方法集成化的研究[J]. 工程经济, 2014,(07):62-68.  
[23] 宋奇瑶.我国轨道交通工程项目的进度管理问题研究[D].苏州:苏州科技大学, 2019.  
[24] 刘建林.景区智慧旅游管理系统设计与实现[D].长沙:湖南大学, 2019.  
[25] 廖翔.电信 OCS 改造项目风险管理研究[D].武汉:华中科技大学, 2012.  
[26] 长青,吉格迪,李长青.项目绩效评价中挣值分析方法的优化研究[J].2006,14(2):65-70.  
[27] 沙珍珍.基于挣值法的环境工程项目审计程序研究[D].青岛:中国海洋大学, 2009.  
[28] 马国丰,尤建新,顾凌赟,李磊.R&D 项目关键链进度计划柔性评价及价值优化[J].科研管

理,2013,34(s):113-124.[29] Goldratt, E.M..The Goal.3rd. US: North River Press Publishing Corporation,1992.P137-168[30] Goldratt, E.M..It's not Luck. US: North River Press Publishing Corporation,1994.P231-239[31] 丁士昭.建设工程项目管理[M].北京：中国建筑工业出版社，2011:109-144.[32] 刘国冬.工程项目组织与管理 [M]. 北京：中国计划出版社，2011:218-256.[33] 罗岳斌,赵英俊,王华.基于改进蚁群算法的关键链项目进度管理研究[J].计算机工程与科学,2014,36(09):1516-1521.[34] Herman Steyn. An investigation into the fundamentals of critical chain projects scheduling [J].International Journal of Project Management, 2000, (6).P363-369.[35] Herman Steyn.Project management applications of the theory of constraints beyond critical chainscheduling [J].International Journal of Project Management, 2002, (20).P75-80 .[36] Wei CC，Liu PH，Tsai YC. Resource-constrained and Project Management Using EnhancedTheory of Constraint [J].International Journal of Project Management, 2002,(20).P561-567.[37] Yeo K T, Ning J H. Integrating Supply Chain and Critical Concepts inEngineer-procure-construct (EPC) Projects [J].International Journal of Project Management,2002, (20).P253-262.[38] 张春生 , 严广乐 . 基于资源时间因子的 DSM 项目群进度优化研究 [J]. 运筹与管理,2013,22(04):93-100.[39] 蔡永勇,桑笑楠,张周磊,徐永红.资源多约束进度网络的风险评估[J].软件,2015,36(07):36-41.[40] 王涛.改进的 PERT 项目工期方差的估算方法 [J].工业工程与管理,2013,17(01):36-41.[41] 赵 金楼 . 基 于 Web 的 海 装 项 目 进 度 监 控 系 统 的 设 计 [J]. 哈 尔 滨 工 程 大 学 学报,2013,34(09):1199-1208.[42] 钟声. D 公司 ZY 平台建设项目进度与质量管理研究[D] .成都:电子科技大学, 2018.[43] 邓斌.基于关键链技术的项目进度管理体系[J].财贸研究，2008,12,25(4). P155-156[44] 马国丰,屠梅曾,史占中等.基于关键链技术的项目进度管理系统设计与实现[J].上海交通大学学报，2004,3,38(3).P377-381.[45] PMI.A guide to the project management body of knowledge(2000)[S].Project managementInstitute, 2000 .[46] 格雷戈里 T·豪根.北京广联达慧中如见技术有限公司译.项目计划与进度管理[M].机械工业出版社,2005.P47.[47] (美)哈罗德·科兹纳著.项目管理:计划、进度和控制的系统方法 (第 11 版) [M].北京：电子工出版社，2014，7.[48] 孙连杰.甘特图与网络技术的应用[J].创新科技. 2004 年 12 期:45.

[49] 宋宇航.基于资源约束和流程优化的笔记本项目进度管理研究[D].上海:上海交通大学, 2018.  
[50] 佛雷姆,J. D. 郭宝柱译. 组织机构中的项目管理[M].北京:世界图文出版社, 2014.  
[51] 郭伊琳.科研项目管理的优化空间及发展趋势研究[J].新技术新工艺. 2016(12) .  
[52] 许志国.软件工程监理过程及方法应用研究[D].北京:北京交通大学, 2010.  
[53] 陈汗龙,黄丁琦,孙庆飞.PERT 在比测试验项目进度管理中的应用[J].中国标准化,2020,(02).P184-188.  
[54] 夏雯钰.坞家 220 千伏输变电工程项目成本控制研究[D].江苏:江苏大学, 2019.

# 致谢

在攻读工商管理硕士期间，经历了很多难忘的人和事，时光艰辛又快乐。在论文将要完成之时，特向曾经帮助过我的老师、同学和家人致以我最诚挚的谢意。

首先感谢我的导师张蔚虹教授，在导师张蔚虹教授的严格要求和反复的耐心指导下完成本文的编写。本文从选题、资料收集到提纲的确定、内容的编写和修改，每个环节都得到了张老师耐心的帮助和指导，张老师以严谨的治学态度、渊博的专业知识、和蔼可亲的待人风格为我树立了学习的榜样，将使我在未来的学习、工作和生活中终身受益。在学业的最后，谨向张蔚虹教授表示我最忠心的感谢！

其次，忠心感谢西电MBA教学中心的所有老师和同学们对我的帮助，使我能够顺利完成学习任务！

再次，感谢我的家人，在我的求学阶段中给予我精神上的鼓励和支持，家人的奉献与关怀鼓励我勇敢向前、努力坚持，最终完成学业，使我在学业方面百尺竿头更进一步。

最后，感谢拨冗审阅本文和答辩委员会的各位专家、老师所付出的辛勤劳动，论文中有不尽人意支持，敬请各位专家、老师批评指正。

# 作者简介

# 1. 基本情况

董婷，女，陕西咸阳人，1990 年 5 月出生，西安电子科技大学经济与管理学院工商管理2017级硕士研究生。

# 2. 教育背景

2008.09\~2011.07 西安航空职业技术学院，大专，专业：软件开发  
2017.09 至今西安电子科技大学，硕士研究生，专业：工商管理

# 3. 攻读硕士学位期间的研究成果

# 3.1发表学术论文

无

# 3.2申请（授权）专利

无

# 3.3参与科研项目及获奖

无

# 3.4参与项目情况

中国电信陕西公司智慧 BSS3.0 系统建设项目：电信与数字化应用深度融合为数字化服务生态系统，中国电信提出转型3.0项目建设要求。项目前期负责与陕西电信相关负责人的接洽，调研、收集相关数据资料；项目建设初期负责与公司项目管理团队依据项目实际情况并结合新、老项目管理经验，制定符合该项目的进度管理方案（项目团队 $2 0 0 +$ 人，项目建设周期为 2018.06-2019.12）。

运营管理平台软件开发项目：项目全称为西安市新能源充电桩运营管理软件升级项目，西安市政府统建，面向企业和个人用户开放运营。担任该项目的项目经理，首次运用关键链与挣值法结合的方式进行项目建设全过程的进度管控。
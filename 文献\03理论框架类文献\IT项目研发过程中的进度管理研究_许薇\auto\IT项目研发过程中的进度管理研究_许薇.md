# IT 项目研发过程中的进度管理研究

许薇（上海交通大学上海 200030）

摘要：介绍了项目进度管理和 项目新产品开发的基本阶段流程 并对 项目研发过程中的进度管理关键影响因素进行分析 只有对进度计划 进度控制和项目沟通管理进行关键控制 严格把关和控制项目工期估算 关键路径和关键资源 集成产品开发体系 加强对供应商项目进度的管理才能保证 企业在如今竞争激烈的全球浪潮中 成功领先市场 立于不败之地 改善企业目前的进度管理瓶颈 推进企业研发的顺利完成关键词： 项目研发 新产品开发过程 进度管理

# 0 引言

当前的中国已快速融入到世界经济的全球化的浪潮中 起步晚的中国 行业也紧追世界步伐 对项目管理理论的研究与实践 是 企业目前急待提高和改进的 调查表明 约 $70 \% \sim 8 0 \%$ 的 项目超期大型项目一般会超期 $20 \% \sim 5 0 \%$ 还有不少项目半途夭折。

由此不难发现 在 项目管理方面 无论是理论研究还是实际应用 都需要 企业提高管理和控制能力 目前 多种经济成分并存的中国 企业或多或少都引进了项目管理。从现代管理学角度来看项目管理（Project Management）的宗旨是在运作方式和管理思维模式上最大限度地利用内外资源 从根本上改善管理人员的工作程序 提高工作效率 企业要想在高度竞争与快速变化的环境下把握商机 提高营运效率以及合理地进行资源配置 项目管理的重要性是不言而喻的。而对于中国的IT 企业来说项目在研发过程中的管理可以说是项目管理最核心的组成部分。

进度管理是项目管理中的重要环节 与项目管理的其他知识领域紧密相关 涉及到项目管理的每一个过程并且贯穿于项目生命周期的任何一个阶段。但是项目进度管理是项目管理中很容易被忽略的一个方面 很多时候项目的半途夭折或是彻底失败就是因为忽略了项目进度管理 所以加强对进度管理的研究成为当务之急。

本文拟对 企业项目实际研发过程中的进度管理展开研究 在对 项目进度管理理论进行深入研究的基础上结合IT 企业公司内部实际情况和IT 项目进度管理方面的工作经验和教训 通过理论与实践的有机结合使 IT 企业在强调“Time-to-Market”（加快产品上市时间）的今天在尽量短的周期内结合有限的人力、物力和财力来达到既定的目标。在最难以控制的不确定因素非常多的开发阶段 找出除了将计划细化和及时地调整 变更之外更多的管理方法和模式来提高效率更好控制的项目进度。

# 1项目进度管理

项目管理按知识领域来细分 可分为范围管理 时间管理、成本管理、质量管理、风险管理、人力资源管理、采购管理、沟通管理、项目整体管理九大知识领域图 项目管理协会 的项目管理知识体系指南 中 没有单独的项目进度管理 主要是项目时间管理 但项目的进度不仅受时间因素影响 还受到资源方面的影响 因此我们在这里使用项目进度管理的说法 主要是强调项目进度中的项目时间管理同时受到资源等方面的影响需要从整体上关注影响项目进度的各个因素

![](images/bf0f81e6d6697a65501d885b33f5168c7103a8369b2d7876342de1d82d802d86.jpg)  
图1 项目管理九大知识领域

进度管理是项目管理的基础 项目管理的起初 也正是从进度管理开始 从图 我们就知道进度管理是企业资源管理 成本管理等的前提

很多企业往往在成本上非常关注 而忽略了进度事实上 因为进度管理的问题 让企业的损失可能更大 进度的延迟 往往带来成本的超支 造成客户的索赔等等 而对于 产业说来 则可以更进一步说时间就是企业的生命 信息技术的生命周期越来越短 产品的更新换代频率越来越快：CPU 的运算速度按照摩尔定律 在不断提升 个人电脑 越做越小 越来越薄 一轮轮的软件浪潮也让不少的消费者应接不暇 在这个信息爆炸的时代 企业更是要强调项目的进度管理。

# 2IT 项目新产品开发基本流程

项目管理按项目过程来分 可分为启动过程 计划过程、执行过程、监控过程、收尾过程等五大项目过程。

项目的普遍特点是技术含量高 目标不精确目标有渐进性 任务边界模糊 针对性强 客户需求变更频繁进度时间紧。这些特点都容易导致项目开发周期延长 费用增加 风险变大 项目新产品研发因为其特殊性 也有基本的阶段流程 就拿某电脑研发公司举例 新产品从开发到上市的阶段流程基本分为五个阶段 定义 计划 设计与开发 投放市场 量产（图2）。

![](images/14ffc2fe1b2f6a01bc15eb7c4baf44f88db831903ab1e3101017f1be69fc6135.jpg)  
图2 某电脑企业新产品开发流程

在这五个主要阶段流程之前的是产品初级计划。

这个阶段包括产品构思与选取 概念与评估 产品构思可以来自于客户 合作伙伴 售后 市场 制造以及研发内部这些来自各个渠道的信息就构成了产品的最原始概念 产品的概念和分析来自于分析市场机会和战略可行性上 主要通过快速收集一些市场和技术信息 使用较低的成本和较短的时间对技术 市场财务 制造 知识产权等方面的可行性进行分析 并且评估市场的规模 市场的潜力 和可能的市场接受度并开始塑造产品概念 这个阶段一般具体项目人员不直接参与而是由专门的市场部门主导项目经理、工程部门 财务 销售部门中层参与决定 所以不被包括在产品的五大阶段内。

# 2∙1 产品定义阶段

这个阶段是产品开发工作的基础阶段 它的主要目的是新产品定义 包括目标市场的定义 产品构思的定义 产品定位战略以及竞争优势的说明 需要明确产品的功能规格以及产品价值的描述等方面内容 决定产品的开发可行性 对前一阶段的估计进行严格的调研。

# 2∙2 产品计划阶段

这个阶段需要落实所有实施项目中未定义项锁定产品本身的定义和功能制定项目进度计划。项目团队完成所有的商业研究 功能部门计划 定下成本目标 预算 质量和供应链目标 当然 这个阶段并不需要详细的产品设计但是需要对这一产品的资源、时间表和资金做出估算 这一阶段涉及的活动比前一阶段要多很多 并且要求多方面的资源和信息投入 所以这一阶段最好是由一个跨职能的团队来处理 也就是最终项目团队的核心成员。

值得指出的是 这个阶段最大的产出就是项目进度计划的制定 这也是本文研究的重点

# 2∙3 新产品设计与开发

这一阶段的重点是按照既定的方案来进行产品的实体开发 大部分具体的设计工作和开发活动都在这一阶段进行 而不再分析产品的机会和可行性了 同时 这一阶段还需要着手测试 生产 市场营销以及支援体系方面的一些工作 包括生产工艺的开发 计划产品的发布以及客户服务体系的建设 根据早期定义的不同 这一阶段的部分工作可以由企业本身完成 也可以由 ODM（ORIGINAL DESIGN MANUFACTURER）原始设计制造商完成

# 2∙4 产品投放市场

这个阶段的工作重点是投放市场的准备和实施这一阶段的活动包括产品的小批量试生产以及市场的试销等 当然 这个阶段仍旧需要更新财务分析报告这一阶段的标志是成功的通过产品测试 完成市场推广计划 以及建立可行的生产和支援体系

# 2∙5 量产

产品的投放市场稳定后 这个阶段的工作主要集中在客户的日常维护 产品的批量生产 质量的持续改进以及产品服务保证的实施。

本文主要讨论的是研发过程中的进度管理研究主要聚焦在产品定义 计划 设计与开发和投放市场阶段这几个不确定因素最多最难以控制的关键阶段 希望通过分析和研究 企业研发过程中产品定义 计划 设计与开发和投放市场阶段的进度管理 改善企业目前的进度管理瓶颈 推进企业研发的顺利按时完成。

# 3IT项目研发过程中的进度管理的关键影响因素分析

年 提出了制约因素理论 主要阐述TOC（TheoryOfConstraints）在制造业中的运用，处在流水线中 决定产出的是该流水线中的某一台机器被称为该项流水线的 瓶颈 为了提高该产出 就必须而且只需提高瓶颈的产出 理论看似浅显 但却是管理理论的一次革命它使人们更注重影响项目的主要因素 更注重从整体全面的看待一个项目 因此今天在这里探讨影响 项目进度管理的三大关键影响因素：进度计划、进度控制、沟通管理。

# 3∙1 进度计划

项目进度计划是项目管理人员在产品计划阶段根据工作的持续时间和工作之间的逻辑关系制定的工作计划 项目进度计划不仅需要制定工作包的进度计划 而且进度计划制定者需要明确项目交付物 定义工作包 估算工期 资源有效性的管理 成本预算 整合进度计划和预算、定义关键绩效指示器或里程碑、明确关键成功因素。

# 3∙1∙1项目工期估算

好的进度计划是成功的一半 而不合实际的进度计划会给项目带来巨大的负面影响。估算项目工期作为项目进度计划中的一个重要环节 不仅会影响其他计划环节定义上的正确性和精确性 同时也会受到其他环节定义的影响。

当面对一个新项目的时候 需要认真分析 分析这个项目与已执行项目的共性 以及其独有的个性 一般说来 任何项目的时间周期都是有可以参照的标准的 将项目与历史上的项目进行对比后 结合项目的自身的特点 包括项目的范围和可利用资源状况等 是可以得出一个相对来说比较准确的项目估算工期的

工期估计还要客观地综合考虑IT 企业员工的工作效率 工作的复杂度和历史经验 使工期的估计更合理更接近现实情况 项目管理人员单凭主观地估计是不可能得出好的结果容易导致项目处于混乱状态。

估计项目开发工期的一个最经典的方法是：根据所有相关的信息 分别估计出乐观工期 $T _ { o }$ 悲观工期$\left( T _ { p } \right)$ 和最可能的工期（ $T _ { m } )$ 然后利用公式期望工时 $T _ { e }$ $= ( T _ { o } + 4 T _ { m } + T _ { p } ) / 6$ 得出基准计划的时间

# 3∙1∙2关键路径和关键资源

优化系统进度计划的有效方法是关键路径法项目是由各个任务构成的 每个任务都有一个最早 最迟的开始时间和结束时间 如果一个任务的最早和最迟时间相同则表示其为关键任务一系列不同任务链条上的关键任务链接成为项目的关键路径 关键路径是整个项目的主要矛盾 是确保项目能否按时完成的关键。

在进度计划安排中是否优先保证了项目关键路径上的资源 是否通过人员技能矩阵对项目关键资源进行分析和安排 在任务安排过程中是否对关键资源进行了保护（尽量减少关键资源上非关键任务的安排）。

另外 我们在进度计划安排上应该适当安排 $10 \%$ ${ \sim } 1 5 \%$ 的余量这样在项目遇到突发事件或项目风险转变为实际问题时候才能够有人员和时间进行处理

# 3∙1∙3集成产品开发体系

众所周知中国IT 企业中以制造为主的硬件设备厂商产品往往需要整合多个供应商的模块在研发中建立密切的关联。一个项目往往是由若干个相对独立的任务链条组成的。例如一款开发新PC（个人电脑）产品的项目就需要有应用软件 机箱 主板等不同的子项目系统 各链条之间的协作配合就直接关系到整个项目的进度。这里可以用到著名的“木桶理论”即进度最慢的项目就会是整个项目进度的代表。利用系统、网络化的管理方法可以优化整个项目的进度计划 因此 对项目进行进度管理 都应采用系统的观点。

# 3∙2 进度控制

在项目执行和控制过程中要对项目进度进行跟踪和控制。对项目进度其实有两种不同的表示方法一种是纯粹的时间表示 对照计划中的时间进度来检查是否在规定的时间内完成了计划的任务 另一种是以工作量来表示的 在计划中对整个项目的工作内容预先做出估算 在跟踪实际进度时看实际的工作量完成情况而不是单纯看时间即使某些项目活动有拖延 但如果实际完成的工作量不少于计划的工作量 那么也认为是正常的 在项目进度管理中 往往这两种方法是配合使用的 同时跟踪时间进度和工作量进度这两项指标 在掌握了实际进度及其与计划进度的偏差情况后 就可以对项目将来的实际完成时间做出预测。

# 3∙2∙1里程碑事件

前面已经提到 任何一个项目都是由若干个相对独立的任务链组成的 只有在任何一条链都已经优化的基础上 才可能进行系统的优化 因此 保证每条任务链的效率是整个项目进度优化的前提和基础。通常可以采用设置“里程碑事件”的方法来保证单独任务链的最优。

所谓“里程碑事件”往往是一个时间要求为零的任务 就是说它并非是一个要实实在在完成的任务 而是一个标志性的事件 例如在电脑开发项目中的 整机测试 测试 是一个子任务 撰写整机测试报告 也是一个子任务 但 整机测试报告通过审核 可能就不能成为一个实实在在需要完成的子任务了但在制定计划以及跟踪计划的时候往往加上“整机测试报告通过审核 这一个子任务 但工期往往设置为 工作日 目的就在于检查这个时间点 这是 整机测试 整个任务的结束的标志。

一般人在工作时都有前松后紧的习惯 而里程碑强制规定在某段时间做什么 从而合理分配工作 细化管理粒度 对复杂的 开发项目而言 每一阶段的进度都需要逐步逼近目标 里程碑产出的中间 交付物就是每一步逼近的结果 也是控制的对象 如果没有里程碑 中间想知道 现在进度做的怎么样了 是很困难的。

在项目管理进度跟踪的过程中 给予里程碑事件足够的重视 往往可以起到事半功倍的效用 只要能保证里程碑事件的按时完成 整个项目的进度也就有了保障。

# 3∙2∙2 加强对供应商项目进度的管理

目前随着 市场竞争越来越激烈 成本控制也成为IT 企业的一个发展大方向于是纷纷外包给 ODM。随着这种 企业的发展趋势 多数 项目会按照企业和供应商的不同合作关系分为四类

外部模式 在这种模式下 企业不再参与任何后期研发与量产工作 而是由 直接全权负责。IT 企业仅仅在定义初期告诉 ODM 企业需要怎样的产品 符合怎样的市场要求和技术需要 在研发期间 企业更多起的是监督作用

平衡模式 企业和 共同合作研发完成产品。（3）联合模式。大部分研发由IT 企业自己完成小部分由ODM 完成。（4）内部模式。这就是由IT 企业自己全权完成一个项目从头至尾的工作。

其实无论是哪种模式都需要企业与各供应商的项目进度统一 由此保证企业本身的项目进度 目前的现状是大多数 企业对企业内部的项目团队有较强的管理 而很难保证原始设计制造商项目进度 这就需要企业在与供应商谈判时就强化他们的进度意识将项目的进度作为验收标准写进合同或作为附件与合同具有同等效用 同时明确违约责任 在项目的研发进行过程中 还需要建立起一个机制 保证供应商与企业内团队的沟通协调确保进度的一致性。

# 3∙3项目沟通管理

由于制定进度计划的工具 主要是甘特图和网络图 包括 即关键路径法 计划评审技术所以很多人一想到进度管理就是绘制甘特图或网络图 而忽视了影响项目进度的其他软技巧 项目沟通管理就是其中一条。

对于进度的延误和超前完成 项目成员都要及时有效的沟通 项目是由项目团队成员共同协作完成的 因此高效项目团队也是项目成功的关键之一除了通常的一些制度管理外 建立良好的沟通 增强团队的凝聚力是提高团队效率的有效途径 良好的沟通是协调各方面的润滑剂。确定项目成员的沟通需求找到合适的沟通方式 进行及时有效的沟通 尤其在项目出现问题后要及时展开沟通 丰富沟通的渠道和层次。

# 4 结语

本文结合企业实践 以 项目管理体系进度管理理论为依据 对 项目研发过程中的进度管理关键影响因素进行分析 企业只有对进度计划 进度控制和项目沟通管理关键控制 才能保证 企业在如今竞争激烈的全球浪潮中 成功领先市场 立于不败之地。

# 参考文献

许江林 项目管理最佳历程 北京 电子工业出版  
张静文 胡信布 王茉琴 关键链项目计划调度方法研究 科技管理研究2008（3）  
［3］ Joseph Phillips．实用IT 项目管理［M］．冯博琴等译．北京：机械工业出版社  
［4］ 窦燕．影响软件项目管理关键因素的探讨［J］．燕山大学学报2004(4):369—372.  
［5］ 许成绩林政．现代项目管理教程 ［ M ］．北京：中国宇航出版社,2003.  
［6］ 项目管理协会．项目管理知识体系指南［M］．卢有杰王勇译．3版 北京 电子工业出版社
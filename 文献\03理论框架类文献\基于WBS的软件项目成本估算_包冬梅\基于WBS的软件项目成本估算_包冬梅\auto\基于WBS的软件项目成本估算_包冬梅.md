# 基于WBS 的软件项目成本估算

包冬梅

摘要：由于软件商品的无形性、无损耗性和易复制性,软件商品的价格不易确定，软件项目的成本不易被估算。软件价格估算的准确度直接影响项目的盈利。本文以“超市管理系统"为例,重点介绍基于WBS 的成本估算方法,以此提高成本估算能力。

关键词：WBS;成本估算;软件成本 中图分类号：F275.3作者单位：呼伦贝尔学院计算机学院

# 文献标识码：A

# 一、引言

随着信息技术的突飞猛进，软件产业的规模也越来越大，软件的价格越来越高,因此软件成本的估算受到相关人员的重视。软件成本管理的目的是确保在批准的预算范围内完成项目所需要的各项任务。软件价格的估算不是一门精确的学科,它受到许多因素的影响,包括技术影响、环境影响和变更的程度等等。因此,软件项目如果要开发成功，关键在于要使用科学准确的软件成本估算方法。

# 二、关于软件项目的成本估算方式

估算软件项目的成本是对开发新的软件项目进行总的成本统计和工作量预测的过程，能够计划和预估整个软件项目将花费的资金。估算软件项目的成本可以通过多种途径，主要的估算方式有以下几种：

1.专家鉴别法：专家鉴别法主要是运用于软件的开发前期，是依靠领域里面相关专家的经验，对软件的开发成本估算后，从而实施打分。相关专家对软件估算得出多个数值，再

H1、H1、H2、H3、H4、H5，本文采用多元回归分析方法计算了工会实践在预测劳资关系氛围和企业绩效，以及劳资关系氛围在预测企业绩效时的回归系数和显著性，证明工会实践四个维度中,参与、教育及维护职能对企业绩效的影响相对显著，且前两者更为突出，这与我国工会职能定位相吻合，其主要是参与企业管理和协助政府建设，并兼顾员工维权与企业发展。

4.结构方程的构建。上述回归分析限定在各变量相互关系分析，未进行模型整体变量间的相互作用，因此本文采用机构方程模型，以AMOS7.0软件进行有效验证，模型路径图如图2所示,其中，以椭圆形表示潜在变量，以矩形表示标识变量，单项箭头线条表示因果关系。潜在变量以椭圆形来表示，标识变量则以矩形来表示，单项箭头线条表示因果关系。

各构面测量指标为：

$\textcircled{1}$ 工会实践：包括维护(α1)、参与(α2)、教育(α3)和建设 $( \alpha 4 )$ ;

$\textcircled{2}$ 积极劳资关系氛围：包括和谐性(α5)、开放性(α6)、迅速性(α7);

$\textcircled{3}$ 企业绩效：组织可见绩效( $\propto 8 \AA$ 和市场可见绩效 $\left( \alpha 9 \right)$ ;

$\textcircled{4}$ 消极劳资关系氛围:包括敌意型(α10)和冷漠型(α11)。

5.结构方程模型的检验。文章对H6和H7进行假设检验，其中介效应前提得到了满足，模型具有合理性及路径显著性;同时，对模型(部分中介和完全中介)进行了比较分析，采用 SEM的中介模型技术进行了编程和计算，得出积极劳资关系氛围及消极劳资关系氛围为中介变量的三种模型的拟想办法将多个值综合成最后的一个估算值。该方式的优点在于预测速度快、成本花费低，而缺点在于得出的估算值不准确,与实际相差较大。

2.比较类推法：比较类推方法在评估同历史项目的应用范围和环境方面类似度较高的项目中运用较多，开发的新软件通过和历史项目进行比较后,推算出大致估算值。它具有简单易行、花费少的优势，但具有一定的局限性，评估结果准确性差。

3.从顶至下：从顶至下进行估算指的是从整体开始，对整个软件项目估算，运用曾经的项目完成工作经验,对项目的总费用和总工作量进行计算和预测，从而根据一定比例发放到局部的组成中。自顶向下估算路线虽然简单，但估算精度较差,通常在项目的初期或信息不足时进行。

4.从底至顶；从底至顶的估算过程指的是逐渐细化有待开发的项目软件，细化到具体的工作量，由专门的负责人对工作量进行估计，给出估算值，把局部工作量统加为总工作

![](images/53f33eb174d64dad502191278c07aca2a9b942bd9997245a3285415dd8dbc9d4.jpg)  
图2结构方程模型图

合度指标。

6.结论分析。通过上述模型的比较及拟合度检验，可以得出H1、H2、H3、H4、H5 几个假设变量之间均呈现显著相关性,相应的路径系数显著性均达到了显著水平( $\cdot \mathrm { { p } < 0 . 0 1 }$ ，且在全模型中,仅考量工会实践对企业绩效的直接影响时，两者的路径系数为0.415,而当增加积极的或是消极的劳资关系氛围为中介路径时，则工会实践与企业绩效的路径系数相应减小到0.287、0.311,t值变化分别为2.067、2.753，路径的减弱是显著的,由此表明积极或是消极的劳资关系氛围在工会实践和企业绩效间均起到部分中介作用。

# 参考文献：

[1]陈培培.企业工会实践对劳动关系氛围影响的实证研究[D].长沙：湖南大学,2014.

[2]吴海艳.企业劳动关系氛围的理论与实证研究[D].天津：南开大学,2011.

量。该方法所得的结果比较精确，并且项目所设计的活动资源的数量更清楚,缺点在于估算工作量大。

5.算法模型：算法模型成本估算主要采用经验公式来预测软件项目计划所需的规模、工作量和进度,进而估算开发所需的成本，它为决策者提供指导。没有任何一种模型能完全反映软件组织及项目的特点、实际开发环境和相关人员的因素，因而使用模型估算成本时要谨慎。

# 三、WBS-工作分解结构

软件规模是软件成本的主要因素，是成本估算的基础，软件规模的估计开始于软件的分解。WBS中文即工作分解结构，是通过规定的原则和方法来分解一个软件项目，层层分解，分解至无法分解，先把项目分解变成任务，任务往下细化为工作,再往下把一个个具体工作派发给工作人员的普通生活中。最后的结构是细化成“可交付成果”,该组织确定了项目的整个范围。

不管用怎样的方式对软件项目实施估算，都应建立在具体掌握了工作量的基础上,最佳的方法是采用WBS-工作分解结构。不同的工作分解将得到不同的项目成本，如果工作分解不合理将使得工作效率无法提高，项目的运营成本居高不下。项目的开发费用受软件的难度影响，同时项目管理方法及人员的能力也直接影响开发成本的高低。

WBS的原则性分解，对一个主体性的软件项目分步骤细化，原则上必须要把项目分解至无法继续分解,细化至把一个个具体工作派发给每一位工作者的生活中执行。每个工作要具体把人员以及资金、时间安排好。WBS 的每一项都只有一个人负责，WBS必须与工作任务的实际执行过程一致。

# 四、基于WBS的成本预测

项目的成本预测是基于WBS分解的基础，借助WBS 的分解方法对一个抽象不具体的软件项目分步骤合理细分，细化至把具体工作时间、工作量派发给每一位执行,在运用WBS后估算成本。“超市管理系统"是一个典型的在运用WBS 的基础上进行成本估算的软件项目，具有很高的探究价值。

“超市管理系统"的需求：用户提到的需求比较抽象，通常给出该系统需要完成的目标。这种项目目标并不能帮助完成成本的预估和系统开发工作。首要任务是要做好WBS分解,接着确定好工作包,最后才能实施成本的估算工作。在软件项目中,对需求做好详细分析是必不可少的,要编写需求规格说明书，该说明书中将详细地描述实现该软件系统的所有功能需求。例如：销售信息管理功能,在需求规格说明书中将描述出该功能模块完成的功能；销售信息管理所包括的商品名称、类型、数量、出售价格、销售人员等相关信息。

描述好实现该软件系统所有功能需求只能代表我们弄清了要完成的任务，而最重要的任务是要在需求规格说明书确定后深入分析怎么完成任务的问题。如何通过软件系统实现文字上的描述，如何对功能模块进行准确设计，然后转变为系统管理体系结构图。根据用户需求得出的超市管理系统结构图,如图1所示。

WBS的分解要素包括功能模块和功能点这两个子系统。WBS分解奠定于需求规格说明书、系统结构图这两种基础上。按照以前对项目的分解经验，可以对功能模块的具体工作量做出估算，在此基础上计算出系统里所包含的总的工作量。首先，可以根据项目的特征对超市管理系统进行自上而下的、WBS的第一层分解。具体的分解流程包括五个阶段,即需求分析阶段、结构设计阶段、编码阶段、测试阶段和安装与维护阶段。第一层的分解结构如表1所示。

![](images/069d55078a9bf3997c2f8bc792133df939a26b77ca1be6837236a106d6c7d2a7.jpg)

![](images/6d411873da6b304fd88ff090b35fe2947b5fa1ab63e1a0977605d4c9f4db19fb.jpg)  
图1超市管理系统层次体系结构图

表1有关超市管理系统的第一层WBS分解图  

<table><tr><td>任务名称</td><td>工期</td><td>开始时间</td><td>完成时间</td><td>前置任务</td><td>资源名称</td></tr><tr><td>超市管理系统</td><td>60工作日</td><td>2015年8月31日</td><td>2015年11月20日</td><td></td><td></td></tr><tr><td>需求分析阶段</td><td>9工作日</td><td>2015年8月31日</td><td>2015年9月10日</td><td></td><td></td></tr><tr><td>田结构设计阶段</td><td>29工作日</td><td>2015年9月11日</td><td>2015年10月21日4</td><td></td><td></td></tr><tr><td>编码阶段</td><td>15工作日</td><td>2015年10月22日</td><td>2015年11月11日14</td><td></td><td></td></tr><tr><td>测试阶段</td><td>5工作日</td><td>2015年11月12日</td><td>2015年11月18日20</td><td></td><td></td></tr><tr><td>田安装与维护阶段</td><td>2工作日</td><td>2015年11月19日</td><td>2015年11月20日23</td><td></td><td></td></tr></table>

通过第一层的WBS分解，已经把系统的功能设计方面细化到子系统的具体范畴中，每一个子系统又可以细分，细分至功能模块，继而细分成特定的功能点。如果我们从第一层分解继续细化,依据以往项目经验数据很容易估算出每一个功能模块的工作量。第二层WBS分解如表2所示。

表2超市管理系统第二层WBS分解图  

<table><tr><td>任务名称</td><td>工期</td><td>开始时间</td><td>完成时间</td><td>前置任务</td><td>资源名称</td></tr><tr><td>超市管理系统</td><td>60工作日</td><td>2015年8月31日</td><td>2015年11月20日</td><td></td><td></td></tr><tr><td>需求分析阶段</td><td>9工作日</td><td>2015年8月31日</td><td>2015年9月10日</td><td></td><td></td></tr><tr><td>编写需求规格说明书</td><td>5工作日</td><td>2015年8月31日</td><td>2015年9月4日</td><td></td><td>分析人员</td></tr><tr><td>编写数据字典</td><td>4工作日</td><td>2015年9月7日</td><td>2015年9月10日3</td><td></td><td>分析人员</td></tr><tr><td>结构设计阶段</td><td>29工作日</td><td>2015年9月11日</td><td>2015年10月21日4</td><td></td><td></td></tr><tr><td>数据库设计</td><td>6工作日</td><td>2015年9月11日</td><td>2015年9月18日</td><td></td><td>分析人员</td></tr><tr><td>系统体系结构设计</td><td>6工作日</td><td>2015年9月21日</td><td>2015年9月28日6</td><td></td><td>分析人员</td></tr><tr><td>系统详细设计</td><td>15工作日</td><td>2015年9月29日</td><td>2015年10月19日7</td><td></td><td></td></tr><tr><td>数据库设计</td><td>3工作日</td><td>2015年9月29日</td><td>2015年10月1日</td><td></td><td>分析人员</td></tr><tr><td>人事管理模块设计</td><td>3工作日</td><td>2015年10月2日</td><td>2015年10月6日9</td><td></td><td>分析人员</td></tr><tr><td>销售管理模块设计</td><td>3工作日</td><td>2015年10月7日</td><td>2015年10月9日10</td><td></td><td>分析人员</td></tr><tr><td>进货管理模块设计</td><td>3工作日</td><td>2015年10月12日</td><td>2015年10月14日11</td><td></td><td>分析人员</td></tr><tr><td>库存管理模块设计</td><td>3工作日</td><td>2015年10月15日</td><td>2015年10月19日12</td><td></td><td>分析人员</td></tr><tr><td>界面美工设计</td><td>2工作日</td><td>2015年10月20日</td><td>2015年10月21日13</td><td></td><td>开发人员</td></tr><tr><td>编码阶段</td><td>15工作日</td><td>2015年10月22日</td><td>2015年11月11日14</td><td></td><td></td></tr><tr><td>数据库实现</td><td>3工作日</td><td>2015年10月22日</td><td>2015年10月26日</td><td></td><td>开发人员</td></tr><tr><td>人事管理模块的实现</td><td>3工作日</td><td>2015年10月27日</td><td>2015年10月29日16</td><td></td><td>开发人员</td></tr><tr><td>销售管理模块的实现</td><td>3工作日</td><td>2015年10月30日</td><td>2015年11月3日17</td><td></td><td>开发人员</td></tr><tr><td>进货管理模块的实现</td><td>3工作日</td><td>2015年11月4日</td><td>2015年11月6日18</td><td></td><td>开发人员</td></tr><tr><td>库存管理模块的实现</td><td>3工作日</td><td>2015年11月9日</td><td>2015年11月11日19</td><td></td><td>开发人员</td></tr><tr><td>测试阶段</td><td>5工作日</td><td>2015年11月12日</td><td>2015年11月18日20</td><td></td><td></td></tr><tr><td>单元测试</td><td>3工作日</td><td>2015年11月12日</td><td>2015年11月16日</td><td></td><td>测试人员</td></tr><tr><td>系统测试</td><td>2工作日</td><td>2015年11月17日</td><td>2015年11月18日22</td><td></td><td>测试人员</td></tr><tr><td>安装与维护阶段</td><td>2工作日</td><td>2015年11月19日</td><td>2015年11月20日23</td><td></td><td></td></tr><tr><td>编写使用说明文档</td><td>2工作日</td><td>2015年11月19日</td><td>2015年11月20日</td><td></td><td>培训人员</td></tr><tr><td>安装与培训</td><td>2工作日</td><td>2015年11月19日</td><td>2015年11月20日</td><td></td><td>培训人员</td></tr></table>

通过WBS分解，得到工作量信息、进度日期和人员分配等信息。

从上面的分解图不难发现,开发这个系统总共需要60个工作日，开发该系统的人员如果支出每月4000元的成本，那么可以计算出该项目成本预算至少为8000元。

# 五、结语

软件项目规模估算是软件项目成本估算的基础，规模估算应基于WBS工作分解。正确和合理地分解任务能提高成本估算值，让估算更加准确。

# 参考文献：

[1]覃征,徐文华,韩颖,唐晶.软件项目管理(第2版)[M].北京：清华大学出版社,2009.

[2]姚列健,吴勇,张社朝.软件项目如何进行成本估算]舰船防化，2009(2)：52-57.
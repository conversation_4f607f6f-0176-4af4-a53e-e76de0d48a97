@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo 开始处理PDF文件...
echo.

:: 递归处理所有PDF文件
for /r %%f in (*.pdf) do (
    set "pdf_file=%%f"
    set "pdf_name=%%~nf"
    set "pdf_dir=%%~dpf"
    
    :: 构建输出目录路径（与PDF文件同名的目录）
    set "output_dir=!pdf_dir!!pdf_name!"
    
    :: 检查是否已存在同名目录
    if exist "!output_dir!" (
        echo [跳过] !pdf_name! - 目录已存在，可能已处理过
    ) else (
        echo [处理] 正在处理: !pdf_name!
        
        :: 创建输出目录
        mkdir "!output_dir!" 2>nul
        
        :: 执行mineru命令
        mineru -p "!pdf_file!" -o "!output_dir!"
        
        if !errorlevel! equ 0 (
            echo [成功] !pdf_name! 处理完成
        ) else (
            echo [错误] !pdf_name! 处理失败
            :: 如果处理失败，删除创建的空目录
            rmdir "!output_dir!" 2>nul
        )
        echo.
    )
)

echo.
echo 所有PDF文件处理完成！
pause

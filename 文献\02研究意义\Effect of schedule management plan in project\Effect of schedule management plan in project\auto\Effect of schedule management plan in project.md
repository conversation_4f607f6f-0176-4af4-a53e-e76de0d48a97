# ENGINEERING SCIENCES

# Effect of schedule management plan in project management worth using structural equation modelling

D. SURESH & SIVAKUMAR ANNAMALAI

Abstract: With the growing digitalization and the need for customers to have more personalized services available, there is a constant need to innovate and fulfil the demands of customers. However, this burden has contributed to further worsening the schedule management for the service companies. Customers now are demanding more personalized service but without having any compromise on the quality and time of delivery. This resulted in creating the need to have a schedule-driven management plan designed. Despite this advantage, there are very limited studies available that even focus on the concept whereas not any study works towards understanding its contribution in affecting project management for service companies. To overcome this, the study aims to assess the perception of 200 employees and 10 managers for understanding a schedule management plan and its impact on project management worth. The employee’s perception evaluation via structural equation modelling revealed that there is no direct influence of schedule management on project management worth but via different factors and strategies the influence could be derived. Managers’ perceptions validated the findings and provided insight that strategies like the building of a cloud-based platform or predictive modelling should be designed for better schedule management plan development.

Key words: Project management worth, schedule-driven management plan, service delivery organization, structural equation modelling.

# INTRODUCTION

The services sector is the engine of growth for the Indian economy. Employing one-fourth of the population, the service sector has emerged as the largest and fastest-expanding sector of the Indian economy. The Economic Survey 2021–22, presented by Finance Minister <PERSON><PERSON><PERSON>, noted that the Services Industry contributed over $50 \%$ to India’s GDP (Gross Domestic Product) (PIB Delhi 2022). Despite this growth, today service companies are facing several challenges such as overstaffing or understaffing, and a lack of teamwork among the employees of the company. These problems in a way results in reducing the productivity of employees which not only affects the timely completion of projects but also hamper the overall effectiveness of the project (Pandey et al. 2021, Gaur 2022, Kaasinen et al. 2022).

In today’s corporate environment, where a growing proportion of workers work remotely rather than in the office, project management effectiveness has become an immense need for services companies that place a high priority on providing outstanding client value are increasingly needed (Vadhanasin et al. 2016, Kerzner 2018). These companies may be able to stay on top of things and keep meeting the organization’s milestones and goals because of effective project management. Project management effectiveness is needed to enable service project leaders to remain adaptable, employ the appropriate technology, follow trends, and build pertinent technical and interpersonal skills (Patanakul 2015).

Among the different management elements, schedule management plays a vital role in project management (Heagney 2022). By removing uncertainty, scheduling puts everyone on the same page and helps the project move quickly toward its goal (Suresh & Sivakumar 2019a). A service company must be certain of the resources, including materials, budgets, labour, and equipment, that they will require to complete a project. A thorough project schedule assists in ensuring that the organization hasn’t missed any essential tasks and that it is aware of everything necessary to finish the project on schedule (Villafáñez et al. 2020, Jiang et al. 2018). Hence through these activities schedule management plays a vital role in project management.

The empirical review of existing studies defined that often in the service sector, there is a scope of delay in delivery due to mismanagement and red-tapism, thus, there is a requirement to manage the tasks promptly so that not the final project can be delivered in time but also all activities can be coordinated (Nor Haizan Nor et al. 2021, Gaur 2022). However, despite having the major role of schedules in controlling the workflow of service companies, there has been less focus on assessing the schedule-driven management contribution. Limited authors (Suresh & Sivakumar 2021, Gibbison 2016) have evaluated the need for schedule management and the factors affecting it. As the service sector mainly relies on the timely delivery of products to customers and their feedback, thus, scheduling is extremely important for the sector (Larco et al. 2018). But still, an absence of ample studies on exploring the role of scheduling in project management for the service sector has been a major hindrance in improving the efficiency of the sector. Moreover, existing literature lacks knowledge on how to maintain project schedules in project management. The aforementioned points need thorough analysis to make service companies proficient in dealing with the project successfully. The information about schedule management could help in regulating the functioning and having more coordinated projects (Jiang et al. 2018). Therefore, this study aims to fill these gaps in the existing literature by examining the effect of a schedule management plan in project management working for service companies in India.

The existing studies have focused on exploring the contribution of schedule management and its role in project management. But in the existing world, with the presence of multiple internal and external aspects, there is no direct association present between the aspects. Even the successful execution of the plan and its fulfilment also affect the strategies adopted by companies and other external or internal factors. This study delves deeper into including the influence of other relevant concepts of the service companies like the digitalized world have usage of agile or modern technology or the other project and human-related factors. This study not only explores the direction contribution of schedule management but also supports the role of other important aspects for a service-based company. Thus inclusion of mediating variables which support the functioning of service companies and also affect their project management helps in having better information about schedule management roles. This mediating variable inclusion and assessment is the major contribution of the study.

To meet this aim, the work is formulated in the following manner: Initially, the theoretical information about the existing studies would be discussed by stating the schedule management plan, its impact and empirical review of existing studies. Next, the methodological section states the study design, participants, and participants’ information. Following it is the presentation of results and their interpretation. finally, the study findings, conclusion, recommendations, limitations, and scope of future studies are stated.

# REVIEW OF THE LITERATURE

The schedule management plan (SMP) is a process that calls for the creation of guidelines and records for upholding, creating, managing, and controlling the time and resource schedules needed to complete the project. It is a document that outlines the development, supervision, and management of a project’s schedule (Bourne & Weaver 2018, Nor Haizan Nor et al. 2021). With a schedule-driven management plan, companies can see how much money they have allocated for the project as well as when and where they should spend it. Additionally, schedule management plans help service companies manage and allocate their resources (Nor Haizan Nor et al. 2021, Suresh & Sivakumar 2019b). Hence, using a schedule management plan can be proven a huge benefit for service industries but the schedule management plan should be designed as per the needs of the company.

# Procedure of building schedule management plan

A well-thought-out, successful procedure yields a high-quality project schedule. A project schedule is thus created during the planning stage of any project. The ten steps required to build schedule management are shown in Figure 1.

1) Determine the tasks: It is important to refer to the tasks listed in the job breakdown structure to include tasks in the schedule.   
2) Identify the connections between the tasks: This stage identifies the tasks that must be finished before starting any additional tasks. The tasks that can be completed while performing other tasks should also be included (Li et al. 2017).   
3) Give each task to a particular employee: It is important to allocate the appropriate individual to each task.   
4) Calculate the effort needed for each task: A skill level should be used as the basis for the estimation. Then, the estimation assumptions should be documented.   
5) Think about the additional factors: When creating the schedule, factors like project constraints, project hazards, and staff training time should be taken into account (Banihashemi et al. 2017).   
6) Include a time buffer in the schedule for contingencies: A contingency is a specific provision for unanticipated factors that can delay a schedule.

![](images/293bbf31d02f6cf2c40954b49810d87307c843bcc92f42ab806cceef6c9f556d.jpg)  
Figure 1. Procedure for developing a schedule management plan.

7) Determine the critical path for the project:The activities with the least scheduling flexibility should be analyzed with the help of the critical path technique and then project duration should be forecasted based on the activities that are along the ”critical path” (Kerzner 2019). 8) Check to discover whether the staff is over-allocated: If there is an excess of staff, the staffing plan needs to be changed to give the team the appropriate amount of work. 9) Repeat the steps: as the creation of the project, schedule is an iterative process, steps 3 and 5-8 should be repeated until a baseline is done.

10) Insert the scheduling data into a Gantt chart: Finally, to demonstrate the precise timeline of the project, the tasks,relationships, milestones, staff members assigned, work estimates, and duration should all be represented in a Gantt chart.

Hence following the above-mentioned step service industries can develop a project schedule (Tereso et al. 2019).

# Schedule management plan role for service companies

Today service companies are facing several schedule management-related problems such as difficulty in building organizational availability, balancing the shifts of employees, budgeting for the scheduled shifts, scheduling for all hard-to-fit shifts, and avoiding all employee and manager burnout (Nor Haizan Nor et al. 2021). Schedule management helps eliminate these problems by creating an option of bringing employees and managers together to decide on a schedule considering the project delivery requirements and the ability of employees so that neither party feel burned out (Larco et al. 2018). Often as the companies are handling multiple projects and with service companies the need is to have a personal look over each project so that the quality-based delivery of each project is maintained. As juggling the projects and employees’ availability is difficult, thus, schedule management would help in eliminating last-minute absences and callouts by having a schedule of employee availability. This will also help in providing employees with the shifts desired by them which results in creating a positive attitude and delivery of excelled customer service (Nor Haizan Nor et al. 2021). With service companies mostly the functioning is $2 4 ^ { \star } 7$ in two or three shifts. Some of the shifts are easy to fill but shifts like early morning or back-to-back night shifts are difficult to fill. Non-availability of schedule could result in having no show of employees and even high turnover. Even the hourly rate of employees exceeds the budget for the project causing a loss for the company. Thus, there is a requirement to manage the shifts and the designing of the plan so that the employees for each shift are available (Gaur 2022). Hence, a schedule management plan is an important strategy that not only helps in supporting the service company by timely delivery of services but also contributes towards deriving employee satisfaction and reduction of project cost.

# Theoretical framework for assessing project management

In this research, the work is proposed considering the triple constraint model (Wyngaard et al. 2012) which defines that for project management the main elements to be considered are the scope, time, and budget of the project. This model defines the key element wherein the time aspect is denoted by schedule management while the scope and budget by the additional factors which influence the project management. The factor identification for project management is based on the aspects defined by Amoah et al. (2021) i.e. human-related factors, project procedures, project-related factors, project management factors, and external environment factors. (Papke-Shields & Boyer-Wright 2017) defined the strategies which affect the service companies’ project management. Using the model and the dimensions defined by the researchers, the focus points of the study are defined i.e. schedule management, project management, factors and strategies.

# Impact of schedule management on project management

The act of precisely describing a project and creating a timeline for when and how it will be completed is known as scheduling in project management. The management of the timetable is crucial to project management (Heagney 2022). Team members are given visibility into the work they are expected to do by the construction of a project schedule, which contains a timeline with specified completion deadlines for each task. This keeps everyone involved on track. By removing uncertainty, scheduling puts everyone on the same page and helps the project move quickly toward its goal (Suresh & Sivakumar 2019a). Projects are completed on schedule and within budget when employees can switch between tasks quickly and hand off their work to the next team member as needed. Companies can detect potential issues and make plans for them before they derail the project by creating a strong timetable in advance (Jiang et al. 2018). A service company must be certain of the resources, including materials, budgets, labour, and equipment, that they will require to complete a project. A thorough project schedule assists in ensuring that the organization hasn’t missed any essential tasks and that it is aware of everything necessary to finish the project on schedule (Villafáñez et al. 2020). Hence schedule management plays a vital role in project management.

Several factors affect the linkage between schedule management and project management. These factors can be categorized into five major groups (Amoah et al. 2021). All the factors are shown in Figure 2.

![](images/cabd0b29164ed93bea9b366b3e878f8e50612f2b24904aedd4d284a597896326.jpg)  
Figure 2. Factors affecting the linkage between schedule management and project management.

i. Human-related factors (HRF): These elements comprise the project’s participants, such as the project manager, the client, the consultants, the supplier, the contractor, the manufacturer, and the subcontractor. The effectiveness of a project’s management depends on the commitment of these participants (Ong & Bahar 2019).

ii. Project procedures (PP): PP can be characterized as a set of actions that must be completed throughout a project. If well planned, they can save project costs and make the project easier to complete, which will have an effect on how the project is managed (Sahib Mohammed et al. 2017).   
iii. Project management factors (PMF): PMF includes tools (Gantt charts, work breakdown structures) for managing project activities. Correct utilization of these tools can positively impact the successful management of the desired project.   
iv. Project-related factors (PRF): The PRF can be characterized as the elements connected to the very nature and other features of a project to be conducted, such as the project’s size, kind, scope, level of complexity, and risks related to the project. These elements offer a very clear path for managing the project successfully. For instance, a well-managed project without cost overruns can be achieved with an accurate estimation of the project budget (Sahib Mohammed et al. 2017).   
v. External environment factors (EEF): EEF stands for all outside factors (political, social, economic, and technological) on a project’s progress, most of which are beyond the project manager’s control. At every stage of a project’s life cycle, the external environment affects how it is handled. In some circumstances, EEF is so strong that it even can end a project during its execution phase (Jitpaiboon et al. 2019).

# Strategies adopted by service companies

Effective project management strategies are crucial to any company’s success. This is especially true for services companies where it might be difficult to maintain track of hundreds of employees and projects. Service companies have used the agile approach, modern communication tools, and entrepreneurship as three strategies to expedite project management and remain competitive (Papke-Shields & Boyer-Wright 2017).

Agile methodology: This methodology emphasizes working in small bursts while continuously testing, evaluating, and adapting. Many times, the finished project is frequently not exactly what was intended. Due to the agile methodology’s emphasis on speed, the finished project can be released sooner. However, it then undergoes more testing and revisions to produce a fully error-free project (Bergmann & Karwowski 2019).

Modern technologies for communication: For the majority of projects, communication is a major deal-breaker. It includes a set communication channel and other things. Service companies use a variety of channels to connect during the project. The two most used platforms for project communication are Slack and Microsoft Teams (Crespo 2018).

Entrepreneurship: Companies are giving employees greater freedom than ever to take charge of their area of the company. In many companies, one employee who sees an opportunity might form their team and pursue it independently. Bringing in more creative individuals increases the company’s flexibility and innovation. Employees can carry out their goals more effectively when they aren’t burdened with bureaucracy. Many service companies have strategies in place to motivate staff members to take on new responsibilities. These applications are essential for ensuring that tasks are completed (Spychalsk a-Wojtkiewicz 2018).

Hence with these proper strategies, service companies complete a project most effectively.

# Existing literature contribution in the field

An empirical assessment of studies that examined the effect of schedule management on project management and used some analysis was done to review the studies.

A study by Gibbison (2016) worked toward understanding the mediating role of time management between adult attention deficit and project managers’ effectiveness. With the assessment of the 104 active employees using factor analysis, correlation, and the mediating factor examination using linear regression, the study revealed that time management helps in positively affecting the operational project management effectiveness and even the aspect serves as a partial mediating factor in linking adult attention deficit impact on operational project management effectiveness.

Nasir et al. (2016) assessed the project performance of the construction industry in Malaysia to understand its linkage with time management. Herein, based on the existing research, the study revealed that the quality of time management for the projects is generally poor. Even the prevalence of existing delays in the project creates the need to examine the time management role but the presence of a lack of the factors to track or examine poor time performance resulted in having limited knowledge availability on its relationship with project performance.

Suresh & Sivakumar (2019a) aimed to determine the influence of schedule management planning on the efficiency of project management. Data on the opinions of 208 employees regarding the value of schedule management planning in their project management strategy are gathered using a closed-ended questionnaire. The investigation demonstrates that elements influencing the schedule management plan do have a considerable and advantageous impact on the efficiency of project management.

Mohamud & Samson (2020) focused on assessing the project constraints which affect the successful implementation of construction projects in Isiolo County, Kenya. To determine the impact the examination of 103 government-funded construction projects has been done using regression analysis and the analysis reveals that scope management, time management, quality management, and cost management are the major constraints borne by the companies for having successful project implementation. Among the existing problems time management is the major factor with the highest role and thus, there is a need for strategies for managing the schedules in the project to have efficient completion of work.

Suresh & Sivakumar (2021) the study examined schedule management’s role in project management effectiveness using structural equation modelling. By assessing the perception of 189 employees from IT (information technology) -based companies using the SEM (structural equation modelling) model, the analysis revealed that the schedule management plan works towards successfully and effectively delivering IT projects by having chronological task performance, controlling management issues, reducing wastage of resources, and building coordination and communication.

A study by Nor Haizan Nor et al. (2021) was undertaken to provide the existing methods for scheduling software projects in Malaysia. An exploratory study was conducted to document the software project scheduling experiences of ICT (information and communication technology) officers from different industries in Malaysia. As a result, a survey form was created to solicit feedback from these officers. The results of this study showed that current scheduling procedures for software projects and scheduling issues had an impact on how well projects were developed.

Gaur (2022) aimed to convey the significance of project planning and scheduling, project planners, and project controls in the Indian construction industry. Quantitative data analysis was used. 50 participants, 50 participants were taken into consideration for the questionnaire survey, primarily from the infrastructure and heavy civil construction industries, were taken into consideration for the questionnaire survey. The results showed that planning is a key activity in overseeing any kind of building project. The project planning and scheduling aid in incorporating the entire contract scope into a schedule that benefits the project by cutting down on time, cost, and risk.

Hence, the studies revealed that schedule management is an important component for managing the performance of projects. Though the relevance of time is not just limited to a particular sector or an industry but still studies are absent and the factors to examine the schedule management role. Thus, the need is to overcome the existing research problem and have more exploration of the relationship so that strategies relevant in today’s world for timely delivery of quality-based services could be adapted to support the companies to hold their competitive position in the market.

# Proposal of Conceptual Framework

The study aimed to describe the effect of a schedule management plan on project management. Thus, the figures presented in the Appendix show the benefits of schedule management, procedures to develop schedule management, and factors affecting schedule management. The figure highlights that the procedure to develop schedule management could be stated in 10 important steps and there are several factors like resource accessibility, project complexity, task dependencies, team experience, deadline, project priority, and material availability that affect the schedule management plan. The figure also reveals that scheduling plays a vital role in project management by keeping everyone on the same page, detecting potential issues that may derail the project, and maintaining the project deadline. Several factors like human-related factors, project procedures, project management factors, project-related factors, and external environmental factors affect the linkage between scheduling and project management.

# MATERIALS AND METHODS

The main methods of study, data collection procedure, and analysis procedure are discussed in the section.

# Data Collection Procedure

To understand the schedule management model used by the organization, the study’s target population is made up of individuals who work for service sector organizations in India and have at least two years of experience working. However, as the implementation aspect of the model is more known to the senior authorities thus, middle- or senior-level managers working in service industries are targetted population for qualitative study.

For the sample size computation, Cochran’s formula (shown in Equation 1) is used and the value is computed at a $5 \%$ level of significance i.e.

$$
\begin{array} { r } { \pmb { n } _ { \mathrm { q u a n t i t a t i v e } } = \frac { Z ^ { 2 } p ( \ k _ { 1 } - p ) } { e ^ { 2 } } } \end{array}
$$

$$
n _ { \mathrm { q u a n t i t a t i v e } } = \ { \frac { 1 . 9 6 ^ { 2 } \star 0 . 8 5 \star ( 1 - 0 . 8 5 ) } { 0 . 0 5 ^ { 2 } } } = 1 9 5 . 9 2 1 6 \approx 1 9 6
$$

Wherein

??quantitative– sample size   
Z: confidence interval at $9 5 \%$ (1.96)   
p: proportion of people with study attribute $8 5 \%$ or 0.85)   
e: margin of error $5 \%$ or 0.05)

The sample size derived from the formula is 196 but for approving the value, the 200 respondents would be considered. The qualitative analysis will consist of $5 \%$ of the quantitative sample size.

$$
n _ { _ { \mathrm { q u a l i t a t i v e } } } = 0 . 0 5 \star 2 0 0 = 1 0
$$

In this study, data on the impact of schedule management plans on project management were collected using a sample size of 200 respondents for the quantitative analysis and 10 respondents for the qualitative study.

Simple random sampling was used in this research to get quantitative data (Muili et al. 2019). This approach was chosen because the approach helps in the collection of population data without the inclusion of any biasness. Purposive sampling was used for the qualitative data (Campbell et al. 2020). This approach was chosen since it made it easy for the researcher to reach the sample population.

The quantitative data survey was separated into three sections:

 Section 1: Demographic. The demographic section works towards the collection of data on demographic characteristics like details about Age, gender, education, working mode, and monthly income were all included in the demographic section of the survey. A total of 6 questions are included.  Section 2: Background. Questions about the general knowledge of employees and supervisors regarding scheduling in project management made up the broad background. The questions are on the meaning of schedule management, role in a service company, benefits of schedule management, or problems of service companies. A total of 5 questions are added.  Section 3: Inferential. This section focused on examining the procedure of implementation of the schedule management model and the effect of the schedule management model. So, herein questions are such that respondents have to answer the questions on a Likert scale which ranges from strongly disagree to strongly agree. A total of 20 statements are included.

Google Forms were used to distribute the created and designed questionnaire to the respondents online.

The next step was to create a structured open-ended questionnaire to gather qualitative data. There were three parts to it.

 Section 1: Demographic. The section focused on collecting middle- or senior-level managers demographic information i.e. age, gender, and year of experience.   
 Section 2: Procedure of implementation of schedule management model. The section has 3 questions to identify procedures adopted by service companies for implementing the model   
 Section 3: Effect of implementation of schedule management model. The section with the inclusion of 4 questions focuses on identifying the schedule management contribution towards the company.

All the questionnaires developed were based on the review of existing literature.

# Data Analysis Procedure

AMOS (Analysis of Moment Structures) software is used to create the SEM model for the analysis of quantitative data (Zaid et al. 2020). It has been used to examine the structural connections between elements like project management and schedule management plans. Thematic analysis is carried out manually while analyzing qualitative data. A qualitative data analysis technique called thematic analysis examines the recurring themes in the data. The flowchart for the analysis is presented in Figure 3.

The study’s credibility is examined using data validity and reliability. The Cronbach’s alpha test was used in this study to evaluate the questionnaire’s validity since it assures that all the components were internally consistent and that they measured similar constructs of the situation as a whole (Bujang et al. 2018). The general formula for the computation of the Cronbach alpha value is shown in Equation 2.

$$
\alpha = \frac { N . c } { v + ( N - 1 ) c }
$$

Wherein,

?? : Cronbach alpha   
N: the item number   
?? : average covariance for item pairs   
?? : average variance

CFA (Confirmatory factor analysis) is a specific type of factor analysis that determines how much variety there is in the outcome. To comprehend the variability or any measurement error, the indices of AVE, and CR were thus collected using the CFA analysis approach. The AVE is computed by the factors loadings square sum divided by unobserved latent variable items i.e. shown in Equation 3.

$$
\mathsf { A V E } = \ \frac { \sum _ { i = 1 } ^ { n } \lambda _ { i } ^ { 2 } } { n }
$$

![](images/95e9ee07ae6b993d1f184ffee0ef1fa6dba0163ff7f2fe40ca058a6b638e2da8.jpg)  
Figure 3. Methodology flowchart.

Wherein $\lambda _ { i }$ denote the standardized factor loading.

However, the CR is the factor variance divided by composite total variance i.e. presented in Equation $4$ .

$$
\mathsf { C R } = \frac { \left( \sum _ { i = 1 } ^ { p } \lambda _ { i } \right) ^ { 2 } } { \left( \sum _ { i = 1 } ^ { p } \lambda _ { i } \right) ^ { 2 } + \sum _ { i } ^ { p } V ( \delta ) }
$$

With $\lambda _ { i }$ is the standardized factor loadings, p is the indicators number and $V ( \delta )$ as error term variance for the ith indicator.

The value for Cronbach alpha is computed using SPSS (Statistical Package for Social Sciences) while for AVE and CR based on the factor loadings derived with AMOS, the value is computed. As a result, the study’s conclusions would be reliable and valid (Sürücü & Maslakci 2020).

# Ethical Considerations

Since the current study involves gathering data from employees of Indian service sector organizations, it was made sure that all of the participant’s personal information, including email addresses, contact information, and professional information, was protected. To prevent any harm from being done to the participants, confidentiality is a crucial measure to take (Surmiak 2018). Additionally, it was made sure that all the data was stored on a device that required a password to access it.

# RESULTS

# Quantitative analysis

Herein, as the purpose is to build a linkage between schedule management practices and project management, thus, the SEM model is developed with the inclusion of strategies and factors affecting the linkage.

# Demographic analysis

As demographics help in understanding the suitability of selected respondents for the study, thus herein examination of the demographics is done using frequency analysis.

Figure 4 shows that most of the employees included in the study are from the young and middle age group, educated and experienced and could provide meaningful information about schedule management practices.

# Background analysis

It is essential to include only those respondents who are aware of the concept, thus, herein background analysis is done using frequency analysis.

Figure 5 shows that about $32 \%$ of the employees state that schedule management refers to the process that calls for the creation of guidelines needed to complete the project while $2 7 \%$ of them consider it as a procedure for the completion of the project. About the role of schedule management in service companies, $2 4 \%$ of employees mentioned it as a means of improving omnichannel marketing, and $2 1 \%$ stated its contribution to evaluating project progress.

Discussing the main challenge in the implementation of schedule management, Figure 6 depict that $23 \%$ of employees consider lack of coordination, $1 5 \%$ believe team conflict as a challenge, $1 5 \%$ accepted inadequate skills of team members as a problem, and $1 1 \%$ mentioned about lack of accountability. About the benefits of schedule management, most of the respondents $( 2 3 \% )$ mentioned its contribution to resolving teamwork issues, $2 1 \%$ about the management of the workforce, $1 5 \%$ stated tracking and monitoring the progress of a project as a benefit, and $12 \%$ discussed allowing work to be prioritized as the contribution. Lastly, the examination of the problems borne by service companies due to the absence of schedule management reveals that $32 \%$ of employees mentioned decreased performance level as a problem, and $2 1 \%$ about the inability to manage complex projects. Hence, the analysis reveals that the selected respondents were aware of the organization, its problems, and the contribution to schedule management, thus, they could be considered for analysis to understand the impact of schedule management on project management.

![](images/776eb19c91388b439e2f4f53ed0d9275fd2a6d7864a6642c0279f78487b68940.jpg)  
Figure 4. Demographic analysis.

![](images/12de0650c58e41a3d2c855aadbb5206d82ae4c41052f0feaab6fd5753a76fd99.jpg)  
Figure 5. Background analysis.

![](images/d2f67c294a0b97fa2f838817864cbba7518fde9f5687499f5ab2c01282fa504f.jpg)  
Figure 6. Background analysis.

# Inferential analysis

The evaluation of the respondent’s basic knowledge and characteristics reveals that the employees selected for the study are aware of schedule management and suitable for understanding its contribution to project management. As the statements considered for measuring each of the variables are lengthy, thus, coding for the variables is done to present in the simplified form in Table I.

The Table I shows that each of the variables is denoted by the initial of the variable along with the numeric word i.e. schedule management is coded from SM1 to SM7, strategies are coded as S1 to S3, factors from F1 to F5, and remaining project management as stated as PM1 to PM5.

Based on the coded statements, the linkage between the variables could be represented by the SEM model as shown in Figure 7.

The syntax for the model is stated as

Project management (PM) $\equiv$ Schedule Management Factors (SM) $^ +$ Strategies Adopted by Service Companies (??) $^ +$ Factors affecting the linkage between schedule management and project management (??)

Strategies Adopted by Servic Companies $( S ) =$ Schedule Management Factors (SM)

Factors affecting the linkage between schedule management and project management (??) $\equiv$ Schedule Management Factors (SM)

Wherein,

$$
\mathsf { P M } = \mathsf { P M } \mathsf { 1 } + \mathsf { P M } \mathsf { 2 } + \mathsf { P M } \mathsf { 3 } + \mathsf { + } \mathsf { P M } \mathsf { 4 } + \mathsf { P M } \mathsf { 5 }
$$

$$
S M = S M 1 + S M 2 + S M 3 + S M 4 + S M 5 + S M 6 + S M 7
$$

$$
S = S 1 + S 2 + S 3
$$

$$
F = F 1 + F 2 + F 3 + F 4 + F 5
$$

Before the examination of the relationship between variables and making deductions about the variable’s impact, there is a need to understand the efficiency of the model and determine whether the selected constructs are relevant or not. For fulfilling the requirements, internal consistency, convergent validity, composite reliability, and discriminant validity will be examined. The results of the reliability and validity are shown in Table II.

Table I. Coding for the schedule management, strategies, factors, and project management.   

<table><tr><td> Statements</td><td>Code</td><td> Statements</td><td>Code</td></tr><tr><td>Schedule Management Factors</td><td></td><td>Factors affecting the linkage between schedule</td><td></td></tr><tr><td>We always assign the appropriate resources to the project.</td><td>SM1</td><td>management and project management The effectiveness of a project&#x27;s management depends on the commitment to</td><td>F1</td></tr><tr><td>We surely incorporate difficult tasks into our schedule.</td><td>SM2</td><td>human-related factors. Project procedures must be completed throughout a project.</td><td>F2</td></tr><tr><td>We always determine the order of the tasks by their dependencies.</td><td>SM3</td><td>Gantt charts and work breakdown structures help in managing project activities.</td><td>F3</td></tr><tr><td>We account for any inadequacies or lack of experience on the project team in our project schedule.</td><td>SM4</td><td>Project-related factors offer a very clear path for managing the project successfully.</td><td>F4</td></tr><tr><td>We always focus on completing the task before the deadline.</td><td>SM5</td><td>Most external environmental factors are beyond the project manager&#x27;s control.</td><td>F5</td></tr><tr><td>We accord every project with high priority to gain the resources needed for the project.</td><td>SM6</td><td colspan="2"> Project Management</td></tr><tr><td>We always plan the project schedule according to the material availability.</td><td>SM7</td><td>All the deliveries could be timely attained</td><td>PM1</td></tr><tr><td colspan="2"> Strategies Adopted by Service Companies</td><td>The efficiency of the employees improved</td><td>PM2</td></tr><tr><td>We have adopted an agile methodology to produce a fully error-free project.</td><td>S1</td><td>There are more opportunities for integrating recent developments in the functioning</td><td>PM3</td></tr><tr><td>We are using Slack and Microsoft Teams to connect during the project.</td><td>S2</td><td>Advanced technologies could be implemented</td><td>PM4</td></tr><tr><td>We are giving our employees greater freedom than ever to take charge of their area of the company.</td><td>S3</td><td>The scope of delay is reduced</td><td>PM5</td></tr></table>

![](images/1ab75cedbdabc78105399373a2c17766337442fccb80651bc40577e40f6d198c.jpg)  
Figure 7. Original model for representing the linkage between schedule management, strategies, factors, and project management.

Internal consistency represents the consistency of the results i.e. how the factors are linked with other factors. The above table shows that the value of Cronbach alpha for schedule management is 0.93, strategies are 0.90, factors are 0.94, and project management is 0.83 which are all greater than 0.7. Thus, there is the existence of internal consistency in the model. Composite reliability on the other hand explains how well the statements included can measure the construct. Herein, the value of composite reliability depicts that for schedule management is 0.93, strategies are 0.72, factors are 0.81, and project management is 0.77 which are all greater than 0.7 thus, there is the presence of composite reliability in the model. Further, convergent validity explains the correlation of the variables in measuring a construct. As the value of AVE for schedule management is $0 . 6 4 ,$ strategies are 0.87, factors are 0.88, and project management is 0.68 which is more than 0.5, thus, there is the existence of convergent validity in the model. Lastly, discriminant validity defines the ability to distinguish between the constructs. The results of the correlation between constructs and the square root of AVE are shown in Table III.

Table II. Reliability and validity.   

<table><tr><td>Variables</td><td>Cronbach alpha</td><td>CR</td><td>AVE</td></tr><tr><td>Schedule Management</td><td>0.93</td><td>0.93</td><td>0.64</td></tr><tr><td>Strategies</td><td>0.90</td><td>0.72</td><td>0.87</td></tr><tr><td>Factors</td><td>0.94</td><td>0.81</td><td>0.88</td></tr><tr><td>Project Management</td><td>0.83</td><td>0.77</td><td>0.68</td></tr></table>

Table III. Discriminant validity.   

<table><tr><td></td><td>Schedule Management</td><td> Strategies</td><td>Factors</td><td> Project Management</td></tr><tr><td>Schedule Management</td><td>0.80</td><td></td><td></td><td></td></tr><tr><td>Strategies</td><td>0.64</td><td>0.93</td><td></td><td></td></tr><tr><td>Factors</td><td>0.63</td><td>0.84</td><td>0.94</td><td></td></tr><tr><td>Project Management</td><td>0.60</td><td>0.77</td><td>0.77</td><td>0.82</td></tr></table>

The table III defines that the value of the square root of AVE is 0.80 for schedule management which is more than the correlation of the variable with other variables i.e. strategies $\left( 0 . 6 4 \right)$ , factors (0.63), and project management (0.60). Similarly, for the strategies too the value of the AVE square root is 0.93 which is more than $0 . 6 4$ , $0 . 8 4$ , and 0.77. For factors, the value is $0 . 9 4$ which is more than 0.63, $0 . 8 4 ,$ and 0.77 and for project management, the square root of AVE is 0.82 which is more than 0.60, 0.77, and 0.77. As the requirement for discriminant validity is fulfilled, thus, the model has the presence of discriminant validity too.

The model fitness values were examined for the original model. Herein, for the absolute fitness index, the values of GFI and AGFI are less than 0.90, RMSEA is more than 0.10, and CMIN/Df is more than 5 thus, the measures for the absolute fitness index are away from the required criteria. For incremental fitness, the values of TLI, IFI, NFI, and CFI are less than 0.90, thus, there is also the absence of incremental fitness in the model. Lastly, for the parsimonious fitness measures the value of PGFI, PNFI, and PCFI are more than or equal to 0.5 showing the existence of parsimonious fitness in the model. Thus, the model is only parsimoniously fit. As the fitness criteria for the model are not satisfied, thus, there is a requirement for modification in the model. To improve the fitness of the model, the linkage between the error terms is developed. The linkage states the existence of covariance between variables. With this, the new model is formulated. The fitness index values for the modified model are presented in Table IV.

Table IV shows that the absolute fitness measures have a GFI value of $0 . 8 4$ and an AGFI value is 0.75 which though are not more than 0.9 but close to the measure. Further, the value of RMSEA is 0.10 which is equal to the required level while for CMIN/Df the value is 3.07 which is less than 5. Thus, the model is absolutely fit. For the incremental fitness measure, the value of TLI is 0.92, IFI is $0 . 9 4 _ { i }$ , NFI is 0.91, and CFI is 0.94 which are all more than 0.9 thus there is the existence of incremental fitness in the model. Lastly, the value of PGFI is 0.55, PNFI is 0.67, and PCFI is 0.69 which are all more than 0.50, thus, the model is parsimoniously fit. Hence, the analysis reveals that the modified model is absolutely, incrementally, and parsimoniously fit and could be used for impact examination.

Table IV. Model fitness with modified mode.   

<table><tr><td> Measure</td><td> Index</td><td>Values</td><td> Required value</td></tr><tr><td rowspan="4">Absolute Fitness</td><td>GFI (Goodness of Fit)</td><td>0.84</td><td>&gt; 0.90</td></tr><tr><td>AGFI (Adjusted Goodness of Fit)</td><td>0.75</td><td>&gt; 0.90</td></tr><tr><td> RMSEA (Root Mean Square Error of Approximation)</td><td>0.10</td><td>&lt; 0.10</td></tr><tr><td>CMIN/Df</td><td>3.07</td><td>&lt;5</td></tr><tr><td rowspan="4">Incremental Fitness</td><td>TLI(Tucker Lewis Index)</td><td>0.92</td><td>&gt; 0.90</td></tr><tr><td>IFl (Incremental Fitness Index)</td><td>0.94</td><td>&gt; 0.90</td></tr><tr><td>NFI (Normal Fit Index)</td><td>0.91</td><td>&gt; 0.90</td></tr><tr><td>CFI (Comparative Fit Index)</td><td>0.94</td><td>&gt; 0.90</td></tr><tr><td rowspan="3">Parsimonious Fitness</td><td>PGFI (Parsimony Goodness of Fit Index)</td><td>0.55</td><td>&gt;0.50</td></tr><tr><td>PNFI (Parsimony Normed Fit Index)</td><td>0.67</td><td>&gt;0.50</td></tr><tr><td>PCFI (Parsimony Comparative Fit Index)</td><td>0.69</td><td>&gt;0.50</td></tr></table>

The R square value for the project management is 0.972 showing that about $9 7 \%$ of the variation in the project management is represented by selected independent variables i.e. scheduled management, strategies and factors.

# Impact of factors on the schedule management practices of service companies.

The hypothesis for impact assessment is

$\mathsf { H } _ { 0 1 }$ : There is no impact of factors on the schedule management practices of service companies

Table V shows that the P-value of all the statements is 0.00 which is less than 0.05 and even the T-value is more than 1.96, thus, the null hypothesis of having no impact of factors on the schedule management practices of service companies is rejected. Herein, the estimated value denotes the incorporation of difficult tasks into the schedule (SM2), accounts for any inadequacies or lack of experience on the project team (SM4), assigns the appropriate resource to the project (SM1), and accords with every project with high priority to gain the resources needed (SM6) as the key factors determining the schedule management practices in service companies.

Table V. Impact of factors on the schedule management practices of service companies.   

<table><tr><td>Independent Variable</td><td> Dependent Variable</td><td>Coefficient (Beta values)</td><td>S.E.</td><td>Tvalues</td><td>P</td></tr><tr><td>SM7</td><td>SM</td><td>1.00</td><td></td><td></td><td>0.00</td></tr><tr><td>SM6</td><td>SM</td><td>1.15</td><td>0.08</td><td>13.77</td><td>0.00</td></tr><tr><td>SM5</td><td>SM</td><td>0.98</td><td>0.12</td><td>8.00</td><td>0.00</td></tr><tr><td>SM4</td><td>SM</td><td>1.30</td><td>0.12</td><td>11.26</td><td>0.00</td></tr><tr><td>SM3</td><td>SM</td><td>1.17</td><td>0.13</td><td>8.95</td><td>0.00</td></tr><tr><td>SM2</td><td>SM</td><td>1.47</td><td>0.13</td><td>11.75</td><td>0.00</td></tr><tr><td>SM1</td><td>SM</td><td>1.22</td><td>0.12</td><td>10.06</td><td>0.00</td></tr></table>

# Role of factors in affecting the linkage between schedule management and project management in service companies

The hypothesis for examining the impact is $\mathsf { H } _ { 0 2 }$ : There is no role of schedule management in affecting the factors influencing the linkage $\mathsf { H } _ { \mathsf { o } 3 }$ : There is no impact of factors on the project management in service companies

Table VI. Mediating impact of factors on the linkage between schedule management and project management in service companies.   

<table><tr><td>Independent Variable</td><td>Dependent Variable</td><td>Coefficient (Beta values)</td><td>S.E.</td><td>T values</td><td>P</td></tr><tr><td>F</td><td>SM</td><td>0.69</td><td>0.10</td><td>6.72</td><td>0.00</td></tr><tr><td>PM</td><td>F</td><td>0.23</td><td>0.07</td><td>3.51</td><td>0.00</td></tr></table>

Table VI shows that the $\mathsf { p }$ -value of the impact of schedule management on the factor is 0.00 which is less than 0.05. Thus, the null hypothesis of having no impact of schedule management on factors is rejected. Even the $\mathsf { p }$ -value for the impact of factors on project management is $0 . 0 0 < 0 . 0 5$ , thus, the hypothesis of having no impact of factors on project management is rejected. The estimated value represents that with a $1 \%$ rise in schedule management, the contribution of the factors increases by $0 . 6 9 \%$ while for $1 \%$ rise in factors results in increasing project management by $0 . 2 3 \%$ . Thus, factors serve as a mediating factor in linking schedule management and project management.

# Role of strategies in the successful implementation of the schedule management plan and enhancing the worth of project management

The hypothesis for examining strategies mediating role are

$\mathsf { H } _ { \mathsf { o } 4 }$ : There is no role of schedule management in affecting the factors influencing the linkage $\mathsf { H } _ { \mathsf { o } 5 }$ : There is no impact of factors on the project management in service companies Table VII shows that the $\mathsf { p }$ -value of the variables is assessed. Herein, for the linkage between schedule management and strategies, the value is $0 . 0 0 < 0 . 0 5$ , thus, the null hypothesis of having no impact of schedule management on strategies is rejected. Further, for the linkage between strategies and project management, the $\mathsf { p }$ -value is $0 . 0 3 < 0 . 0 5$ , thus, the null hypothesis of having no impact of strategies on project management is rejected. As the estimate helps in determining the contribution, thus the analysis reveals that with a $1 \%$ rise in schedule management, the usage of strategies increases by $0 . 8 2 \%$ . Even the rise in usage of strategies by $1 \%$ also results in an increase in project management by $0 . 0 8 \%$ . Hence, strategies have a role in influencing the linkage between schedule management and project management

Table VII. Mediating impact of strategies between schedule management and project management in service companies.   

<table><tr><td>Independent Variable</td><td> Dependent Variable</td><td>Coefficient (Beta values)</td><td>S.E.</td><td>Tvalues</td><td>P</td></tr><tr><td>S</td><td>SM</td><td>0.82</td><td>0.10</td><td>8.11</td><td>0.00</td></tr><tr><td>PM</td><td>S</td><td>0.08</td><td>0.04</td><td>2.17</td><td>0.03</td></tr></table>

# Impact of schedule management on the project management of service companies

Due to the relevant role of schedule management, there is a need to assess the influence of schedule management on project management (Yaghootkar & Gil 2012). and herein below-stated hypothesis would be tested i.e.

$\mathsf { H } _ { 0 6 }$ : There is no impact of schedule management in the project management of service companies

Table VIII. Impact of schedule management on the project management of service companies.   

<table><tr><td></td><td></td><td> Independent VariableDependent VariableCoeffcient (Beta values)</td><td></td><td>s.E.Tvalues</td><td>P</td></tr><tr><td>PM</td><td>SM</td><td>0.01</td><td>0.02</td><td>0.84</td><td>0.40</td></tr></table>

Table VIII shows that the standard error value for the model is low i.e. 0.02 showing less biasness present in the measurement of the model. Herein, as the $\mathsf { p }$ -value is $0 . 4 0 \ > \ 0 . 0 5$ , thus, the null hypothesis of having no impact of schedule management in affecting the project management of service companies is not rejected.

However, in the above sections, it is defined that there is the existence of two variables i.e. factors and strategies which serve as a mediator between schedule management and project management. Thus, the results of the direct and indirect effects between the variables are presented in Table IX.

Table IX. Total impact of schedule management on the project management of service companies.   

<table><tr><td>Dependent variable</td><td>Independent variable</td><td> Total effect</td><td>Direct effect</td><td> Indirect effect</td></tr><tr><td> Project Management</td><td> Schedule Management</td><td>0.24</td><td>0.01</td><td>0.22**</td></tr></table>

In the Table IX, the analysis reveals that the direct effect between variables is much less i.e. 0.01 which is even not significant but the indirect effect is 0.22 and significant. Thus, schedule management does not directly contribute to influencing project management, but with the presence of factors and strategies, the association between the variables could be developed.

# Qualitative analysis

The examination of 10 middle-level or senior-level managers working in service industries herein is done to understand the influence of the developed model. The results for the respondent’s demographics and the responses about the schedule management model are discussed in the below sub-sections.

# Demographic analysis

Herein, as the senior and middle-level managers are included for understanding the implementation status of the schedule management model thus, there is a need to be aware of the experience level of the employees.

The demographics represent that 10 respondents were selected for the qualitative analysis wherein most of the respondents were from age 25 and above i.e. 3 out of 10 from the age group 20-30 years, 3 from 30-40 years, and 4 from 40-50 years. Further, the experience level of selected employees is 2 or more years i.e. 3 out of 10 with experience of below 5 years, 1 from 5-10 years, 3 from 10-15 years, and the remaining 3 from 15-20 years. Thus, the employees included are experienced and are aware of the working or the procedure of implementing the schedule management model.

# Thematic analysis

The focus of qualitative analysis is to understand the implementation of a formulated schedule management model, thus, herein, the responses of the employees are divided into two main themes i.e. procedure of implementation and effect of implementation. A detailed description of the themes and their sub-themes is stated below.

# The procedure of Implementation of the schedule management model

The schedule management model is an essential component for an organization that not only enables the timely completion of a project but also helps in reducing costs and improving the quality of the product. Though many organizations implement different schedule management models, but as the successful implementation is not just dependent on the plan but also on other components, thus, there is a need to adapt the appropriate procedure for the schedule management model implementation. The study with an exploration of schedule management roles identified that the factors and strategies are the key aspects that tend to support the organization in improving project management. About this, interviewee B mentioned, “We follow 6 steps for developing the plan i.e. creating the work breakdown structure, estimating durations for each task, determining the resources required, identifying the predecessors, determining milestones, and identifying the dependencies”.

All the mentioned steps help in designing the schedule management however the procedure of implementation is not just limited to the designing of schedule management but also includes the inclusion of the mediating variables. Herein, Interviewee E stated, “For successful planning and project management, our organization focuses on using strategies like agile methodology and Microsoft teams for maintaining better connection and clearing all communication barriers”. Interviewee F also described, “As employees are the main source of completing projects effectively, thus, our organization focuses on providing freedom to employees”. A lack of human resources management not only could limit the efficiency of the organization but also prevent the successful implementation of plans, thus, the strategies are designed and accepted as the best procedure for having effective usage of the schedule management model. Interviewee H added, “The successful implementation of any plan is dependent on aspects like resources, management, or coordination”. Further, limited skillsets, non-availability of materials, economic environment, site condition, project duration, or quality are the additional factors that affect the implementation of schedule management. Even Interviewee J highlighted that “Our organization not only focuses on planning schedules but also on the strategies used for the management and the factors influencing plans”. Thus, once the planning for the schedule is completed, the consideration of the organization is to identify the factors influencing the linkage and working towards their management by designing appropriate communication, coordination, and resource management policies. These efforts not only help in directing factors positively but also in formulating the strategies to have timely completion of all tasks. Hence, organizations understand the relevance of factors and strategies and thus, for successful implementation of schedule, efforts are made to have digital technology used for better communication and management.

# Effect of implementation schedule management model

The schedule management model is the effort initiated by organizations to manage activities and the timely completion of projects. As the main goal for companies is to have effective management of projects, thus, the focus has always been on understanding the relevance and contribution of the schedule management model. In this regard, Interviewee A mentioned that “The stated model helps in easy access of all task-related information and continuous monitoring, thus, all the tasks could be timely completed without any hindrance”. As the usage of agile methodology and Google Meets makes communication easy, thus, all barriers to project completion due to delays in information are eliminated. Along with this Interviewee C added, “The facility to track the resources and the quality of products makes the management process easy and enables in having a successful completion of the project, rise in productivity of employees, and more profit generation”. Even organizations can strategize more efficiently the procedure of working and could reduce the wastage of resources due to extra time and cost. Thus, the schedule management model not only motivates organizations to have digital technologies adaptation but also enables better functioning to improve project management. However, as the procedure of schedule management involves many steps, thus, there is a prevalence of situations that result in affecting the implementation. Interviewee D in this regard stated, “The procedure of coordination maintenance and adaptation of cloud-based technologies for easy sharing of task information needs enhancement of skillset, thus, the process adds challenges for the company”. Even though many factors are outside the organization’s control like timely delivery of inputs from suppliers or the country’s infrastructure, thus, problems do arise in the implementation process. But an efficient schedule plan needs to include external conditions and make a functioning plan accordingly thus the effect of problems could be minimized. Interviewee G also added, “Though the organization is shifting towards digital technology for supporting schedule management but as the process of estimation for resources, time or unknown events is complex, thus, there are chances of having overpromising or underestimation of time”. To counter these problems, organizations are now moving towards adapting prediction models which not only enable the usage of advanced technologies but also help in reducing the influence of uncertainty. Interviewee I stated that “The organization focuses on regularly monitoring the tasks which helped in eliminating the issues due to external problems on schedule management model implementation”. Thus, the analysis reveals that schedule management not only helps in managing task completion promptly but also has improved project performance and organizational productivity.

# DISCUSSION AND CONCLUSION

Schedule management practices are defined as the means of controlling and managing the activities for the successful and timely completion of the project. Though schedule management helps support an organization in its project, but still there are many factors present that tend to influence the practices of service companies. The existing research had very limited focus on exploring the schedule-driven management role thus this study has identified that appropriate use of resources, incorporation of difficult tasks into the schedule, determination of tasks order as per their dependencies, inadequacies or lack of experience on the project team, focus on completing the task before the deadline, accord every project with high priority for gaining resources, and planning of schedule as per material availability are some of the main factors which tend to affect the schedule management practices. The knowledge of these factors would help the industries to design the plan by considering each of the factors so the possibility of mismanagement and delay is avoided. Further, as the competition is growing for service companies, thus, there is the existence of constant pressure on the organizations to fulfil the demands of consumers and make services more personalized. The study identified that there is no direct impact of schedule management on project management for service companies but with the presence of factors and strategies, the effect could be mediated. Thus, the study uniquely determined that one of the reasons for having not much focus on the schedule-driven management role has been due to the non-presence of a direct connection between schedule-driven management and project management effectiveness. However, there is the presence of factors like human-related factors, project procedures, project-related factors, project management factors, and external environmental factors or strategies like the usage of agile methodology, or Google Meets which enable the successful maintenance of the plan and easy access to all information and updates on completed tasks. The study in the existing digitalized world wherein customization and the timely delivery of quality-based projects are the priority focuses on providing valuable insights to the service industry managers and top management executives about the strategies or the factors which could be considered to derive the advantage of effective schedule management. This strategy identification not only keeps companies in line with the existing technological level of competitive companies but also provides the opportunity to derive a competitive advantage by gaining one-up in the form of a productive schedule. The study also highlights that the schedules for the companies vary as per their need, thus, instead of following a one-size-fits-all concept, the schedule management plan should be designed as per the goals of the companies.

For successful integration of practices, there is a need to have strategies implementation which tends to support the functioning, thus, the following recommendations are stated.

 A discussion portal should be developed within the organization which enables an easy discussion on any queries and an easy flow of information. As this results in preventing any chance of delay due to doubts thus, projects could be completed on time.   
 A daily schedule should be maintained which consists of tasks listed as per priority. This enables the completion of all important works and following the schedule plan.   
 The cloud-based platform should be used for working so that easy accessibility for the documents is present. This would result in reducing any delay due to the non-availability of information   
 Predictive models should be used while designing a schedule management plan based on past performance and external requirements. This would help in reducing the chances of underestimation of time and help in the completion of projects.

# LIMITATIONS OF THE STUDY AND SUGGESTIONS FOR FUTURE RESEARCH

The study focused on building the linkage between schedule management and project management by assessing the perception of employees and managers using quantitative and qualitative data but as the sample size of the study is less i.e. 200 for employees and 10 for managers, thus, it limits the ability of the study to generalize the results for complete India. Even the scope of the study is just limited to the service industries without having much focus on the new-aged technologies and the techniques used for supporting schedule management plans which result in hampering the determination of the major technical challenges.

The scope of the study does not include the new aged technologies role, thus, future studies could focus on determining the technology’s role and also identifying which technique is more suitable in supporting schedule management. Further, as the study covers a broad perspective i.e. service sector, thus, future studies could focus on having a sub-sector-based examination to have a comparison of a schedule management role for different industries and determine for which industry the model is more effective.

# REFERENCES

sustainability into construction project management practices in developing countries. Int J Proj Manag 35(6): 1103-1119.

AMOAH A, BERBEGAL-MIRABENT J & MARIMON F. 2021. Making the management of a project successful: Case of construction projects in developing countries. J Construc Eng Manag 147(12): 04021166.

BANIHASHEMI S, HOSSEINI MR, GOLIZADEH H & SANKARAN S. 2017. Critical success factors (CSFs) for integration of

BERGMANN T & KARWOWSKI W. 2019. Agile project management and project success: A literature review. In: Advances in Human Factors, Business Management and Society: Proceedings of the AHFE 2018 International Conference on Human Factors, Business Management and Society, July 21-25, 2018, Loews Sapphire

Falls Resort at Universal Studios. p. 405-414. Springer. Orlando: Springer.

BOURNE LM & WEAVER P. 2018. The origins of schedule management: the concepts used in planning, allocating, visualizing and managing time in a project. Front Eng Manag 5(2): 150-166.

BUJANG MA, OMAR ED & BAHARUM NA. 2018. A review on sample size determination for Cronbach’s alpha test: a simple guide for researchers. Malays J Med Sci 25(6): 85.

CAMPBELL S, GREENWOOD M, PRIOR S, SHEARER T, WALKEM K, YOUNG S, BYWATERS D & WALKER K. 2020. Purposive sampling: complex or simple? Research case examples. J Res Nurs 25(8): 652-661.

COETZER G & GIBBISON G. 2016. Mediating influence of time management on the relationship between adult attention deficit and the operational effectiveness of project managers. J Manag Dev 35(8): 970-984.

CRESPO LG. 2018. Project Manager Strategies to Improve the Delivery of Construction Projects.

GAUR S. 2022. Understanding the importance of project planning and scheduling in Indian construction projects. J Pos Sch Psychol 6(3): 3535-3544.

JIANG JJ, KLEIN G & FERNANDEZ WD. 2018. From project management to program management: an invitation to investigate programs where IT plays a significant role. J Assoc Inf Syst 19(1): 1.

JITPAIBOON T, SMITH SM & GU Q. 2019. Critical success factors affecting project performance: An analysis of tools, practices, and managerial support. Proj Manag J 50(3): 271-287.

KAASINEN E, ANTTILA AH, HEIKKILÄ P, LAARNI J, KOSKINEN H & VÄÄTÄNEN A. 2022. Smooth and resilient human-machine teamwork as an Industry 5.0 design challenge. Sustainability 14(5): 2773.

KERZNER H. 2018. Project management best practices: Achieving global excellence. New Jersey: John Wiley & Sons. P. 784.

KERZNER H. 2019. Using the project management maturity model: strategic planning for project management. New Jersey: John Wiley & Sons. P. 320.

LARCO JA, FRANSOO J & WIERS VC. 2018. Scheduling the scheduling task: a time-management perspective on scheduling. Cogn Technol Work 20: 1-10.

LI X, XU J & ZHANG Q. 2017. Research on construction schedule management based on BIM technology. Procedia Eng 174: 657-667.

MOHAMUD GI & NYANG’AU PAUL S. 2020. Effect of project management constraints on implementation of public housing projects in Isiolo county, Kenya. Int J Soc Sci Mang Entrep 4(1).

MUILI J, AUDU A & SINGH R. 2019. Modified Estimator of Finite Population Variance in Simple Random Sampling. J Sci Technol Res 1(1).

MUSTAFA M, NORDIN M & RAZZAQ A. 2020. Structural equation modelling using AMOS: Confirmatory factor analysis for taskload of special education integration program teachers. Univ J Educ Res 8(1): 127-33.

NASIR N, NAWI MNM & RADZUAN K. 2016. Relationship between time management in construction industry and project management performance. In: AIP conference proceedings. Vol. 1761. AIP Publishing.

NOR RNH ET AL. 2021. Project scheduling management in the software industry. Turkish J Comput Math Ed 12(3): 2136-2145.

ONG CH & BAHAR T. 2019. Factors influencing project management effectiveness in the Malaysian local councils. Int J Mang Proj Bus $\bar { 1 2 } ( 4 )$ : 1146-1164.

PANDEY P, GAJJAR H & SHAH BJ. 2021. Determining optimal workforce size and schedule at the retail store considering overstaffing and understaffing costs. Comput Ind Eng 161: 107656.

PAPKE-SHIELDS KE & BOYER-WRIGHT KM. 2017. Strategic planning characteristics applied to project management. Int J Proj Manag 35(2): 169-179.

PATANAKUL P. 2015. Key attributes of effectiveness in managing project portfolio. Int J Proj Manag 33(5): 1084-1097.

SPYCHALSKA-WOJTKIEWICZ M. 2018. The scope of project management in service companies. Eur J Serv Manag 27(3/2): 403-407.

SURESH D & SIVAKUMAR A. 2019a. Impact of schedule management plan on project management effectiveness. Int J Adv Eng Manag p. 2249-8958.

SURESH D & SIVAKUMAR A. 2019b. Review of Effectiveness of Schedule Management Planning in Project Success. J Adv Sch Res Allied Educ 16(6): 984–986.

SURESH D & SIVAKUMAR A. 2021. An empirical study, analysis and investigation about impact of schedule management plan in project management effectiveness using structural equation modeling. Vol. 3. 810-834 p.

SURMIAK AD. 2018. Confidentiality in qualitative research involving vulnerable participants: Researchers’ perspectives. In: Forum Qual Sozialforschung. Vol. 19. DEU.

SÜRÜCÜ L & MASLAKCI A. 2020. Validity and reliability in quantitative research. Bus Manag Stud Int J 8(3): 2694-2726.

TERESO A, RIBEIRO P, FERNANDES G, LOUREIRO I & FERREIRA M. 2019. Project management practices in private organizations. Proj Manag J 50(1): 6-22.

VADHANASIN V, RATANAKUAKANGWAN S, SANTIVEJKUL K & PATANAKUL P. 2017. It project management effectiveness framework: a study in thai firms. J Eng Sci Technol 12: 1-16.

VAN WYNGAARD CJ, PRETORIUS JHC & PRETORIUS L. 2012. Theory of the triple constraint—A conceptual review. In: 2012 IEEE International Conference on Industrial Engineering and Engineering Management. p. 1991-1997.

VIDHYASRI R & SIVAGAMASUNDARI R. 2017. A review on factors influencing construction project scheduling. Int J Civ Eng Technol 8(3): 146-157.

VILLAFÁÑEZ F, POZA D, LÓPEZ-PAREDES A, PAJARES J & ACEBES F. 2020. Portfolio scheduling: an integrative approach of limited resources and project prioritization. J Proj Manag 5(2): 103-116.

YAGHOOTKAR K & GIL N. 2012. The effects of schedule-driven project management in multi-project environments. Int J Proj Manag 30(1): 127-140.

# APPENDIX

![](images/ed175c0ce2d67187b4a32c912e8c14ae2879f0cbafa0ba4184350c1068e1c6f9.jpg)

# Conceptual Framework.

# How to cite

SURESH D & ANNAMALAI S. 2024. Effect of schedule management plan in project management worth using structural equation modelling. An Acad Bras Cienc 96: e20230117. DOI 10.1590/0001-3765202420230117.

Manuscript received on February 2, 2023; accepted for publication on December 2, 2023

D. SURESH1   
https://orcid.org/0000-0002-4133-1805   
SIVAKUMAR ANNAMALAI2   
https://orcid.org/0000-0002-4183-7830

1Assistent Professor, Department of Mechanical Engineering, V.S.B Engineering College, 67, Kovai Main Road, Karudayampalayam Post, Karur, Tamil Nadu 639111, India 2Professor, Department of Mechanical Engineering, Excel Engineering College, Komarapalayam, Namakkal- 637303 Tamil Nadu, India

Correspondence to: D. Suresh E-mail: <EMAIL>

# Author contributions

D. Suresh, the corresponding author of this study, contributed towards designing the analysis, collecting some part of the data, performing the analysis and writing the paper. The author was majorly responsible for having the analytical work wherein examination was not just restricted to software-based assessment but also evaluation of the findings and their interpretation concerning study objectives. A. Sivakumar made a major contribution in building the theoretical base of the study, formulating a study blueprint and collecting of remaining data. The author was well versed in the theoretical assessment, therefore, the detailed exploration of the problem and assessment of existing literature contributions in the field was done by the author.
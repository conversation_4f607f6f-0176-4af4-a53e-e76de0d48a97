# 进度管理在软件项目中的应用实践

温翔

(广西天道信息技术有限公司，广西 南宁 530003)

摘要：阐述了在项目实施当中所采用的进度管理的策略、技术与方法,说明了如何制定符合项目及环境特点的进度管理策略,描述了工作量与工期估算以及进度计划编制与控制等的具体方法，并对如何在项目中保证进度管理的策略的实现进行了分析，给出了具体措施，最后列出了项目实施当中进度管理的特点。

关键词：进度管理；管理信息系统；策略；技术；方法

# Application Practice of Schedule Management in Software Project

WEN Xiang

(Guangxi Tiandao Information Technology Co.，Ltd.,Nanning, Guangxi 530003,China)

Abstract：Weelaboratethestrategytechnologiesandmethodsofschedulemanagementadoptedinprojectimplementationexplain howtodeveloptheschedulemanagementstrategyfitinwiththeprojectandenvironmentalcharacteristics，describethespecific methodsofworkloadandactivitydurationestimatingaswellasscheduleplanningandcontroling，alsoanalyzehowtoensurethe implementationofschedulemanagementstrategyinproject，givethespecificmeasures，finallylistthefeaturesofscedule management in project implementation.

Key words:schedule management；MIS；strategy； technology；method

# 0引言

进度管理一直以来是项目管理当中极其重要的一个内容,在目前的绝大部分项目中，进度管理往往是让项目经理最为头疼的一件事,因为项目实施进度往往落后于项目预期。在我们平常进行的各类项目当中,由于项目类别、实施条件及环境因素等原因,不同项目会遇到不同的涉及进度管理的问题与难题。在项目的实施中,进度管理的实践如何与理论相结合，如何将进度管理当中的技术、方法与措施运用到具体项目的实践中,这是一个需要不断参与项目实施、不断探索与总结的理性过程。

本文以作者参与过的项目为例,重点阐述了在组织项目实施过程中所采用的进度管理的策略、技术与方法，论述了如何制定符合项目及环境特点的进度管理策略,描述了工作量、工期估算、进度计划编制与控制等具体方法,并列举了保证进度管理的策略和措施,分析了所采用的策略、技术及措施后给项目实施带来的效果,最后对项目进度管理的特点进行了阐述。

# 1项目概述

南方物流有限责任公司是一家铁路物流企业，它依托铁路,以铁路运输代理服务及其延伸服务等为主要业务，业务内容涵盖运输、仓储、包装、装卸、商贸等方面，是一家综合性物流企业。该企业设三级管理机构,即总公司、各地分公司、经营网点，经营点分布于铁路局所辖范围，共计100多家，员工有三千多人,年营业额约13亿元。南方物流公司,由于依托铁路的优势,大力发展以铁路运输为主的物流业务，在近几年取得了很大发展，业务量及经营网点拓展较快。公司管理层在企业快速发展过程中，明显感觉到了由于点多、线长、面广所带来的管理难度，跨地区的物流组织及管理成为企业发展的瓶颈。

因此，从2009年下半年开始，南方物流公司与作者所在公司就其信息化建设成立了开发项目组,对物流公司的信息化开发进行了调研、规划及开发实施。南方物流管理信息系统的开发时间历时一年,经过半年试运行及调试,于2010年7月全部上线,应用至今情况良好。该系统包括运输代理及延伸服务管理系统、运输综合统计分析系统、客户关系管理系统、网上销售系统、门户及服务网站系统、内部网络办公管理系统等六个子系统，内容涵盖南方物流公司基础物流办理作业管理、市场营销管理、客户关系管理、企业宣传及服务、领导决策、内部行政、办公、财务、采购、后勤物流企业管理的所有主要流程,实现了物流公司的业务办理、客户服务、市场营销、业务统计分析及管理决策支持、办公等自动化、网络化及信息共享。

在该项目中，作者担任项目开发负责人，负责项目的开发组织与管理，其中六个子系统分别有子系统的技术负责组长，另外有测试组及培训组，以及变更控制小组。作者的主要工作是组织并参与项目的需求调用、需求分析、系统总体方案设计等,指挥项目开发，协调开发当中遇到的问题,包括进度管理、成本管理、质量管理、计划与跟踪,代表开发方与客户进行项目沟通与协调、负责组织项目实施等工作。

# 2项目进度管理策略、技术与方法

# 2.1项目进度管理中采取的策略

进度管理作为项目开发当中一个重要的内容涉及项目开发的全过程,项目实施的每个阶段，从概念、开发、实施到结束均离不开进度的计划与控制等工作。该项目是一个涉及多个子系统开发的综合管理信息系统，在技术上，面临各个子系统之间的接口处理、数据共享与协同开发与运行等问题,所以在项目管理中需要对各个管理因素，包括进度、资源、人力、沟通、质量等进行统一协调,特别是对进度进行控制。

我们在进度控制中采用的策略是：“做好计划、事前控制、及时调整,确保进度。”

做好计划。在项目中，事前制定好进度计划,包括子系统进度计划及总体进度计划。

事前控制。事前做好充分的准备工作，预留部分活动时间,尽可能避免发生干扰进度的问题或把问题解决在萌芽状态。

及时调整。发生了影响进度计划的异常事件或问题,及时处理,采取应对措施,及时调整计划。

确保进度。调整尽可能不要影响预定计划。如果发生计划进度变更，需要协调各方资源以确保进度符合客户及各方要求。

# 2.2项目进度管理中应用的技术与方法

项目管理中进度控制的技术与方法有工作量与工期的估算方法,包括delphi法,类比法,功能点计算法等。计划编制与控制的技术与方法有甘特图法,关键路径法,计划评审技术,挣值法等。在我们实施的项目当中,根据项目的实际情况与特点,进度控制选择了如下技术与方法。

(1）采用类比法进行项目工作量及工期的估算。由于公司的多数项目均是以企业综合管理信息系统为主，并且作者也有多个类似项目的管理实施经验，因此，我们决定采用类比法进行项目工作量估算：根据历史项目与当前项目的规模、功能结构、应用环境等几方面因素，并制作新项目各个子系统功能列表，与历史项目功能点进行对比分析，由此估算各个子系统的规模,得到项目总工作量估算,作为进度计划与控制的基础。

(2)采用甘特图法编制项目进度计划。考虑到各个子系统的接口协调,对各个子系统进行功能分解，根据分解功能估算开发时间,制定各个子系统进度计划，最后将各个子系统汇总得到项目进度计划。

(3）动态监控进度,确保项目按计划完成,为此我们采用了如下措施：

$\textcircled{1}$ 对计划进度进行评审。进度计划制定后,召集项目系统分析员、各组长、测试组长、变更控制小组成员等进行正式会议,以正式评审的方式对项目进度计划进行讨论,最后经综合修正后得到正式进度计划，以保证项目计划的可行并符合项目开发要求。

$\textcircled{2}$ 进度执行监督与里程碑检查。我们采取重点监控的方法对项目开发的关键点及关键任务，进行进度控制，作者把里程碑重点放在了需求说明书、计划评审、子系统集成测试、总项目集成等几个涉及客户和子系统之间协调的关键活动点上面。

$\textcircled{3}$ 采用进度动态监测措施，坚持每周进度碰头会与进展报告。通过周例会及进展报告动态掌握项目进展状况。各个子系统进展情况,子系统之间的协调及客户协调状况。弄清需要解决的问题。对于技术难点，则指定某个系统分析员负责组织有关项目组成员攻克难题;对于影响项目进度的问题，则通过会议讨论拿出解决方案，以保证项目的正常进度。

# 3项目进度管理的方法与策略的实施与特点

# 3.1项目进度管理的实施

各个子系统之间的数据协调是一个重大管理难点,需要强化项目组成员之间的沟通途径及方式。在项目开发过程中,有些子系统进度可能稍快，有些稍慢这就可能会产生问题和矛盾。针对这种情况,我们对项目周例会做了调整,将原定以项目进度汇报为主的会议模式，调整为以项目子系统进度协调为主的会议模式,重点解决各子系统之间的接口协调及进度监控，随着开发的深入,这种调整带来了明显的效果。我们通过其他辅助方式,比如口头交流等非正式沟通方式改善各个成员之间的关系,使得项目组成员统一思想,把焦点集中在更好地完成项目实施的目标上来。在里程碑的关键控制上,这种计划加调整的策略得到了良好的发挥，保证了项目进度。

在项目实施过程中，比较容易出现需求范围蔓延的情况。随着开发的深入，开发方及客户对系统与业务管理认识越来越清晰，需求的变更在所难免，因此对需求变更的控制特别重要。对于开发组而言,既要保证项目的开发符合客户需求,又要界定需求范围，防止需求蔓延,保护开发方利益，保证开发按进度推进,避免因为一味满足客户需求而导致需求失控,项目失败。因此,项目组成立了变更控制小组,制定了严格的需求变更控制制度;并就需求变化的情况及时与客户沟通,通过正式会议、非正式交流等不同渠道达成需求变更的共识,最后双方共同对需求的变更进行确认与实施,既让客户满意。

# 3.2在项目实施中进度管理的特点

在项目实施过程中,进度保证策略及措施必需"及时调整，灵活掌握”。在项目实施当中，会发生很多意外的情况，计划总没有变化快,并且客户的情况不同。因此,当中出现需求范围、时间要求等变化情况,需要灵活对待,通过沟通、及时化解问题、调整策略与计划,保证项目的顺利实施。

# 4结束语

进度管理在项目开发中有着重要的作用，根据项目实际情况，制定符合项目特点的进度管理策略，采用得当的技术与方法，可以有效提高项目进度管理效率，保证项目进度符合预期。

由于作者水平有限，难免有不足之处,敬请专家批评指正，以促使作者能够开发出更多更适用的项目,服务于企业和社会。

# 参考文献：

[1](美)富勒(Fuller,M.A),(美)瓦拉西奇(Valacich,J.S.),(美)乔治(George,J.F.)著，杨眉，车皓阳译.IT项目管理[M].人民邮电出版社,2009.  
[2]周贺来.软件项目管理实用教程[M].机械工业出版社,2003.  
[3]ScottBerkun，项目管理艺术.东南大学出版社,2008.  
[4]林锐,李江博,黄曙江.如何管理软件企业[M].机械工业出版社,2010.  
[5](美)JohannaRothman,郑柯译.项目管理修炼之道[M].人民邮电出版社,2009.  
[6](美)KarlE.Wiegers,陈展文,何国坤译.成功软件项目管理的奥秘[M].人民邮电出版社，2009.  
[7](美)ClarkA.Campbell,赵俐译.一页纸IT项目管理[M].人民邮电出版

社，2009.
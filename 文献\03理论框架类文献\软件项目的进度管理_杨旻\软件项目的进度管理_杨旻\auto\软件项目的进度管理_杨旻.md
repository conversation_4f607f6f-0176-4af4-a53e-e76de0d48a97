# 软件项目的进度管理

杨旻

（上海交通大学电子信息与电气工程学院，上海200030)

摘要：以PMI项目管理体系相关理论为依据，主要对软件项目管理中的进度管理问题进行研究。分析影响软件项目进度的各类因素，并对软件项目进度管理的相关细节进行阐述。  
关键词：进度管理；软件项目；进度计划

# 1简介

# 1.1 基本概念

项目管理（Project Management)，一个新鲜事物，从最开始在中国出现算起也不过十多年的光景。项目管理是指为了完成一个特定的目标，应用一定的规范或规章制度对项目资源进行全面的规划、组织、协调、控制并使之系统化的过程，即在规定的时间、预算和质量目标范围内完成项目的各种工作。

进度管理，简单来说,就是采用科学的方法确定进度目标，编制进度计划和资源供应计划，进行进度控制，在与质量、费用目标协调的基础上，实现工期目标。

软件项目管理是为了使软件项目能够按照预定的成本、进度、质量顺利完成，而对成本、人员、进度、质量和风险等进行分析和管理的活动。目前，对软件项目进行科学有效的项目管理，尤其是进度管理已经成为一种惯例。

# 1.2软件项目的特点

(1)在项目初期，对成本和工作人员的需求相对较少。随着项目的进展，各类资源需求变得越来越多。当项目接近尾声时，资源需求又会剧烈减少。

(2)在项目初期，风险和不确定性为最高，成功的概率最低。而随着项目的逐步发展，成功的可能性越

来越高。

(3)在项目起始阶段，项目设计人员的能力对项目产品的最终特征、项目成本、项目质量等要素的影响力是最大的，随着项目的进行，这种影响力逐渐削弱。

# 2软件项目进度管理原理及内容

进度管理，可以从两个方面来理解，一方面是要制订一个可行而且高效率的计划，而另一方面则是要将此计划坚决贯彻执行。

进度管理一般遵循以下原理：

(1)动态控制原理;项目进度控制是一个不断进行的动态控制，也是一个动态进行的过程。

(2)弹性原理：项目进度计划周期长，影响进度的原因多，其中有的已被人们掌握，根据统计经验估计出影响的程度和出现的可能性，并在确定进度目标时，进行实现目标的发现分析。

(3)封闭循环原理：进度计划控制的全过程是计划、实施、检查、比较分析、确定调整措施和再计划的封闭循环过程。

进度管理一般包含以下几方面内容：

(1)项目进度安排。 $\textcircled{1}$ 项目活动排序，或者说确定工作包的逻辑关系。活动依赖关系确认的正确与否，将会直接影响到项目的进度安排、资源调配和费用的开支。项目活动的安排主要是用网络图法、关键路径法和里程碑制度； $\textcircled{2}$ 项目历时估算。历时估算包括一项活动所消耗的实际工作时间加上工作间歇时间，注意到这一点非常重要。历时估算方法主要有：类比法，通过相同类别的项目比较，确定不同的项目工作所需要的时间;专家法，依靠专家过去的知识、经验进行估算;参数模型法，是通过依据历史数据，用计算机回归分析来确定一种数学模型的方法； $\textcircled{3}$ 制订进度计划。制订进度计划就是决定项目活动的开始和完成的日期。根据对项目内容进行的分解，找出了项目工作的先后顺序，估计出了工作完成时间之后，就要安排好工作的时间进度。随着较多数据的获得，对日常活动程序反复进行改进，进度计划也将不断更新。

(2)人员工作量分配。任何项目都要依靠项目组成员来完成，成员的多少和具体工作的分配也在一定程度上决定了项目的成败。项目人员分配方面有以下经验：$\textcircled{1}$ 项目利益相关方之间的有效沟通会极大地提高软件生产率和质量； $\textcircled{2}$ 尽可能不要在项目后期增加人员，因为这样既会导致人力成本的增加，也增大了沟通难度和成本； $\textcircled{3}$ 适当地延长开发周期，可以减少人力。

例如一个软件项目的人员的工作量分配比例为：$2 0 \% \sim 2 5 \%$ 需求分析， $2 0 \% \sim 2 5 \%$ 设计， $1 5 \% \sim 2 0 \%$ 编码 $3 0 \% { \sim } 4 0 \%$ 调测。

(3)项目任务优化。任何一个项目都是由若干个相对独立的任务链组成的，只有在任何一条链都已经优化的基础上，才可能进行系统的优化，因此，保证每条任务链的效率是整个项目进度优化的前提和基础。

通常，可以采用设置"里程碑任务"的方法来保证单独任务链的最优。所谓"里程碑任务”,往往并非是一个具体需要完成的任务，而只是一个标志性的事件。例如软件开发项目中的 $\ " 0 . 9$ 版本测试”，“测试"是一个子任务，“撰写测试报告"也是一个子任务，而"完成0.9版本测试报告"就不是一个实实在在的子任务，但在制订计划以及跟踪计划的时候，可以设定“完成0.9版本测试报告"这个“里程碑任务”,工期设置为"0工作日”,作为项目进展到一个实质性阶段的标志。

“里程碑任务"的目的就在于将一个过程性的任务用一个结论性的标志标的，从而使得任务拥有明确的起止点，这一系列的起止点就成为引导整个项目进展的"里程碑”。在项目管理进度跟踪的过程中，只要能保证里程碑任务的按时完成,整个项目的进度也就有了保障。

# 3软件项目进度的影响因素分析

# 3.1变更控制

在项目执行过程中要注意对变更的控制，特别是要确保在细化过程中尽量不要改变工作范围。有四个重要控制点：授权、审核、评估和确认;在实施过程要进行跟踪和验证，确保变更被正确执行。

# 3.2客户风险

客户风险存在于客户化项目中，根据客户行业特点的不同，技术、理解水平的不同，所产生的风险也不尽相同。特别要避免因需求理解误差导致的项目目标的更改等可能导致进度失控的风险。

# 3.3技术与工具

以开发为主的软件项目，技术和工具风险必须特别重视。开发平台必须适合本项目所涉及的软件开发、满足最终的需求，平台的错误选择将导致庞大的开发工作量，即便满足了用户需求也可能造成系统效率低下、扩展性差的致命问题，软件可能会很快被淘汰。

# 3.4人员技能

项目人员技术水平、工作效率、团队适应性和沟通能力等素质，都会对开发进度产生影响。

# 3.5人员激励

项目涉及参与该项目工作的个体和组织，或者是那些由于项目的实施或项目的成功其利益会受到正面或反面影响的个体和组织。必须识别哪些个体和组织是项目的涉及人员，确定他们的需求和期望，然后设法满足和影响这些需求、期望，以确保项目能够成功。

# 4软件项目进度管理的基本方法和选择依据

# 4.1甘特图

19 世纪由美国科学家甘特发明,因此被称做"甘

特图”。这种计划方法的特点是简单并容易制作，也容易理解和不断更新，它是进度计划方法中最简单的一种。

# 4.2关键路径法(CPM)

项目是由各个任务构成的，每个任务都有一个最早、最迟的开始时间和结束时间，如果一个任务的最早和最迟时间相同，则表示其为关键任务，一系列不同任务链条上的关键任务链接成为项目的关键路径，关键路径是整个项目的主要矛盾，是确保项目能否按时完成的关键。

# 4.3计划评审技术(PERT)

计划评审技术是一种应用工作前后逻辑关系及活动不确定时间表示的网络计划图，其基本的形式与CPM网络计划基本相同，只是在工作时间估计方面与CPM有一定的区别，CPM仅需要一个确定的工作时间，而PERT需要工作的三个时间估计。分别为乐观工期 $\left( \mathrm { T } \mathbf { 0 } \right)$ 、悲观工期(Tp)和最可能的工期(Tm），然后利用公式期望工时 $\mathrm { T e } = ( \mathrm { T o } + 4 \mathrm { T m } + \mathrm { T p } ) / 6$ 得出基准计划的时间。

采用不同的进度计划方法本身所需的时间和费用是不同的。关键日期表编制时间最短，费用最低。甘特图所需时间要长一些，费用也高一些。CPM要把每个活动都加以分析，如活动数目较多，还需用计算机求出总工期和关键路径，因此花费的时间和费用将更多。PERT法可以说是制订项目进度计划方法中最复杂的一种，所以花费时间和费用也最多。应该采用哪一种进度计划方法，主要应考虑下列因素：项目的规模大小、项目的复杂程度、项目的紧急性以及对项目细节掌握的程度。

一个树型图),因此结构化地组织和定义了项目的工作范围。

# 5.2进度计划的实施

在按进度计划实施的过程中，项目实施条件的变化是绝对的，不变是相对的，所以，进度计划的平衡是暂时的，而不平衡是经常的。必须建立一套科学的进度控制系统，包括进度监测、进度记录、分析和调整过程，以保证项目进度同实际实施环境相适应，使调整措施得到及时准确的执行。在项目进行到里程碑时，要对进度计划进行认真的评审，以保证项目能够按照计划顺利实施。里程碑是完成阶段性工作的标志，在项目管理中具有重要意义。比如软件开发中的重要节点就是一个里程碑。重要节点是一种大量活动都依赖它的节点，这类活动一旦延期，可能造成后续很多工作无法进行。另一种是依赖于大量活动的节点，这类活动的开始要取决于很多工作按期完成，风险较大。

# 5.3进度计划的实施控制

进度控制就是比较实际状态和计划之间的差异，并做出必要的调整使项目向有利的方向发展。进度控制可以分成四个步骤：Plan（计划）、Do(执行）、Check（检查)和Action(行动)，简称PDCA，因为软件开发项目的不确定性，项目监控显得非常重要，项目应该在检查点进行检查，比较实际和计划的差异并进行调整;通过设定里程碑渐近目标、增强控制、降低风险；而基线是重要的里程碑，交付物应通过评审并开始受控。定期监控进展，分析项目偏差，采取必要措施以实现目标。当进度出现偏差时，需要分析这种偏差对后续工序产生的影响，偏差的大小以及偏差所处的位置。分析的方法主要是利用网络计划中的总时差和自由时差来进行分析判断。

# 5软件项目进度管理细节

# 6结语

# 5.1进度计划的制订

项目进度计划的第一步是项目范围定义，进而定义项目需要进行的活动、角色、责任以及项目组的结构。范围定义一般使用工作分解结构（WorkBreak-down Structure,WBS)。WBS将项目的“交付物”自顶向下逐层分解成易于管理的若干元素(这些元素组成

本文以PMI项目管理体系相关理论为依据，粗浅探讨了软件项目进度管理的原理及基本内容、影响软件项目进度的关键因素、软件项目进度管理的基本方法和选择依据及进度管理的细节。通过以上内容的描述，来表达目前大多数软件项目中的进度问题，都可以

# 企业信息化项目管理中的激励对策与探讨

李东锋

（上海交通大学电子信息与电气工程学院，上海200030)

摘要：从项目管理的角度，对于企业信息化进程中的激励机制给予说明，以制造执行系统(MES)为例，说明激励机制对于企业信息化的重要作用，同时笔者根据自身的实际经验，对于现代化企业的项目管理中的激励机制给出相应的建议。

关键词：项目管理;激励机制;MES;进度管理

我国加人WTO以后，随着国际化竞争的日益加剧，越来越多的企业认识到信息化的重要作用，大到企业资源计划(ERP)系统，制造执行系统(MES)，小到一个办公自动化(OA)系统，这些系统帮助企业从资源管理到无纸化办公，各个方面都有了充分的提高，但是企业实施信息化又是一个复杂的项目管理过程，涉及到项目管理的各个方面，故能否统筹好各个方面的关系，成功实施项目显得尤为重要。企业信息化不是一蹴而就的，逐步实施信息化，有助于企业跻身于全球供应链，增强自身的竞争实力，从而提升质量、降低成本，提升企业的整体实力。本文即从项目管理者的角度出发，以MES为例，论证在实施信息化系统中激励的对策并给出相应的建议。

MES是制造执行系统的简称，主要是针对车间执行系统而言，因此有些人也把MES 称做 SFC(Shop

Flow Control)系统，此系统是车间自动机床与企业资源计划系统的中间层，负责生产管理和调度执行。实现由主生产计划生成的生产需求，把生产需求分配到工作中心或生产线，并进行跟踪、确认和结算。同时把实施信息反馈给计划层，从而实现企业的连续信息流，形成完整、闭环的生产管理。实施MES是一项系统工程，可以在企业实施ERP之前，亦可以在企业实施ERP之后，在整个实施过程中采取项目管理的方式进行整体的统筹调度，是实施项目成功的关键因素。

项目实施是一项长期和艰苦的工作，参与到其中的项目成员由各个相关部门的关键人员共同组成，来源不同，成分复杂，素质各异，因此，项目期间的激励与沟通问题尤为重要。如果处理不当，极易造成一盘散沙的局面，影响甚至阻碍项目进程，最终导致项目失败。

通过完善的分析找出确切原因，同时通过使用合理的方法及工具对症下药，有效提高项目的进度管理效率。

# 参考文献

[1]窦燕.影响软件项目管理关键因素的探讨[J].燕山大学学报，
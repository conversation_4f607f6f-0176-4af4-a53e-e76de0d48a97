# 硕士学位论文 THESIS FOR MASTER'S DEGREE

论文题目 W 公司汽车集成热交换新产品开发项目进度管理研究

作 者 陈书宁学 号 2180712学 院(部) 工商管理学院专 业 工程管理指导教师 戢守峰教授

# 2024 年 06 月

# 学 位 论 文

# W 公司汽车集成热交换新产品开发项目进度管理研究

作 者 姓 名 ： 陈书宁  
作 者 学 号 ： 2180712  
指 导 教 师 ： 戢守峰 教授东北大学工商管理学院  
申请学位级别： 硕士 学 科 类 别 ： 管理学  
学科专业名称： 工程管理（专业学位）  
论文提交日期： 2024 年 06 月 论文答辩日期： 2024 年 06 月  
学位授予日期： 2024年07 月 答辩委员会主席： 王建伟教授  
评 阅 人 ： 王晓欢副教授 沈阳工业大学 祝爱民教授

东 北 大 学2024 年 6 月

# A Thesis in Master of Engineering Management

# Research on Schedule Management of Company W Integrated Heat Exchange Development Project

By Chen Shuning

Supervisor: Ji Shoufeng

# Northeastern University June 2024

# 独创性声明

本人声明，所呈交的学位论文是在导师的指导下完成的。论文中取得的研究成果除加以标注和致谢的地方外，不包含其他人己经发表或撰写过的研究成果，也不包括本人为获得其他学位而使用过的材料。与我一同工作的同志对本研究所做的任何贡献均已在论文中作了明确的说明并表示谢意。

学位论文作者签名：

日 期： 2024 年 06 月 27 日

# 学位论文版权使用授权书

本学位论文作者和指导教师完全了解东北大学有关保留、使用学位论文的规定：即学校有权保留并向国家有关部门或机构送交论文的复印件和磁盘，允许论文被查阅和借阅。本人同意东北大学可以将学位论文的全部或部分内容编入有关数据库进行检索、交流。

作者和导师同意网上交流的时间为作者获得学位后：

半年 □ 一年□ 一年半□ 两年☑学位论文作者签名： 导师签名：签字日期： 2024 年 06 月 27 日 签字日期：2024 年 06 月 27 日

# 摘 要

新产品开发项目的进度管理是从单一的计划性进度管理到强调系统化进度管理的过程中逐步形成的。传统的进度管理概念局限于大型建筑类项目，注重企业自身资源的充分利用。随着现代制造业对新产品开发项目和其进度管理的研究开展，项目进度管理被扩展到企业与产品供应商之间，并进一步加强了与研发、质量、商务以及生产制造之间的联系，形成了涵盖从设计到质量保障，再到最终生产交付的复杂动态系统。成功的新产品开发项目进度管理，必须以高效的企业内部合作为基础, 必须注意对商务合作、成本管理以及资源分配方面的相关构建。

本文以 W 公司集成热交换新产品开发项目为研究对象，针对其项目特点分析了其存在的风险因素，制定并优化了项目进度计划并对进度进行控制保障。基于项目进度计划、项目管理等理论与方法，采用文献研究方法、实证调查与分析法、案例分析法等方法，对公司的开发项目进度计划的制定进行研究。主要研究内容有包括以下3个方面：

（1）W公司汽车集成热交换产品开发的项目特点与风险分析。这部分内容主要通过对新产品开发项目的介绍，以及公司项目开发流程进行分析，找出影响项目进度的风险点，进而在项向后的进度计划的制定中避免风险。

（2）将项目进度管理、进度计划和进度控制的理论与方法应用到 W 公司新产品开发项目的进度管理研究中。这部分内容在对项目工作内容分解以及关键路径的确定基础上，运用 Project 软件制订出项目的进度计划甘特图。

（3）对 W 公司新产品开发项目进度计划进行控制与保障。主要从动态监测偏差分析、偏差调整、以及保障措施的建立这几个方面对W公司的开发项目进行控制与保障。

通过对W公司新产品开发项目进度计划的制定与保障研究，得出具有一定针对性和指导意义的结论。希望本文的研究能够对 W公司以及同类企业在新产品开发项目进度方面启到有益的借鉴。

关键词：项目进度管理; 新产品开发; 集成热交换; 绘制网络图; 关键路径

# Abstract

The progress management of new product development project is gradually formed from the single planned progress management to the process of emphasizing systematic progress management. The traditional concept of schedule management is limited to large-scale construction projects, focusing on the full utilization of the enterprise's own resources. With the modern manufacturing industry on new product development projects and its progress management research, project schedule management is extended to the enterprise and product suppliers, and further strengthened with research and development, quality, business and manufacturing links between the formation of a complex dynamic system covering the design to quality assurance, and then to the final production delivery. Successful new product development project schedule management must be based on efficient internal cooperation, and attention must be paid to the construction of business cooperation, cost management and resource allocation.

This article takes the integrated heat exchange new product development project of Company W as the research object, analyzes the risk factors for its project characteristics, formulates and optimizes the project schedule plan and controls and guarantees the schedule. Based on the theory and method of project schedule planning, management, etc., adopting literature research method, normative theoretical analysis method, empirical investigation and mathematical modeling and software system analysis, etc., the formulation of the company's development project schedule plan is studied. The main research content has includes the following 3 aspects:

(1) Project characteristics and risk analysis of the development of automotive integrated heat exchanger products in Company W. This part of the content is mainly through the introduction of the new product development project, as well as the analysis of the company's project development process, to find out the risk points affecting the project schedule, and then to avoid the risk in the development of the project to the future schedule plan.

(2) The theories and methods of project schedule management, schedule planning and schedule control are applied to the study of schedule management of the new product development project of Company W. This part is based on the decomposition of project work content and the determination of critical path. This part of the content of the project work content decomposition and the determination of the critical path, based on the use of Project software to develop a project schedule Gantt chart.

(3) Control and guarantee the progress plan of the new product development project of Company W. We control and safeguard the development project of Company W mainly from the aspects of dynamic monitoring deviation analysis, deviation adjustment, and establishment of safeguard measures.

Through the development of new product development project schedule of company W and guarantee research, to have a certain target and guiding significance of the conclusion. I hope that the research in this paper can be useful to W company and similar enterprises in the new product development project schedule to enlighten the useful reference.

Key words: project schedule management; new product development; integrated heat exchanger; draw network diagram; critical path method

# 目 录

独创性声明..  
摘 要..  
Abstract..

# 第1章 绪 论..

1.1研究背景与研究意义.

1.1.1 研究背景..  
1.1.2 研究意义.

1.2国内外研究现状..

1.2.1 国内研究现状.  
1.2.2 国外研究现状.  
1.2.3 发展动态.

1.3研究内容与方法.

1.3.1 研究内容.  
1.3.2 研究方法..

1.4 论文的章节结构.

# 第2章 相关理论与方法概述..

2.1项目进度管理概述 .8

2.1.1项目进度管理的定义 8  
2.1.2项目进度管理的内容

2.2项目进度计划..

2.2.1项目进度计划的概念  
2.2.2项目进度计划的步骤.

2.3 项目进度计划编制的方法.. 11

2.3.1 WBS 工作分解法 .

2.3.2 甘特图法 . 11

# 2.3.3 关键路径（CPM）法. 13

2.4 项目进度控制. .14

2.4.1 项目进度控制定义. 14  
2.4.2项目进度控制原理. 14  
2.4.3项目进度控制方法. 15

# 第3章 W公司汽车集成热交换新产品开发项目概况.. ..16

3.1 W公司概况与开发项目流程. .16

3.1.1 公司简介 . 16  
3.1.2 开发项目流程.

3.2 W公司汽车集成热交换新产品开发项目概述. .18

3.2.1 项目背景介绍. 18  
3.2.2 项目重要性 . 19

3.3项目特点以及项目进度影响因素. .20

3.3.1 项目特点 . . 20  
3.3.2项目进度影响因素. . 21

# 第4章 W公司汽车集成热交换新产品开发项目进度计划的制定........23

4.1 项目工作范围与组织结构.. .23

4.1.1 项目工作范围. . 23  
4.1.2 项目责任分配. 24

4.2 项目工作分解、排序与里程碑设定 .25

4.2.1 项目工作分解. . 25  
4.2.2 项目工作排序. 27  
4.2.3 项目里程碑设定 . 29

4.3 项目工作逻辑关系和持续时间估算. .31

4.3.1 项目工作逻辑关系分析. . 31  
4.3.2 项目工作持续时间估算. . 33

4.4 项目进度计划的编制. .35

4.4.1 编制网络图 . 35  
4.4.2 关键路径的确定 .. 35  
4.4.3 项目进度甘特图 . 39  
4.5项目进度计划的优化.. .39  
4.5.1 项目进度优化的原则和目标. . 39  
4.5.2 项目进度计划优化内容. . 41  
4.5.3 优化后项目进度计划. .. 43

# 第5章W公司汽车集成热交换新产品开发项目进度控制与保障措施46

# 5.1项目进度控制体系和措施. .46

# 5.2项目进度动态监测与偏差分析. .49

# 5.3项目进度偏差调整. .52

# 5.4 项目进度的保障措施. 53

# 第 6 章 结论与展望. ..60

# 参考文献.. .62

# 致 谢.. .64

# 第 1 章 绪 论

# 1.1 研究背景与研究意义

# 1.1.1 研究背景

近些年来，发展新能源汽车成为了公认的节能减排有效措施，因此新能源汽车产业迅速成为各国的战略性新兴产业。2023 年2月欧洲议会通过了一项决议，旨在确保2035年完全停售燃油轿车，实现欧盟到 2050年温室气体零排放，这无疑再次推动了新能源汽车的发展。然而，新能源汽车的发展受制于其有限的行驶里程和逊色于燃油车的驾驶的舒适性以及安全性，迅速冲破其技术壁垒成为了各个车企的聚焦点。

集成热交换新产品开发成为了解决新能源汽车当前弊端的关键。相比传统燃油汽车，新能源汽车最为核心的“三电”系统需要极其复杂的热管理系统，用来控制精细繁多的电子元器件温度。电子元器件及其管路的温度过高将增加整车的安全风险；温度过低，电子元器件的寿命以及性能将大幅受损，更关乎整车的续驶里程和舒适度。新能源汽车集成热交换新产品主要是对电池、电机、电控以及乘员舱温度的调节，保障它们工作在最适宜温度。

以其中最为核心的锂电池为例，其最理想的工作温度在 $2 0 \mathrm { { } ^ { \circ } C }$ 至 $3 0 \mathrm { { } ^ { \circ } C }$ 范围内。当电池处在较低的温度下，其充放电性能不佳，甚至无法进行常规的工作。而在较高温度下，其循环寿命将大大缩短甚至影响续航里程，在过于高的温度下工作，甚至会导致爆炸。当然，电机以及其他电子元器件的温度控制尤为重要，有效的将各个子部件进行迅速的热量转换可以实现新能源汽车中的热量管控，保证其安全性。由于没有发动机热源，新能源汽车一般需要额外的发热装置来补充系统热量。因此集成热交换新产品需要同时满足制冷、制热两种需求。

就当前的情况来看，新能源汽车较燃油汽车相比其总体生产规模不大，与此同时其开发路线以及不断更细的电池技术也正呈现出多元化的发展趋势，解决新能源汽车技术壁垒的集成热管理产品目前急需快速发展。新能源汽车正在以迅猛的势头快速的发展，毫无疑问的将成为未来汽车产业的主流发展趋势。在当前的情势下，最为关键的就是快速相应市场需求并对资源进行全面统筹和布局。W公司借此良机抓住新能源汽车的发展的核心技术，大力发展集成热交换新产品，切实满足消费者对于新能源车型驾驶里程、舒适性以及安全性能的要求，定能在我国庞大的汽车生产企业的竞争中崭露头角。

为满足公司管理层对于开发项目的时间要求，确保集成热交换新产品顺利的完成量产交付，从此开发项目的实际情况出发，分析并制定科学规范的项目进度计划，以确保开发项目在预计的工期内完成各项工作任务。本文针对集成热交换新产品开发项目中可能出现的问题进行详细的分析，并使用甘特图法、关键路径法以及计划评审法，以达到制订项目进度计划并对开发项目进行合理优化的目的。

# 1.1.2 研究意义

本文以 W 公司集成热交换新产品开发项目为研究对象，结合项目进度计划和项目进度控制的相关理论对其进行深入的研究，制定出符合此开发项目的进度计划和进度控制流程，以实现项目管理的最终目标。本文具有以下意义：

（1） 理论意义。本文的理论意义在于尝试将项目管理理论中的进度计划制定与控制管理方法运用到W公司集成热交换新产品的开发项目的管理过程中，同时与项目特点与开发中遇到的实际情况相结合，制订出更为科学规范的项目进度计划，并对其进行更为有效的进度控制。同时，对项目管理理论的研究领域的拓展起到了极大的重用，有效的补充了开发项目进度计划与控制方面的有关研究。

（2） 实际应用意义。本文的研究对于 W 公司集成热交换新产品开发项目具有重要的指导意义，项目管理者通过制订科学规范的项目进度计划，可以充分的了解项目，并对项目的工期进行有效的优化以及进度控制，这对项目管理目标的实现具有实际意义，同时也可以为W公司甚至其他汽车制造性企业的产品开发项目提供参考意义。

# 1.2 国内外研究现状

# 1.2.1 国内研究现状

相较于国外的项目进度管理的研究，我国的相关研究直到20时机 50年代才开始起步，进度管理方法最先在我国的建筑以及水利工程项目中推广和应用。其中，“统筹法”因为其网络计划统筹兼顾、全面规划的特点得到了大量的应用并取得了显著的成绩。我国开始对网络计划技术进行真正意义上的研究是在20世纪80年代，主要有两种方法：一种是将关键路线法（CPM）的方法应用于工程项目,特别是在80年代初期的阶段，国内学者将这种方法应用于核电厂的进度控制，取得了很好的效果。这种方法主要是根据工程项目所处环境的特点，编制工期估算表和进度计划，再根据工期估算表和进度计划进行控制。这种方法不仅考虑到了关键路线上的资源需求、组织、进度安排等问题，而且还考虑到了整个项目的时间和费用要求。另外一种方法是将其应用于一般工业项目和农业项目等，以技术为基础，在较短的时间内完成一定数量的产品或服务。随着进度管理的发展和应用，我国的大中型工程建设项目大规模使用网络计划技术对项目资源进行合理化安排、进度计划的编制以及进度计划的优化和控制。

2016年，唐玉莲, 张文光认为，项目的进度计划的控制在某种意义上说，是项目成败的关键因素之一。通过有效和科学的项目进度计划以及计划的控制工作，让计划引导实际工作，不仅可以有计划的进行各环节步骤，减少项目的总成本，还能提前完成项目[1]。

2018年，张延深入阐述了开发项目管理中存在的问题，如项目需求易变更、各部门协作性差、风险管理意识淡薄等，他在文中建议必须运用项目管理相关理论和手段，对开发项目进行全流程的指导和监控[2]。

2020年，唐元元, 蔡德明, 黄祖朋, 陈旭探析了项目进度控制过程中的PDCA各环节问题，并对汽车产品开发项目实施中的最常出现的问题进行了归纳和总结，对此提出了相应的项目理论和工具，表述了在出现何种问题采取何种应对措施[3]。

# 1.2.2 国外研究现状

对于项目进度管理的研究与应用，在经济相对发达的西方国家是非常的普遍和成熟的。为了适应项目管理理论的科学发展和对其在实践中的有效应用，国外研究学者自20世纪50年代以来，陆续发现了一些新的管理计划方法。其中最为著名的是关键路径法(Critical Path Method，简称CPM)，由美国学者与著名公司技术人员共同提出，其主要工作原理是使用数字分析显著缩短了工作时间并节约了成本。对它的首次使用是在化工厂的建造和设备维修项目中，这种方法极大的减少了工作时间，节约了费用。随后，计划评审技术(Program Evaluation and ReviewTechnique，简称PERT)在两年后被美国海军军械局提出，针对舰载洲际导弹项目对该项理论进行研究。此项目通过使用计划评审技术，综合分析并权衡了导弹研制过程中的众多复杂的商务合同，有效地协调了其中上千个承包商的关系，不仅使项目完成时间提前，还极好的控制了成本。

经年来，随着项目管理领域的蓬勃发展，国外的研究学者大多聚焦于对进度计划管理中的挣值法以及关键链技术进行不断深入研究。作为进度网络分析技术中的另一重要技术，关键链法(Critical Chain Method)由艾利·高德拉特(Eliyahu M.Goldratt)提出，在关键路线法(Critical Path Method，CPM）的基础上引入了TOC制约法的思想。根据有限的资源对项目表进行调整，由在项目进度管理中获得了积极的应用。MadterD ,NancyP根据提出并处理制定项目进度方案时目标计划的时间和成本间的关联性[4]；Ramos, Moselhi , Osama 几位学者以净值现有框架知识基础，提出精准估算净值法的研究；提出了更精准的控制工期进度的有效方法[5]；WilsonD和James M 两位学者将研究的切入点放在了关键链技术在实际应用中的优势[6]。

David Pierce等人提出了基于以目标为导向的检索方案并通过有限的条件进行检索的方法[7]。这种以利用指定检索知识为基础，对项目进度计划进行工作分配的方法较其他方法更为灵活有效。

John N, Herman S的研究中提出了更具有可操作性的“EVM/LC”挣值法模型，这种新型的实证分析模型能够为没有充分预计到工期的复杂性而造成的工期进度失控现象进行一定的弥补，使之得到较好的解决[8]。

BellahJeremy依据WBS关系网络理论将各个工序环节得到优化，不足的是局部部分得到了很大的改善，但是全局性的统筹规划力度不够[9]。在净值法精确度提升方面的研究,Esmail C, Babak M 分别研究的Planned Value Rate, Earned Durationand Schedule等方法都获得了可喜的成果[10]。

# 1.2.3 发展动态

为了实现并进一步完善项目进度的高效管控，项目进度管理必须依赖于现代的管理理念、高效率的管理机构、科学合理的管理方法以及先进的技术手段，这些都是项目进度管理发展的必然趋势。随着社会经济的不断进步与科技水平的日益提高，在管理理念及管理方法方面，项目进度管理仍然有着很大的提升空间。近年来，涌现出了许多创新性的管理理念，例如TOC（约束因素理论、约束管理）、知识管理体系、灵活适用的柔性管理等诸多领域的先进思想理念。进度管理的发展从古至今，应用范围不断变得广泛，从开始局限于工程建设项目、国防和航天等少数领域，发展渗透到互联网、汽车、医疗、高科技设备、金融、电信、物流运输等各个领域。如今，随着新技术和成功的项目进度管理案例的不断出现，项目进度管理几乎像所有其他行业一样，正在经历重大的变化和发展，并且为项目

管理提供了卓越的贡献。

在80年代初期，我国就引进了一些国外的项目管理软件，如：Project，CACIS，WBS，甘特图等，这一时期主要是为了对国内的项目管理软件进行验证和完善，为后续计算机网络计划技术在国内的推广应用打下基础[11]。这一阶段主要是为了验证和完善国内的项目管理软件，研究如何改进和完善国内的项目管理软件；在80年代中期至 90年代初，我国开始采用计算机网络计划技术对大型工程项目进行进度控制，如：三峡工程、西气东输工程等。由于我国大型工程项目具有投资大、工期长、技术复杂等特点，因此在这些工程项目中使用网络计划技术进行进度控制具有非常重要的意义。90年代中期至 21世纪初：我国开始大力推广计算机网络计划技术在民用建筑工程中的应用。

项目进度管理的发展主要经历了计划与控制、设计与管理和项目管理三个阶段，在这个过程中，我们的管理方式从传统的计划与控制转变到现代的计划与控制上来，同时也是一个不断完善和发展的过程[12]。

# 1.3 研究内容与方法

# 1.3.1 研究内容

本文的主要内容是利用项目进度管理、项目进度计划和项目进度控制理论与方法对W公司集成热交换新产品开发项目进行了深入系统的研究。从W公司集成热交换新产品的项目要求和项目特点入手，充分考虑到 W公司开发项目的进度管理流程和项目进度中的风险点，给出了适合 W公司新产品开发项目的进度计划、项目进度优化方案。具体研究内容包括如下 3个方面：

（1）W公司汽车集成热交换产品开发的项目流程与项目特点的分析。这部分内容主要通过对新产品开发项目的介绍，以及此项目的重要性进行分析，找出影响项目进度的风险点，进而在向后的项目进度计划制定中避免风险。

（2）将项目进度管理、进度计划和进度控制的各项理论方法运用到W公司的开发项目进度管理工作中。在对开发项目的各项工作内容进行分解后，确定其关键路径的确定基础上，运用Project 软件制订出项目的进度计划甘特图。

（3）对W公司新产品开发项目进度计划进行控制与保障。主要从动态监测偏差分析、偏差调整、以及保障措施的建立这几个方面对W公司的开发项目进行监测控制与保障。

# 1.3.2 研究方法

（1）文献研究方法

综合研究国内外相关理论研究与借鉴东北大学图书馆内丰富的资源，以建立准确认识事实科学的方法。依据论文的研究目标与需求，广泛的阅读专业书籍与文献、关注报刊杂志及相关研究，深度研究所提出的问题。

# （2）实证调查与分析方法

通过对W公司的深度研究与项目组资深成员的咨询与前期项目的调查，获取大量宝贵的资料。对这些资料进行分析与整合，对W公司新产品开发项目进度计划进行科学制订，作为本文关注的事实基础，为后续探讨W公司项目进度计划优化与控制提供坚实基础。

# （3）案例分析法

选取专业的合资汽车企业进行案例分析研究，以得出去油代表性的结论。本文所选择案例企业W 公司，是全球范围内知名的豪华汽车企业，历史悠久，技术经验丰富，且较早的引入了开发项目进度管理的概念，因此具有极强的代表性。

# 1.4 论文的章节结构

本文共分六章。首章为绪论，概括了研究背景、意义以及国内外研究现状，明确了研究内容和方法，并综述了各项理论和框架；第二章为相关理论与方法概述，主要是概述论文中涉及的主要理论与方法，包括项目进度管理、项目进度计划、编制步骤、编制方法以及项目进度控制等；第三章介绍W公司汽车集成热交换新产品开发项目概况，详细介绍了W公司及新产品项目，通过深入分析现有开发项目流程及特点，得出项目进度影响因素；第四章W公司汽车集成热交换新产品开发项目进度计划的制定，根据第三章得出的影响因素，针对性地提出项目工作范围、工作分解、排序、逻辑关系和持续时间估算，以编制项目进度计划；第五章W公司汽车集成热交换新产品项目进度控制与保障措施，根据优化后的项目进度计划，进一步考虑进度计划的控制与保障措施，并对项目进度进行动态监测和偏差调整；最后一章为结论与展望，全面的对本文的主要研究成果和对未来研究展望进行总结陈述。

本文主要的创新点在于：

首先，W 公司属于国内较大型的汽车生产制造合资企业，目前已经形成一定的规模，其技术研发团队一直在德国总部，因此在过往的新产品的设计、性能和可靠性验证的周期较长，从立项到产品量产通常需要3至5年时间。然而在当前新能源市场中的紧张激烈的竞争环境下，较多国内汽车企业的研发周期甚至在 1年之内。因此W公司要求此新产品的开发周期小于2年，且要求各项试验验证均按不可减少，因此对此次开发项目的进度计划的制订、实际执行以及进度控制的过程都要求十分严苛，以提高集团在新能源汽车领域竞争力。

其次，此集成新产品是针对新能源汽车里程、安全性、舒适性劣势而研发的新产品，在汽车领域无其他主机厂的技术借鉴，包含较多的下级电子、机械子部件，针对电机电控系统、电池系统、乘用舱系统进行热交换的极其复杂新产品。涉及德国总部、中国整车厂和一级供应商以及遍布全球各地的下级子部件和关键原材料的供应商。因此需要非常紧密的沟通合作，以及项目组科学有效的管理，用以应对多方参与、时差以及跨地区合作的影响，切实保证开发项目顺利进行，且满足项目要求的量产时间以及集团的各项质量要求。

# 第2章 相关理论与方法概述

# 2.1 项目进度管理概述

# 2.1.1 项目进度管理的定义

项目进度管理是对进度目标和进度计划进行科学的制定，调配资源以实施进度监控，确保工期目标得以实现，同时与项目成本、质量目标保持一致。项目进度管理的主要目标是要在规定的时间内，制定出合理、经济的进度计划，然后在该计划的执行过程中，检查实际进度与计划进度是否一致，保证项目按时完成[13]。项目进度管理项目管理中不可或缺的一环，它与项目投资管理、项目质量管理等共同构成了项目管理的核心要素。项目进度管理在确保项目按时完成、优化资源配置以及降低工程成本方面发挥着至关重要的作用。

在项目执行过程中，控制保障各项工作的进度至关重要。项目进度管理在确保项目按时完成、优化资源配置以及降低工程成本方面发挥着至关重要的作用。一旦发现项目进度出现偏差，应立即查找原因并采取必要的纠偏方案，或者调整、修改原来的项目进度计划，直至项目顺利完成[14]。

# 2.1.2 项目进度管理的内容

项目进度管理的内容基本上包括：项目活动定义、项目活动排序、项目活动时间、项目进度管控、项目活动资源估算等[15]。上述的几个内容总是相互影响同时也相互帮助的，形成十分紧密的关系。

在项目的具体工作开展以前，必须制定出科学其合理的项目进度计划，并依据此项目进度计划进工作推进。搜集分析项目背景资料、项目工作任务的拆分、逻辑关系的确定、以及项目工作的时间推算是项目进度计划制定的几个步骤。在项目进度管理中，在项目工作的实际推进过程中，难免被外界环境和各种项目条件的变化所影响，产生了项目进度偏差。针对这些偏差，应立即分析并纠正，以免对项目进度产生无法挽回的后果。由此可见，项目进度计划的控制在项目进度管理中尤为重要。对项目实施进行合理规划、有序的组织、科学的引导和有效的控制，让项目管理能够实现动态化和全程化，最终，按照既定的规划，完成工程目标[16]。在具体应用的过程中，需要根据项目的实际运行进行工作内容分解，为

后续的工作开展做好基本的保障。

# 2.2 项目进度计划

# 2.2.1 项目进度计划的概念

项目进度计划亦称“进度计划”，是将满足项目目标期望的任务、分配、事件的进度列出的计划[17]。项目进度计划的内容是多样性的，它可以简明扼要的呈现也可以详尽细节的表达，有时也称为主进度计划或里程碑进度计划。项目进度计划的表现形式多样，既可以采用表格式，也可以运用图示法。具体来说，有以下几种：

（1)项目网络图。这种图示方式能够清晰地显示项目工作之间的前后逻辑顺序，并同时呈现项目的关键路径及其相对应的活动，且包含明确的日期信息。(2)条形图。也可称之为“甘特图”是一种非常易读的直观显示图。该图可以显示活动开始以及活动结束的日期，甚至可以呈现明确的预计活动时间，但是对于各项活动间的相关性无法显示。(3) 重大事件图。它与条形图极其相似，能够明确的展示出主要工作任务以及其起始时间信息。(4)含时间尺度的网络图。结合项目网络图和条形图的后，可展示项目的前后逻辑关系、各项活动所需时间和进度方面信息。

项目进度计划最为显著的特点就是具有严格的时间期限要求，可以清晰的控制各项工作的起始时间，有效的帮助项目控制甚至节约时间，因此，项目进度计划在项目管理中起到了相当重要的作用。项目进度计划的优势在于可以保障按时实现收益以抵消已发生的费用支出，协调资源并确保资源在必要时得以利用，预测不同时间所需的资金和资源水平，为项目设定不同的优先级，已到达满足项目时间的要求。

# 2.2.2 项目进度计划的步骤

项目进度计划的制定需立足于项目范围管理，依照项目范围规定内容，精确设置各项活动。项目目标范围、工期要求、项目特性、内外部环境、项目结构拆分、各任务时间估算及资源供应是项目进度计划编制主要依据。此外，为了确保项目目标的达成，进度计划需协调成本、质量以及安全等目标，并且充分考虑客观因素与潜在发生的风险。进度计划编制主要工具是网络计划图和横道图，通过绘制网络计划图，确定关键路线和关键工作[18]。

在制定项目进度计划的首要任务中，首先需要全面解析项目的结构。基于项目范围的详细需求，对整个项目的结构构成进行深入的剖析。随后，针对这些活动进行规划，这其中包括对实施过程和工作细节的考虑，以及对项目的系统化分解，从而确保项目计划既全面又细致，为后续的项目执行奠定坚实的基础。 项目结构分解是以工作分解结构（WBS）为原理，项目按照其内在结构和实施过程的顺序进行逐步分解，从而形成结构示意图，通过运用WBS分解，可以将项目分解成结构相对独立，内容相对单一且易于成本核算与检查的项目单元，明确单元间的逻辑关系与工作关系，并落实每个单元的责任人，进而实现各部门、各专业的有效协调[19]。计划编制时也考虑到各种风险的存在，使进度留有余地，具有一定的弹性，进度计划优化时，可利用这些弹性，缩短工作持继时间，或改变工作之间的搭接关系，确保项目工期目标的实现[20]。

其次组建一个高效的进度控制管理小组也尤为重要，该小组应由项目经理担任组长，各职能部门负责人担任副组长，并由各具体工作负责人担任组员。明确每位控制小组成员的分工与职责，确保责任到人。通过定期或不定期召开会议，严格遵循讨论、分析、制定方案、迅速执行和反馈的工作流程，旨在建立起一个高效协作的项目管理团队。这样的团队能够迅速有效地解决项目进程中出现的各种问题，确保项目按计划顺利进行。

最后，运用控制系统、动态调整、封闭循环、信息及弹性原理，制定并持续实施控制保障措施。这一过程从计划的制定开始，经过实施阶段，再到调整优化，形成一个完整的循环，直至最终目标的达成。在这个过程中，信息的传递与反馈是不可或缺的环节，它们贯穿于计划实施与控制的始终，确保整个系统的高效运转。 整体项目进度计划步骤如图2.1所示。

![](images/b34d9bdf29a55f8801cbe2f2e28fea1d17c4f7259b8940c3089635c9e993bd9d.jpg)  
图2.1 项目进度计划步骤 Fig.2.1 Project schedule steps

# 2.3 项目进度计划编制的方法

# 2.3.1 WBS 工作分解法

工作分解结构（简称WBS）跟因数分解是一个原理，就是把一个项目，按一定的原则分解，项目分解成任务，任务再分解成一项项工作，再把一项项工作分配到每个人的日常活动中，直到分解不下去为止[21]。WBS将项目逐层细化为任务、工作和日常活动，以可交付成果为导向，明确项目工作范围。作为计划核心，WBS是制定进度、资源、成本、风险和采购计划的关键基础。

工作分解结构以可交付成果为导向对项目要素进行的分组，它归纳和定义了项目的整个工作范围每下降一层代表对项目工作的更详细定义[22]。工作分解结构（WBS）是项目管理和PMP考试的核心内容。它是规划过程的基础，支撑着进度、资源、成本、风险和采购计划，同时帮助控制项目变更和界定项目范围。

分解原则包括:（1）每个任务应分解到无法再细分（2）将目标细化至可直接分配给个人的日常活动（3）日常活动需要明确落实到个人、持续时间以及成本。

任务分解的方法包括:（1）利用树状结构对任务分解（2）通过由上而下的、由下而上的和团队为中心的充分有效的沟通来进行工作分解。

任务分解的标准包括:（1）分解后的活动结构清晰明确，一目了然，坚决避免复杂交错的情况（2）逻辑上形成大型活动，集成所有关键因素，包括临时里程碑和监控点，确保所有活动均被详细定义，细化至责任人、时间和资金投入。在日常项目管理中，任务分解至关重要。为了有效控制项目进度，任务需被分解得足够细致、明确，以便全局统筹，合理调配人力和财力资源。

# 2.3.2 甘特图法

甘特图是以作业排序为目的，将活动与时间联系起来的最早尝试的工具之一，帮助企业描述工作中心、超时工作等资源的使用, 因为其简单、醒目、便于编制，而在管理中广泛被使用[23]。依据内容进行分类，甘特图分为计划图表、负荷图表、机器闲置图表、人员闲置图表以及进度表五种形式。其包含的三层含义如下：（1）以图表形式对活动进行呈现；（2）普遍适用于进度展示方法；（3）构建时包含日历天数及持续时间，不将周末和公共假期计入其中。

甘特图的特点在于鲜明的表达了生产管理中核心的因素—时间，它的作用主要体现在三个方面：（1）直观地展示了计划产量与计划时间之间的对应关系（2）清晰地对比了每日实际产量与预定计划产量的差异（3）准确地反映了在一定时间段内实际累计产量与同期计划累计产量的对比关系。

甘特图的优势在于其图形概要和通用技术，十分便于理解，而局限性来源于其部分地反映了项目管理在时间、成本以及范围的三重约束。它主要关注的是工作进程的管理时间，故此存在局限性。此外，软件的功能亦存在不足，尽管能够展现项目活动的内部关系，但当关系繁多时，甘特图的阅读难度将随之增加。甘特图被广泛应用在现代的项目管理中，它可以预测时间、成本、数量及质量上的结果并回到开始[24]。甘特图同时也具备考虑人力、资源、日期、项目复用要素及关键部分的功能，以直观方式展示任务进展状况和资源利用率等信息。随着生产管理和项目管理的不断发展，甘特图不仅限于在生产管理领域在的应用，在建筑工程、软件开发、汽车制造等多个领域也得到大量的应用。甘特图的具体形式，如图2.2所示。

![](images/e124af9ffa1757394cd4c00783b0c5f3b1d823ca7fcbda2782744c3a6937a698.jpg)  
图 2.2 项目开发甘特图  
Fig.2.2 Project Development Gantt Chart

# 2.3.3 关键路径（CPM）法

在项目过程中，从输入到输出经过的最长逻辑路径就是关键路径。针对该关键路径进行优化可以显著提升项目工作速度。通常情况下，依据信号在最大延迟路径中所经过的时间，与其他短路径无关。在设计优化的过程中，可反复运用关键路径法，直至无法降低关键路径总体时间为止，EDA 工具中综合器及设计分析器通常向设计者提供关键路径的信息以便于优化提高速度[25]。

关键路径法即通过寻找关键路径及其时间长度来确定项目的完成日期与总工期的方法。根据绘制方法的不同，关键路径法可以分为两种：即箭线图（ADM）和前导图（PDM）[26]。箭线图又称作双代号网络图法，以横线表示活动，并通过带编号的节点关联，活动符合结束到开始的逻辑。前导图以节点代表活动，以节点间的连线表示逻辑关系，活动间可存在四种逻辑关系，即结束到开始、结束到结束、开始到开始以及开始到结束。绘制单代号网络图时主要有以下一些规则：

(1) 必须准确的表达既定的逻辑关系；  
(2) 严禁存在循环回路；  
(3) 严禁出现双重箭头或无箭头的连线；  
(4) 禁止出现无箭尾节点的箭线以及无箭头节点的箭线;  
(5) 箭线不宜交叉，若无法避免，可采用过桥法或指向法绘制。

![](images/c85686b81ab794f02ac3661478007761369936a40a6e0892eeb48daa5ec5cc8c.jpg)  
图2.3关键路径法 Fig 2.3 Critical Path method

# 2.4 项目进度控制

# 2.4.1 项目进度控制定义

项目进度控制（Project schedule control）主要控制工程活动，它包括项目结构图上各个层次的单元，上至整个项目，下至各个工作包(有时直到最低层次网络上的工程活动) [27]。项目进度状况通常以各工程活动完成度的百分比来衡量。鉴于工程包含多种子项目和工作包，其性质各异，因此选择合适的进度指标对进度描述、计算和控制至关重要。为确保一致性，需选定适用于所有工程活动的计量单位。进度控制管理则通过科学手段设定进度目标，编制相应的进度计划和资源配置计划，并实施有效的进度监控，以确保工期目标在质量、成本和安全目标相互协调的基础上得以实现。

在进度执行过程中，由于目标明确但资源有限，加之众多不确定性和复杂的干扰因素（包括客观和主观因素），以及主客观条件的持续变化，项目计划往往需要相应的调整。因此，项目开展时必须持续监控进度计划的执行情况，并与计划内容进行实时对比。一旦发现偏差，应立即采取有效的纠偏措施，确保项目进度能够按预期目标稳步推进，最终实现项目的整体目标。

# 2.4.2 项目进度控制原理

项目进度控制原理可以分为动态原理、系统原理、封闭原理、信息原理以及弹性原理种控制原理[28]。动态原理指出，项目执行是一个不断变化的过程。进度控制需贯穿项目全程，管理者应分阶段制定进度规划，实时监控并适时调整。系统原理认为，项目计划是各单位、阶段、部分和层级相互关联的系统，进度控制需采用系统方法。封闭原理指出，进度控制是编制、执行、检测、分析、调整、修订的循环过程。信息原理强调，信息是进度控制的基础，需构建有效的信息系统。弹性原理则要求在制定计划时考虑灵活性，以应对复杂多变的项目环境。

# 2.4.3 项目进度控制方法

项目进度的控制方法有很多种，但其宗旨都是为了项目进度的实施可以依据前期编制的项目进度计划顺利的执行，减小甚至有效的避免项目实施阶段遇到的各种风险[29]。项目进度控制的核心在于实践过程中所采取的各类措施，项目进度控制包含四大措施：组织、技术、经济和管理。组织措施需要明确人员职责、构建控制体系、制定工作制度，并监控计划执行；经济措施的目的是确保资金和资源供应，实施激励机制。技术措施意在加快项目进度进程，管理措施加强合同管理、信息管理等，协调各方利益。

控制方法种常用的是项目进度的动态监测，通过日常和定期观测记录工作进展和资源消耗。比较前言的控制方法有横道图比较法，它可以的直观对比实际的进度情况与计划进度，为分析偏差并调整计划提供有效的控制手段，进而对所涉及的工作内容进行调整，亦或是逻辑关系变更甚至计划重编和资源调配。

# 第3章 W 公司汽车集成热交换新产品开发项目概况

# 3.1 W 公司概况与开发项目流程

# 3.1.1 公司简介

W公司是一家成立于 2003年的中外合资企业，其注册地和生产厂设在辽宁省省会沈阳市，拥有员工约 23,000人，国内零部件及原材料供应商430 家，已经连续16年蝉联沈阳最大纳税企业榜首。自2010 年起，W公司在华总投资累计已超945亿人民币,创造了超26,000个高品质工作岗位，以实际行动践行“家在中国”的发展策略。如今，W 公司集本土专业的研发、采购、生产部门为一体，拥有独立的研发中心，电池生产工厂以及 3家整车装配工厂，在华年总产量可达83万辆，是当前 W 集团全球最大规模的生产基地和最为重要的新能源汽车生产制造产地之一。2021 年，W 公司生产销售了超过 70 万辆汽车，首次成为中国豪华车产量最大的汽车企业。

以W集团 iFactory 生产战略愿景为指导，沈阳工厂结合数字化技术和先进的5G通信、虚拟仿真以及人工智能技术，在确保高品质的同时带来高度灵活性。为应对电动车市场的快速增长需求，沈阳地区三个整车生产基地均可以根据市场需求灵活调整纯电动和燃油车型的生产。W集团打造专业电动车生产的“工业元宇宙”工厂， LD 工厂在沈阳市铁西区于 2022 年 6 月正式成立。W 集团致力于打造贯穿于产品全生命周期的“最绿色的电动车”，而沈阳生产基地也已为此做出表率。W 公司使用 $100 \%$ 可再生电力，还通过电动卡车短驳、铁路海运等低碳整车联运、绿色仓储等方案，打造绿色物流体系。未来，W 公司全价值链“循环减碳”理念将聚焦于打造绿色供应链，已在中国为此成立专门项目组，根据中国的国情更加科学地制定和执行“中国供应链减排计划”。从2014年第1 台本土生产的混合动力新能源车型量产,到如今 2023 年 4 款纯电车型批量生产，年能迅速发展从年产2,000台到如今的年计划生产纯电车型 20万台，充分的体现了集团对大步迈向电动化、数字化、循环永续的未来出行策略的决心。打造更优性能、更长续航、数字化和舒适性的新能源车型，力求再度诠释 W品牌在电动车市场与传统燃油车市场一样的品牌定位。W公司在中国沈阳地区拥有四个专业汽车及汽车发动机生产制造工厂，如图3.1。

![](images/836a9471047fd24c4dd075c5dff3c2d290ab54fcc0b7c3b591dbc00f1266613b.jpg)  
图3.1 W 公司生产制造工厂图Fig.3.1 Production Plants of Company W

# 3.1.2 开发项目流程

汽车新产品开发的流程为项目评估、技术方案确定、供应商选择、生产线模具准备、样件的生产制造、样件测试、量产审核以及正式的批量交付。

W 公司的新产品开发项目需要在公司内部进行立项审批，开发项目一旦批准，项目组便需要成立并且迅速指定开发项目计划，根据公司管理层以及销售部门制定的发展策略和市场分析制定关键的项目里程碑，协调项目涉及的各个部门，并取得各部门管理层的支持。其开发项目一直秉承着四叶草合作模式，在公司内部的项目开发中合作，确保项目关键信息的传递有效且一致。项目组制定详细有效的项目计划和责任分工计划进行项目的具体工作，在项目工作开展过程中，定期的像项目监管会进行进度汇报，如遇到突发事件或者工作推迟，及时升级至W公司高层领导层，以取得及时有效的解决。

在技术图纸设计阶段，引入采购、质量、物流以及零部件生产商的技术团队，一同进行定期的会议，以保证商务合同、后期质量稳定保证、供应稳定以及生产制造的稳定。样件通过各项标准测试后，由质量部门对供应商进行生产现场的审核，以确保零部件生产制造过程的稳定性。新产品的产能与模具经过采购部门现场确认后记录至模具产能管理系统。物流部门负责人就产品的物流包装和发货进行审核确认，并释放量产审核文件。至此，整个新产品通过各部门审核要求后，整个新产品的开发项目结束。

# 3.2 W 公司汽车集成热交换新产品开发项目概述

# 3.2.1 项目背景介绍

此项目从2023年 6月1日开始启动，历经图纸设计冻结，供应商选择与商务合同签署，零部件生产设备、卡具模具到厂调试以及供应商端生产制造，试验验证，小批量认证，以及量产审核批准等诸多任务，需要在 2025年3 月15日前完成国内批量供货。设计峰值产量年为 2026年产量四十九万支，项目周期 5年，项目周期内供应共计两百二十万支 。如果项目无法在计划时间内完成，W 公司的新能源车型将失去里程竞争力，无法保持目前的市场份额，因此项目对整个集团公司具有相当大的挑战，“集成热交换新产品”的开发项目中实施进度计划与控制就成为该项目成败的关键。

目前W公司的新能源车型并无此集成热交换产品，水泵、温度传感器、电子阀门以及电子电气控制箱分布在新能源车内各个位置，通过较长的管路以及电子电气件相连，信号捕捉后传递至水泵时间慢、能量损失高且仅限于冷却功能。新开发的热交换产品充分集成化，节约了长距离传递过程中的能量损失，有效的提升了热交换效率，节省空间。其产品样件如图3.2。

![](images/f49520d3cb804d1f0e7754e8cb971d7ed53cb997087aa7dee61b1a8281ae5286.jpg)  
图 3.2 新产品样件示意图  
Fig.3.2 Diagrammatic sketch of new product

此新产品的创新点在于搜集电池包内工作时产生的热量，并通过电子阀门传递给需要温度升高的客舱内部，统筹控制电池、电机以及电控与外部环境的温度的交换，从未确保各电子部件在最佳温度范围内工作，有效的提升电动车各方面的性能，提升电动车的行驶里程。因从产品内部系统也较为复杂，内部系统如图3.3。

![](images/266c4de9b91d4fdc56ef65c4d6a5acad2b4b8f86b58c0da0a9c60c01216c593a.jpg)  
图3.3 产品系统示意图  
Fig.3.3 Diagrammatic Sketch of Product System

因为此开发项目的新产品特性在于将多个专业的专业子部件高度集成，因此所选定的一级生产制造供应商需要在汽车行业具有相当专业的研发、装配以及下级供应商管理能力，项目管理团队可以完美的对接 W公司项目组内各部门的专业人员，以达到此开发项目满足客户质量要求并按时量产交付。

# 3.2.2 项目重要性

集成热交换新产品的开发是基于整合整车内外部热量交互的目的，从电子元气件本身的最佳温度范围出发，综合控制并调节零部件间的热量与外部环境间的热量，最终达到各电子元器件在最事宜的温度下进行高效工作；较传统燃油汽车相比，电动车缺少冷却以及空调系统热管理系统进行大范围的温度控制，其电机、电控、电池以及乘员舱空调的热管理仅仅依靠热交换产品，之前的设计是热交换系统分散在整车的各个位置，热量传输过程过长导致了能量的浪费，甚至影响电车的行驶里程。集成热交换新产品作为新能源车型提升车辆纯电里程，保障车辆行驶安全，提升客户舱内舒适性的核心产品，受到了 W公司内部高层领导的一致重视，因此开发项目得到了公司内部各个部门的积极配合。项目组由项目经理管理负责，联合设计部门、试验部门、采购部门、质量部门、生产部门以及物流部门在立项前期开始介入，以确保项目顺利量产交付，c 为公司核心的换代新能源车型提供有效的技术升级换代产品。

2025 年是新能源车型的转折点，在这关键的一年欧盟停止对商用燃油车的生产制造，汽车企业需要承诺其供应链保证 $100 \%$ 使用绿色能源生产制造，因此 W公司的核心技术研发产品集成热交换产品的开发项目尤为重要，这将决定让W公司在新能源汽车市场上重回燃油车时代的夺目地位，能否在下一个汽车时代争取到生存之地。

# 3.3 项目特点以及项目进度影响因素

# 3.3.1 项目特点

（1）项目开发时间紧迫

W 公司属于国内较大型的汽车生产制造合资企业，目前已经形成一定的规模，其技术研发团队一直在德国总部，因此在过往的新产品的设计、性能和可靠性验证的周期较长，从立项到产品量产通常需要3至5年时间。然而在当前新能源市场中的紧张激烈的竞争环境下，较多国内汽车企业的研发周期甚至在1年之内。因此W公司要求此新产品的开发周期小于 2年，且要求各项试验验证均按不可减少，因此对此次开发项目的进度计划的制订、实际执行以及进度控制的过程都要求十分严苛，以提高集团在新能源汽车领域竞争力。

（2）项目涉及较多一级、二级、原材料供应商

此集成热交换新产品是针对新能源汽车里程、安全性、舒适性劣势而研发的新产品，在汽车领域无其他主机厂的技术借鉴，包含较多的下级电子、机械子部件，针对电机电控系统、电池系统、乘用舱系统进行热交换的极其复杂新产品。涉及德国总部、中国整车厂和一级供应商以及遍布全球各地的下级子部件和关键原材料的供应商。因此需要非常紧密的沟通合作，以及项目组科学有效的管理，用以应对多方参与、时差以及跨地区合作的影响，切实保证开发项目顺利进行，且满足项目要求的量产时间以及集团的各项质量要求。

（3）集成热交换新产品无相似产品可借鉴

将水泵、温度传感器、电子阀门以及电子电气件集成是新能源市场上首次创新的产品，其技术设计复杂且性能参数要求严格，且无法借鉴其他新能源整车厂的产品设计、试验标准、供应商选定以及生产装配等关键项目步骤。因此，整个新产品开发项目的进度管理需要项目组科学有效的把控。

# 3.3.2 项目进度影响因素

影响项目进度的关键因素有很多，特别是这种复杂性高，涉及面较为广泛的技术性开发项目，受到诸多外界影响因素。

（1）沟通方时差影响

项目需求与审核均需要来德国总部批准，沟通受到时差影响比较大，且大多数子部件供应商设计部门均在欧洲，多方沟通时会议时间受限。基于此，需要项目各方的重视与合理详细的进度安排，充分考虑时差因素，提前确定周会、月会时间、频次、内容以及会议目标，明确落实负责人，确保每次会议各方负责人均按时参加并保证输出内容,各类项目会议安排如图3.4。

![](images/a0e5021db3b861e6cd8c328e846b463eaef4a9991003f2d0c23be84edd7b687c.jpg)  
图3.4 项目会议安排图  
Fig.3.4 Project Meetings Arrangement

（2）供应商端项目管理意识薄弱

项目进度管理是基于庞大的项目进度管理系统，所有人都有处理和监控的责任。缺乏深入的认识和科学的规划将对企业的盈利产生直接的影响，项目的实施是由人去具体实施的，而供应商端的项目管理小组对于涉及面如此之广的项目管理意识还比较薄弱。比如说，供应商端大部分主管工程师都是兼职作为开发项目的成员，他们在开发集成热交换产品的同时，也被其他的客户的产品消耗着时间时间和精力，这势必影响着他们的工作效率，分散了他们的关注力。与此同时，供应商端的项目开发团队缺乏项目管理方面的专业人士，当遇到棘手的问题时，普通的团队成员没有解决冲突的经验。还有就是供应商端的专业技术人员跳槽比较频繁，领导层也没有非常重视离职率，频繁更换项目组成员，导致项目工作连续性较差，配合工作也缺乏默契，以至于整体项目工作效率遭受影响。

# （3）新产品设计要求严苛

由于电动汽车的驾驶舱、电池包、电机以及电控都需要热量的交换，以确保不同的零件都能不同温度下进行高效的工作，从而保障电动汽车的安全性能，增加电子元器件的使用寿命，提升电动车的行驶里程和舒适度，因此集成热交换产品需要通过频繁且准确的散热、吸热、加热、传导热以及保温等方法来达到这一目的。为了达到在不同温度下精确的对多个部件进行热交换功能，需要对新产品的流量与压力值进行持续的监测，任何一项数据不达标，都将影响开发项目的进度，因此需要在图纸设计阶段充分考虑各项技术指标，具体的技术要求如图3.5。

![](images/a941fe5432eba56e104adf1dbc3801869a5539aae8a3706b0da388ddf8d17691.jpg)  
图3.5 不同温度的流量与压力值要求  
Fig.3.5 Volume and pressure figures in different temper

# 第4章 W 公司汽车集成热交换新产品开发项目进度计划的制定

# 4.1 项目工作范围与组织结构

# 4.1.1 项目工作范围

项目名称“W公司汽车集成热交换新产品开发项目”，开发项目时间范围为2023年6月起至2025 年3月15日止。项目重点要求是汽车集成热交换产品量产技术图纸释放，并依据技术要求选定合格供应商，所选定的供应商具备符合W公司质量体系要求的量产生产线，并对产品进行批量交付。

项目规划产能要求为，达到年度连续生产交付 49 万支集成热交换产品的产能，日产能19179个，每小时连续稳定生产合格零部件91个。每周连续生产不得超过6个工作日，年度范围内不超过300个工作日。

项目技术要求为所交付的产品符合W公司研发部门释放的图纸要求，并通过技术文件内标注的各项功能测试，新产品符合汽车行业法律法规以及 W公司安全零部件技术规范要求。项目的商务要求为与选定一级供应商完成商务合同的签署，监督一级供应商选定关键子部件供应商，保证一级供应商与子供应商的商务合同在项目进度计划内完成签署，并保证一级供应商与关键子部件供应商接受W公司的质量索赔条款。W公司对一次性支付的专用设备以及工装卡具具有所有权，对此类工装卡具进行商务合同的签署与生产制造过程进行监督。

W公司的研发部门对开发产品的技术图纸进行释放，供应商质量管理团队负责依照技术图纸要求对此开发项目进行量产前的供应商生产现场审核，并对审核通过的供应商予以释放量产审核批准文件。采购部门负责人将对W公司所投资的生产线设备以及工装卡具进行现场审核，确认供应商连续生产开发新产品的产量能力，并予以释放产能确认文件。物流部门负责人将对产品生产现场的器具流转以及产品包装和物流运输流程进行现场审核，审核通过后将释放物流包装确认文件。项目的范围说明如下图4.1所示。

![](images/747e9a2ed17fe6a7093557108f49dfd1e4affc92fff06c9f85f5161c9f53fa29.jpg)  
图4.1 项目范围说明书 Fig.4.1 Statement of Scope for Project

# 4.1.2 项目责任分配

项目的客户为W公司铁西工厂总装车间，业务部门有采购部门、质量部门、研发部门以及物流部门共同协助开发项目，确保项目按计划的要求完成量产交付目标。具体组织责任分配如图4.2所示。

![](images/a1ac6dfaf6379947712c0d0faf350cba6fc0897032b2be0296a68f0737ee3b28.jpg)  
图4.2 项目部责任分配结构图Fig.4.2 Project Organization Chart

（1）集成热交换新产品项目经理（1人）

项目经理在整个新产品的开发项目中负主要管理职责，是首要负责人，因此根据开发项目的流程规定，此岗位必须有公司五级经理以上职位的员工担任，负责统筹把控整个开发项目的管理工作，其中包含项目进度管理、安全管理、开发费用的调整与控制以及开发产品的质量验收，项目经理应高度关注项目的实施进度以及问题解决，对项目组成员进行组织、调配和管理，特别是对选定的供应商负责人和公司内部的项目团队进行沟通协调，评估开发项目的技术质量风险，汇报项目进展与获取更高管理层的支持与资源调配，以却保开发项目的进度在计划时间内完成。

（2）研发组岗位职责（3人）

开发项目中的研发部门负责根据产品功能要求，绘制可进行批量生产的技术图纸，并根据电动车行业的安全技术规范对零部件进行法律法规的细化明确，在整个项目的开发过程中，与供应商技术人员对接确认图纸状态，在样件装配与测试中，逐步更新技术尺寸以确保产品技术状态的稳定性和生产性，技术变更的频次和时效影响着整个项目的进度管理，因此需要有专业的研发同时对技术变更进行记录评审。

（3）质量组岗位职责（3人）

负责整个开发项目的零部件质量保证工作，确保所选定的供应商生产过程可以达到W集团的质量要求，产品功能满足释放图纸要求的各项技术阐述，同时供应商可以稳定持续的生产出质量合格的零部件，并通过现场审核对供应商的量产能力进行确认并释放质量审核文件。对于技术变更后的生产现场，质量组工程师需要进行重新的生产制造过程审核。

（4）采购组岗位职责（3人）

负责整个开发项目的供应商比价、选定以及合同签署工作，根据采购部门的供应商管理流程，对潜在供应商进行考察审核后进行价格比较，通过供应商评审委员会审批后，与选定的供应商进行商务合同签署，量产审核确保供应商按进度计划完成开发项目。

（5）物流组岗位职责（3人）

负责对供应商内外部的物流规划进行LEAN 的管理审核，确保供应产品的包装方案对零部件起到充分的保护作用，应对供应商送货到 W公司的流转过程进行管理监督，保证物流运输的时效性。对样件阶段的一次性包装与量产阶段的循环包装方案都需要进行技术方案确认和过程监控。

# 4.2 项目工作分解、排序与里程碑设定

# 4.2.1 项目工作分解

项目工作分解(Work Breakdown Structure，WBS)是一个将项目的关键元素与可交付物的指导相结合的过程，它总结并定义了项目的总体范围，每下降一层都代表了项目工作的更详细的定义[30]。WBS（工作分解结构）是项目开发进度软件管理的基石，它为项目的实施与管理提供了关键方法，并在项目进度管理中扮演着举足轻重的角色。项目进度计划的编制以WBS为基础，从定义活动开始，贯穿整个项目实施过程，直至项目圆满完成。WBS的分解优势在于，它能将项目细化成更小、更易于管理的单元，形成类似树状的结构，每个单元独立且内容明确，便于控制和管理。 WBS 的分解直接影响整个项目活动的完整性、可靠性和整个项目的完成[31]。

通过对 W 公司集成热交换系统开发项目进行梳理，我们将整个项目划分为 8个一级子任务，分别为前期规划、产品图纸设计、商务合同、生产线布局及工装卡具安装、样件生产与交付、主机厂装配与测试、小批量生产与文件准备以及主机厂量产放行，再通过进一步细化，将每个一级子任务划分为 2-5 个不等，累计28个二级具体工作，并对每个具体工作进行研究。

（1）前期开发阶段

公司项目组在前期进行调查研究，对市场上的其他主力新能源车型进行分析对比以及新能源车型的客户满意度调查，识别热交换新产品开发的必要性，并对项目经济收益进行初步核算, 形成项目调研报告经由公司管理成批准后正式立项，随后进行可行性研究报告的编写与新产品的功能设计。

（2）产品设计与商务合同签署阶段

将新产品按其技术功能划分为电机、电控热管理系统，电池热管理系统以及乘用仓管理系统，并针对不同的系统产品进行商务合同的签署与供应商的选定，根据产品图纸向至少 3家供应商进行询价，根据供应商选定流程进行技术、质量以及商务价格评比，最终确定一级以及关键子部件的二级供应商。

（3）生产与样件测试

所选定的供应商需要按照产品图纸上的技术要求进行生产步骤拆解，并依据生产工艺过程进行生产线布局，供应商还需要向专业模具厂家定制W公司新产品专用工装卡具以配合产品生产。其量产生产线员工需要进行产前培训，并达到熟练操作标准后，进行 W 公司新产品的生产制造。前期供应商需要进行 A 、B 两次样件的生产交付，用以进行装配干涉测试、产品台架测试、产品性能测试以及耐久测试。

（4）小批量生产与审核

W 公司依据公司审核流程，派遣质量、采购以及物流主管人员对审核选定供应商进行小批量生产的现场审核，对其生产制造过程、产能、连续性、稳定性以及包装周转进行全方法细致的审核，审核通过后才可以进行新产品的批量供应。

（5）项目量产交付

项目正式量产前，供应商依据W公司释放的季度交付预测量进行原材料到厂准备，并根据年释放的年度交付预测量进行关键原材料的订单锁定，例如电子芯片子部件。项目的量产交付预示着整个新产品开发项目正式结束，W公司集成热交换产品开发项目的 WBS 分解示意图，如 4.3 图所示。

![](images/c99b7cfd54f1a3178d30ad20f885d945a62ea782e2d095b8ade9b952ddc87567.jpg)  
图4.3 项目工作分解图  
Fig.4.3 Project task breakdown diagram

# 4.2.2 项目工作排序

项目中的每个具体工作之间存在明显联系性，部分项目工作的开展需要依托于上一阶段的工作成果，因此项目实际进行过程中应注意先后关系的问题。本文主要是针对新能源汽车集成热交换新产品的开发项目进行工作内容分解与研究，旨在明确整个项目工作内容和各项工作之间的逻辑关系。其次，在确定各项工作的时间范围和关键工作节点后，将其细化为独立的工作内容，过程中需要保证各项工作之间无重叠、无交叉，相互独立且明确清晰。最后对每一项独立活动提供标注和注释，以确保该项工作被落实在项目进度计划文件中，具体的项目活动如下表4.1所示。

表 4.1 项目活动定义表  
Table 4.1 Project activity definition Table   

<table><tr><td>代码</td><td>字母序号WBS</td><td></td><td>工作定义</td><td></td><td>工作描述</td></tr><tr><td></td><td></td><td>1</td><td>前期规划</td><td></td><td></td></tr><tr><td>AA</td><td>1</td><td>1.1</td><td></td><td>项目调查研究</td><td>对项目产品的技术功能性进行市场调查</td></tr><tr><td>AB</td><td>2</td><td>1.2</td><td></td><td>项目立项审查</td><td>公司内部评审委员会审批此开发项目</td></tr><tr><td>AC</td><td>3</td><td>1.3</td><td></td><td>编写可行性研究报告</td><td>针对市场调研结果对开发项目可行性进行分析</td></tr><tr><td>AD</td><td>4</td><td>1.4</td><td></td><td>产品功能初步设计</td><td>产品功能的初步设计确定开发产品的核心功能</td></tr><tr><td></td><td></td><td>2</td><td>产品图纸设计</td><td></td><td>散热器、冷却风扇、水箱、冷却液泵等冷却系 统的图纸设计</td></tr><tr><td></td><td></td><td></td><td></td><td>AE52.1电机、电控热管理系统</td><td>冷却器、电池水冷版、电子水泵、电子膨胀阀、 电子水阀、PTC 加热器图纸设计</td></tr><tr><td></td><td>AF6</td><td>2.2</td><td></td><td>电池热管理系统</td><td>电动压缩机、蒸发器、冷凝器、储液罐、 PTC加热器/热泵空调</td></tr><tr><td></td><td>AG7</td><td>2.3</td><td>乘用舱空调系统</td><td></td><td></td></tr><tr><td></td><td></td><td>3</td><td>商务合同</td><td></td><td></td></tr><tr><td>AH8</td><td></td><td>3.1</td><td>保密及W集团 框架协议签署</td><td></td><td>与所有潜在供应商签署发包前的商务协议</td></tr><tr><td>Al9</td><td></td><td>3.2</td><td>供应商发包及选定</td><td></td><td>遵循W集团供应商选定原则对竞价供应商进行 选定</td></tr><tr><td></td><td>AJ103.3</td><td></td><td>零部件质保协议</td><td></td><td>与选定供应商签署零部件交付以及售后质量 保证协议</td></tr><tr><td></td><td></td><td></td><td>AK113.4开发及量产单价协议</td><td></td><td>对开发阶段的样件及一次性投资以及量产交付 单价进行谈判以及协议签署</td></tr><tr><td></td><td></td><td>4</td><td>生产线、工装卡具安装</td><td></td><td></td></tr><tr><td></td><td></td><td></td><td>AL124.1生产线布局安装</td><td></td><td>根据产品图纸要求进行生产线工艺过程规划 与设备安装</td></tr></table>

续表 4.1  

<table><tr><td></td><td>13</td><td>4.2</td><td>工装卡具生产安装</td><td>续农41</td></tr><tr><td>AM</td><td></td><td>4.3</td><td>生产线员工培训</td><td>零部件特殊工位的专用工装卡具生产与安装 特殊工种员工培训（如高压电及</td></tr><tr><td>AN14</td><td></td><td></td><td></td><td>热处理工艺相关操作）</td></tr><tr><td></td><td></td><td>5</td><td>A阶段样件生产</td><td></td></tr><tr><td></td><td>155.1</td><td></td><td>交付与测试 A阶段样件生产交付</td><td>供应商使用软模生产初始阶段样件</td></tr><tr><td>A0</td><td>16</td><td>5.2</td><td></td><td></td></tr><tr><td>AP</td><td></td><td></td><td>A阶段样件装配验证</td><td>W公司对A阶段样件进行主机厂装配验证</td></tr><tr><td>AQ</td><td>17</td><td>5.3</td><td>A阶段样件台架测试 B阶段样件生产交付</td><td>W公司对A阶段样件进行主机厂台架测试 供应商使用量产模具生产变更阶段样件</td></tr><tr><td></td><td></td><td>6</td><td>与测试</td><td></td></tr><tr><td>AR</td><td></td><td>186.1</td><td>B阶段样件生产交付</td><td>W公司对变更阶段样件进行主机厂装配验证</td></tr><tr><td>AS</td><td>19</td><td>6.2</td><td>B阶段样件装配验证</td><td>W公司对变更阶段样件进行主机厂台架测试</td></tr><tr><td>AT</td><td>20</td><td>6.3</td><td>B阶段样件的DV试验</td><td>变更阶段样件的设计验证试验</td></tr><tr><td>AU</td><td>21</td><td>6.4</td><td>B阶段样件的PV试验</td><td>变更阶段样件的产品验证试验</td></tr><tr><td></td><td></td><td>7</td><td>小批量生产与主机厂 审核</td><td></td></tr><tr><td>AV</td><td>22</td><td>7.1</td><td>小批量生产</td><td>供应商小批量生产</td></tr><tr><td>AW</td><td>23</td><td>7.2</td><td>产能及资产审核</td><td>对供应商的产能及投资模具卡具的审核</td></tr><tr><td>AX</td><td>24</td><td>7.3</td><td>生产过程审核</td><td>对供应商的生产过程进行审核</td></tr><tr><td>AY</td><td>25</td><td>7.4</td><td>交付过程审核</td><td>对供应商的物流运输以及包装方式进行审核</td></tr><tr><td>AZ</td><td>26</td><td>7.5</td><td>审核文件释放</td><td>输入审核结果并在系统内释放审核通过文件</td></tr><tr><td></td><td></td><td>8</td><td>量产交付</td><td></td></tr><tr><td></td><td>BA278.1</td><td></td><td>量产物料准备</td><td>供应商依据LAB系统对量产物料进行采购</td></tr><tr><td>BB</td><td></td><td>288.2</td><td>正式量产</td><td>供应商正式量产</td></tr></table>

# 4.2.3 项目里程碑设定

项目进度计划最重要的部分就是项目里程碑的设定，这一步骤有着举足轻重的地位和不可替代的意义。因为在此过程中，影响项目顺利进行的关键因素就是要明确指出关系到项目成败的决定性工作。里程碑事件就是对项目实施和最终结果起到决定性作用的工作，很大程度上这些工作与项目上的关键路径是相互交叉的，有很高的重合性。无论哪项工作突然发生事故或者受到不良影响，都可能会对后续项目产生连锁反应，最终使得项目总进度无法按期完成，所以项目里程碑的计划和实施就格外的重要，对项目进度的顺利进行起着保护和督促作用。

针对此次开发项目中的里程碑事件，项目管理者需要根据经验既全面评估项目中有可能会发生的制约因素和潜在障碍，更需要邀请整个团队成员以头脑风暴的形式对整个里程碑事件进行查漏补缺，确保更加科学性合理性的制定出项目里程碑，组内认可度高。项目里程杯的制定过程如下：

（1）头脑风暴。整合项目成员，对项目里程碑事件进行广泛讨论，初步拟定项目里程碑事件以及时间顺序，集合大家的智慧查漏补缺里程碑事件。里程碑事件的计划无法由单单一个人计划决定，也同样需要集合项目责任人、研发人员、核心参与者等的智慧，集齐大家的想法与建议，一起拟定项目里程碑的初步计划。这种头脑风暴后的成果会很大程度上使得项目实施极具顺畅性。

（2）项目里程碑事件的确定。倒推法是本项目使用的一种方法，其优势性非常明显。以项目结果为源头，逐步梳理在项目过程中可能发生的里程碑活动的逻辑性和时间顺序，继而把控每个里程碑活动细节，使得里程碑事件的确定更加具有完善性和成熟性。

（3）里程碑计划的最终检验。将最终确认下来的里程碑事件以报告形式交付并汇报给项目经理以及公司管理层，对这一计划进行最终把控审查，审查计划的科学性及合理性，进度计划的编制可根据审批通过后的里程碑事件编写。表4.2下方表格及为本项目的里程碑事件计划。

![](images/f94f1db052c4056a8aa20b8f2c3529cf25b68efb4712b7f455dacbb7da7aaa9d.jpg)

# 4.3 项目工作逻辑关系和持续时间估算

# 4.3.1 项目工作逻辑关系分析

顺利开展后期工作的一项重要前提是前期项目工作的执行满足各个时间点，项目里的每个单独工作表面上彼此独立，实际上在实施过程中往往牵一发而动全身，有一定的时间顺序性，一般有两种情况：（1）固定的时间顺序，每个工作间的逻辑关系不可改变，要按照既定的顺序进行，否则进程将无法向前发展。（2）在对资源正常合理利用的前提下，对工作进行排序[32]。虽然频繁的进行顺序调整也能完成整体工作，但势必造成资源浪费，在项目进度管理中产生风险。因此，按照逻辑关系和既定的实施顺序做相应的整理，这对有效地执行项目计划起到了有益的作用，可以确保项目在满足质量要求的前提下，按计划完成。因为新能源汽车产品开发相对来说既艰巨又对有过高的时间把控标准，所以项目工作的合理安排就是提高工作效率必要条件，才能保证把复杂的工作做好。在决定实施一个项目的各项工作时，必须遵守以下一些规则：

（1）项目工作的内部逻辑联系。由于某些项目工作在实施过程中存在固有的逻辑顺序，这一顺序是固定且不可更改的。因此，在项目启动之前，进行充分的可行性研究和项目逻辑梳理，对于确保项目在理论上的可行性至关重要。

（2）明确项目组织结构。一个有效的组织结构能够显著提升项目的执行力并推进项目进度。因此，尽早明确项目组织结构至关重要。在保持组织关系和谐的基础上，优化资源分配和调度，减少在资源分配上的耗时与不均衡，进而提升项目资源的使用效率。

（3）外部资源阻碍的考虑不可或缺。项目内部和外部资源是项目成功的关键要素，这些资源之间可能存在着复杂且微妙的逻辑关系。因此，全面评估和考虑可能出现的外部资源阻碍至关重要，以避免对项目造成不利影响。详见表4.3 所示：

表 4.3项目工作顺序表  
Table 4.3 Project task sequence table   

<table><tr><td>序号</td><td>WBS</td><td>活动定义</td><td>字母代码</td><td>紧后工作</td></tr><tr><td></td><td>1</td><td>前期规划</td><td></td><td></td></tr></table>

续表 4.3  

<table><tr><td colspan="4"></td></tr><tr><td>1</td><td>1.1</td><td>项目调查研究</td><td>AA</td><td>AB</td></tr><tr><td>2</td><td>1.2</td><td>项目立项审查</td><td>AB</td><td>AC</td></tr><tr><td>3</td><td>1.3</td><td>编写可行性研究报告</td><td>AC</td><td>AD</td></tr><tr><td>4</td><td>1.4</td><td>产品功能初步设计</td><td>AD</td><td>AE, AF, AG</td></tr><tr><td></td><td>2</td><td>产品图纸设计</td><td></td><td></td></tr><tr><td>5</td><td>2.1</td><td>电机、电控热管理系统</td><td>AE</td><td>AH</td></tr><tr><td>6</td><td>2.2</td><td>电池热管理系统</td><td>AF</td><td>AH</td></tr><tr><td>7</td><td>2.3</td><td>乘用舱空调系统</td><td>AG</td><td>AH</td></tr><tr><td></td><td>3</td><td>商务合同</td><td></td><td></td></tr><tr><td>8</td><td>3.1</td><td>保密及W集团框架协议签署</td><td>AH</td><td>AI</td></tr><tr><td>9</td><td>3.2</td><td>供应商发包及选定</td><td>AI</td><td>AJ</td></tr><tr><td>10</td><td>3.3</td><td>零部件质保协议</td><td>AJ</td><td>AK</td></tr><tr><td>11</td><td>3.4</td><td>开发及量产单价协议</td><td>AK</td><td>AL, AM</td></tr><tr><td></td><td>4</td><td>生产线、工装卡具安装</td><td></td><td></td></tr><tr><td>12</td><td>4.1</td><td>生产线布局安装</td><td>AL</td><td>AN</td></tr><tr><td>13</td><td>4.2</td><td>工装卡具生产安装</td><td>AM</td><td>AN</td></tr><tr><td>14</td><td>4.3</td><td>生产线员工培训</td><td>AN</td><td>A0</td></tr><tr><td></td><td>5</td><td>A 样件生产交付与测试</td><td></td><td></td></tr><tr><td>15</td><td>5.1</td><td>A 阶段样件生产交付</td><td>A0</td><td>AP</td></tr><tr><td>16</td><td>5.2</td><td>A 阶段样件装配验证</td><td>AP</td><td>AQ</td></tr><tr><td>17</td><td>5.3</td><td>A 阶段样件台架测试</td><td>AQ</td><td>AR</td></tr><tr><td></td><td>6</td><td>B 样件生产交付与测试</td><td></td><td></td></tr><tr><td>18</td><td>6.1</td><td>B阶段样件生产交付</td><td>AR</td><td>AS</td></tr><tr><td>19</td><td>6.2</td><td>B阶段样件装配验证</td><td>AS</td><td>AT</td></tr><tr><td>20</td><td>6.3</td><td>B阶段样件的 DV 试验</td><td>AT</td><td>AU</td></tr><tr><td>21</td><td>6.4</td><td>B 阶段样件的PV 试验</td><td>AU</td><td>AV</td></tr><tr><td></td><td>7</td><td>小批量生产与主机厂审核</td><td></td><td></td></tr><tr><td>22</td><td>7.1</td><td>小批量生产</td><td>AV</td><td>AW, AX</td></tr><tr><td>23</td><td>7.2</td><td>产能及资产审核</td><td>AW</td><td>AY</td></tr><tr><td>24</td><td>7.3</td><td>生产过程审核</td><td>AX</td><td>AY</td></tr></table>

续表 4.3  

<table><tr><td>25</td><td>7.4</td><td>交付过程审核</td><td>AY</td><td>AZ</td></tr><tr><td>26</td><td>7.5</td><td>审核文件释放</td><td>AZ</td><td>BA</td></tr><tr><td></td><td>8號</td><td>量产交付</td><td></td><td></td></tr><tr><td>27</td><td>8.1</td><td>量产物料准备</td><td>BA</td><td>BB</td></tr><tr><td>28</td><td>8.2</td><td>正式量产</td><td>BB</td><td></td></tr></table>

# 4.3.2 项目工作持续时间估算

对于项目工作时间估算，应尽可能的接近实施时的实际完成时间。一旦对项目工作的时间估算过少，项目组需要立即调配其他工作的时间，或增加对工作资源的投入，甚至需要利用额外的加班时间完成项目工作，这无疑会提升零部件开发项目的质量风险和进度风险。如果对项目工作的估算的时间过长，项目成员极易在项目实施过程中懈怠消极，对与项目资源来说这无疑时极大的浪费。由此可见，科学、客观的对项目工作时间进行合理的估算是十分重要且具有实际一样的。

（1）集成热交换新产品开发工作时间的估算技巧

专家估计法：通过经验丰富的行业专家，利用科学的理论知识针对不同项目的背景特点进行分析和估算，得出有实际指导意义的具体项目工作持续时间，往往对于项目工作的时间估算具有很高的实践性。

类推法：是一种以过往行业经验进行估算和推迟的有效方法。对比分析过往的相似项目，从而进行合理的时间规律推算，同时可可以借鉴过往项目的实际完成时间进行新项目的时间估算。

模拟法：以其全面考虑项目各影响因素并得出贴近实践实施情况的科学计算结果为其显著优势。模拟法的计算过程通常包括以下几个步骤：首先，基于过往经验，估算出在高效条件下所需的工作时间a，以及恶劣条件下所需的工作时间b；接着，考虑当前资源状况，确定概率最大的所需时间 $\mathbf { m }$ 。最后，将这些数据代入计算公式中，即可得出贴近实际情况的工作完成时间：

时间平均计算公式 $\mathrm { t } = \ \left( \mathrm { a } { + } 4 \mathrm { m } { + } \mathrm { b } \right) \ / \ 6$ 方差均值计算公式 $\mathbf { a } = \left( \mathbf { b - a } \right) / 6$ （2）集成热交换新产品开发工作时间确定的方法

首先，对项目背景以及项目特点进行充分调查，尽可能全面的分析客观约束条件，例如新能源汽车的安全法规、零部件质量标准，在关键路径的帮助下估算出各项工作的持续时间。然后，对集成热交换新产品和过往开发项目的相关资料进行分析评估，利用类推法粗略估算出项目工作的持续时间。最后，从开发项目的时间情况出发，利用模拟计算法对数据进行分析计算后，再评估项目各项工作的持续时间，利用不同的估算方法反复对比分析，从而估算出不同的持续时间，挑选出其中最贴近实际情况的结果。

通过对项目所处的内外界环境进行充分研究，本文结合过往开发项目的实际实施情况，对集成热交换新产品开发项目的具体工作进行时间估算，统计得出了合理的计算数据，并得出了合理的项目工作持续时间，结果见表4.4。

表 4.4 集成热交换新产品开发项目主要工作时间估算表  
Table 4.4 Time Evaluation of Main tasks on Integrated Heat Exchange Develop   

<table><tr><td>字母代码</td><td>WBS</td><td>活动定义</td><td>期待工期</td></tr><tr><td></td><td>1</td><td>前期规划</td><td></td></tr><tr><td>AA</td><td>1.1</td><td>项目调查研究</td><td>20</td></tr><tr><td>AB</td><td>1.2</td><td>项目立项审查</td><td>20</td></tr><tr><td>AC</td><td>1.3</td><td>编写可行性研究报告</td><td>20</td></tr><tr><td>AD</td><td>1.4</td><td>产品功能初步设计</td><td>30</td></tr><tr><td></td><td>2</td><td>产品图纸设计</td><td></td></tr><tr><td>AE</td><td>2.1</td><td>电机、电控热管理系统</td><td>90</td></tr><tr><td>AF</td><td>2.2</td><td>电池热管理系统</td><td>75</td></tr><tr><td>AG</td><td>2.3</td><td>乘用舱空调系统</td><td>60</td></tr><tr><td></td><td>3</td><td>商务合同</td><td></td></tr><tr><td>AH</td><td>3.1</td><td>保密及W集团框架协议签署</td><td>15</td></tr><tr><td>AI</td><td>3.2</td><td>供应商发包及选定</td><td>45</td></tr><tr><td>AJ</td><td>3.3</td><td>零部件质保协议</td><td>15</td></tr><tr><td>AK</td><td>3.4</td><td>开发及量产单价协议</td><td>15</td></tr><tr><td></td><td>4</td><td>生产线、工装卡具安装</td><td></td></tr><tr><td>AL</td><td>4.1</td><td>生产线布局安装</td><td>75</td></tr><tr><td>AM</td><td>4.2</td><td>工装卡具生产安装</td><td>65</td></tr><tr><td>AN</td><td>4.3</td><td>生产线员工培训</td><td>15</td></tr><tr><td></td><td>5</td><td>A 样件生产交付与测试</td><td></td></tr><tr><td>A0</td><td>5.1</td><td>A 阶段样件生产交付</td><td>10</td></tr></table>

续表 4.4  

<table><tr><td>AP</td><td>5.2</td><td>A阶段样件装配验证</td><td>10</td></tr><tr><td>AQ</td><td>5.3</td><td>A阶段样件台架测试</td><td>70</td></tr><tr><td></td><td>6</td><td>B 样件生产交付与测试</td><td></td></tr><tr><td>AR</td><td>6.1</td><td>B 阶段样件生产交付</td><td>7</td></tr><tr><td>AS</td><td>6.2</td><td>B 阶段样件装配验证</td><td>7</td></tr><tr><td>AT</td><td>6.3</td><td>B阶段样件的DV 试验</td><td>30</td></tr><tr><td>AU</td><td>6.4</td><td>B 阶段样件的PV 试验</td><td>46</td></tr><tr><td></td><td>7</td><td>小批量生产与主机厂审核</td><td></td></tr><tr><td>AV</td><td>7.1</td><td>小批量生产</td><td>15</td></tr><tr><td>AW</td><td>7.2</td><td>产能及资产审核</td><td>15</td></tr><tr><td>AX</td><td>7.3</td><td>生产过程审核</td><td>15</td></tr><tr><td>AY</td><td>7.4</td><td>交付过程审核</td><td>15</td></tr><tr><td>AZ</td><td>7.5</td><td>审核文件释放</td><td>35</td></tr><tr><td></td><td>8</td><td>量产交付</td><td></td></tr><tr><td>BA</td><td>8.1</td><td>量产物料准备</td><td>30</td></tr><tr><td>BB</td><td>8.2</td><td>正式量产</td><td>1</td></tr></table>

# 4.4 项目进度计划的编制

# 4.4.1 编制网络图

在拆分项目工作、逻辑关系确定、估算活动时间全部完成以后，需要对各项工作进行节点连接，明确出项目活动的顺序。集成热交换开发项目包括项目成本管理、项目进度管理以及项目的质量管理。由于此次的新产品开发项目的项目时间要求过于严苛，且涉及众多内部、外部的相关项目成员，为了确保依照项目进度计划完成整体开发任务，不得不增加对项目进度的管理要求，倾斜更多的管理资源。充分的做好项目前期准备工作，并全面了解所有可用管理资源，对可能出现的风险因素进行风险预警，梳理项目各活动之间的逻辑关系，整合这些内容后进行项目进度计划的科学制定，这对步骤对于实施集成热交换新产品开发项目有着实际的指导意义。如图 4.4，为各主要工作的顺序和逻辑关系：

![](images/cac2f86de83963ca8165e58b03e37bfdbbc232df5ba16484a4f4ba213b12c840.jpg)  
图 4.4 项目进度网络图  
Fig. 4.4 Project schedule diagram

# 4.4.2 关键路径的确定

确定关键路径是项目进度管理中最为主要环节之一，其目的是将项目进度计划中最为耗时的工作进行识别后相加，所得到的时间路线就是关键路径。若是关键路径上的项目工作面临较多的主观或是客观因素，将极大可能的影响项目工作时间，甚至会加剧项目进度延迟的几率。

项目网络计划图是用于描述和规划工作完成时间的，它共计包含七个时间参数。其中，D是工作持续时间， ES（最早开始时间）是指在前序工作全部完成后，该项工作可以开始的最早时间点；EF（最早完成时间）则是指在已知前置工作全部完成后，该项工作任务预计能够完成的最早时间点。LS（最晚开始时间）表示在不延误整个项目进度的情况下，该工作允许的最晚开始时间点；LF（最晚完成时间）则是指在整个项目进度不受影响的前提下，该项工作被允许的最晚完成时间点。TF（总时差）是工作过程中可以利用的弹性时间，但前提是该工作的进度调整不能对整个项目的进度产生负面影响；FF（自由时差）则是指在不影响后续工作最早开始时间的前提下，该工作所拥有的弹性完成时间。

网络计划时间的计算参数主要包括以下步骤：

（1）最 早 完 成 时 间等 于 最 早 开 始 时 间 加 上 该 活 动 的 持 续 时 间 ， 即$\mathrm { E F _ { i - j } = E S _ { i - j } + D _ { i - j } }$ 。在一个活动没有多个紧前活动的情况下，最早开始时间等于其紧前活动的最早完成时间；当一个活动的紧前活动不止一个时，该活动的最早开始时间为其所有紧前活动最早完成时间中的最大值。

（2）计算每个活动的最迟开始时间和最迟完成时间，在一个活动只有一个紧后活动的情况下，最迟完成时间等于其紧后活动的最迟开始时间；当一个活动的紧后活动不止一个时，其最迟完成时间为其所有紧后活动最迟开始时间中的最小值。

（3）计算各活动的总时差， $\mathrm { T F _ { i - j } = L S _ { i - j } - E S _ { i - j } = L F _ { i - j } - E F _ { i - j } }$ 。总时差最小的活动为关键活动，由关键活动组成的路径是关键路径，关键路径上各活动时间总和是项目的持续时间。

（4）计算各活动的自由时差，它指的是某个非关键活动独立使用的机动时间，可以通过其紧后活动的最早开始时间减去本活动的最早完成时间来计算，即 $\mathrm { F F _ { i - j } = }$ $\mathrm { E S _ { j - k } \mathrm { - E F _ { i - j } } }$ 或 $\mathrm { F F _ { i - j } = E S _ { j - k } - E S _ { i - j } - D _ { i - j } }$ 。

根据W公司集成热交换新产品开发项目的主要工作顺序表，对项目中各项工作的先后顺序进行分析后，结合估算出的每项活动的持续时间，以此计算项目网络计划时间参数如表4.5所示。

表4.5 项目网络计划时间参数计算表  
Table 4.5 Project network schedule time parameter calculation table   

<table><tr><td>字母代码</td><td>D</td><td>ES</td><td>LS</td><td>EF</td><td>LF</td><td>TF</td><td>FF</td></tr><tr><td>AA</td><td>20</td><td>O</td><td>0</td><td>20</td><td>20</td><td>O</td><td>O</td></tr><tr><td>AB</td><td>20</td><td>20</td><td>20</td><td>40</td><td>40</td><td>0</td><td>0</td></tr><tr><td>AC</td><td>20</td><td>40</td><td>40</td><td>60</td><td>60</td><td></td><td>0</td></tr><tr><td>AD</td><td>30</td><td>60</td><td>60</td><td>90</td><td>90</td><td>0</td><td></td></tr><tr><td>AE</td><td>90</td><td>90</td><td>90</td><td>180</td><td>180</td><td>0</td><td>0</td></tr><tr><td>AF</td><td>75</td><td>90</td><td>105</td><td>165</td><td>180</td><td>15</td><td>15</td></tr><tr><td>AG</td><td>60</td><td>90</td><td>120</td><td>150</td><td>180</td><td>30</td><td>30</td></tr><tr><td>AH</td><td>15</td><td>180</td><td>180</td><td>195</td><td>195</td><td>0</td><td></td></tr><tr><td>AI</td><td>45</td><td>195</td><td>195</td><td>240</td><td>240</td><td>0</td><td>0</td></tr><tr><td>AJ</td><td>15</td><td>240</td><td>240</td><td>255</td><td>255</td><td>10</td><td>0</td></tr><tr><td>AK</td><td>15</td><td>255</td><td>255</td><td>270</td><td>270</td><td></td><td>0</td></tr><tr><td>AL</td><td>75</td><td>270</td><td>270</td><td>345</td><td>345</td><td>0</td><td></td></tr><tr><td>AM</td><td>65</td><td>270</td><td>280</td><td>335</td><td>345</td><td>10</td><td>10</td></tr><tr><td>AN</td><td>15</td><td>345</td><td>345</td><td>360</td><td>360</td><td>0</td><td></td></tr><tr><td>A0</td><td>10</td><td>360</td><td>360</td><td>370</td><td>370</td><td>O</td><td></td></tr><tr><td>AP</td><td>10</td><td>370</td><td>370</td><td>380</td><td>380</td><td>O</td><td></td></tr><tr><td>AQ</td><td>70</td><td>380</td><td>380</td><td>450</td><td>450</td><td>0</td><td>0</td></tr></table>

续表 4.5  

<table><tr><td>AR 7</td><td></td><td>450 450</td><td>457</td><td>464</td><td></td><td>0</td><td>0</td></tr><tr><td>AS</td><td>7</td><td>457</td><td>457</td><td>464</td><td>464</td><td>0</td><td>0</td></tr><tr><td>AT</td><td>30</td><td>464</td><td>464</td><td>494</td><td>494</td><td>O</td><td>0</td></tr><tr><td>AU</td><td>46</td><td>494</td><td>494</td><td>540</td><td>540</td><td></td><td>0</td></tr><tr><td>AV</td><td>15</td><td>540</td><td>540</td><td>555</td><td>555</td><td></td><td></td></tr><tr><td>AW</td><td>15</td><td>555</td><td>555</td><td>570</td><td>570</td><td>0</td><td>0</td></tr><tr><td>AV</td><td>15</td><td>555</td><td>555</td><td>570</td><td>570</td><td>O</td><td></td></tr><tr><td>AY</td><td>15</td><td>570</td><td>570</td><td>585</td><td>585</td><td>O</td><td>0</td></tr><tr><td>AZ</td><td>35</td><td>585</td><td>585</td><td>620</td><td>620</td><td>0</td><td>O</td></tr><tr><td>BA</td><td>30</td><td>620</td><td>620</td><td>650</td><td>650</td><td>0</td><td>O</td></tr><tr><td>BB</td><td>1</td><td>650</td><td>650</td><td>651</td><td>651</td><td>0</td><td>0</td></tr></table>

基于对项目进度网络图与项目网络计划时间参数计算表的分析，可以得出本项目的关键路径， $\mathrm { \Delta A A {  } A B {  } A C {  } A D {  } A E / A F / A G {  } A H {  } A I {  } A J {  } A K {  } A L { - } A K / A K {  } A K {  } A K / K }$ AN$ \mathrm { A O }  \mathrm { A P }  \mathrm { A Q }  \mathrm { A R }  \mathrm { A S }  \mathrm { A T }  \mathrm { A U }  \mathrm { A V }  \mathrm { A W } / \mathrm { A X }  \mathrm { A Y }  \mathrm { A Z }  \mathrm { B A }  \mathrm { B B } \stackrel { \pm } {  } \mathrm { A W } / \mathrm { A X }$ 共计2 $0 + 2 0 + 2 0 + 3 0 + 9 0 + 1 5 + 4 5 + 1 5 + 7 5 + 1 5 + 1 0 + 1 0 + 7 0 + 7 + 7 + 3 0 + 4 6 + 1 5 + 1 5 + 1 5 + 3 5 + 3 0 + 1 = 6$ 51天。所有关键路径上节点的路线即为关键路径。项目进度的关键路径图, 如图4.5 所示。

![](images/0c71a39a096e4a720ae1a25ce5b594fdd2731bc712b6bb0195fe5cde1042ae58.jpg)  
图 4.5 项目关键路径图  
Fig. 4.5 Project Critical Path Network Diagram

# 4.4.3 项目进度甘特图

# （1）项目一级进度甘特图

集成热交换新产品开发项目的实施工作难度性较强，产品的功能设计复杂，依照图纸要求生产的新产品生产制造过程专业性较强，一级供应商与众多技术指定的二级供应商，甚至原材料供应商众多，因此涉及广泛、大量的组织协调工作。制定科学的项目进度甘特图有利于后期直观有效的监控项目工作实施，优化调整其中的各项工作，是项目进度管理中最为重要的组成部分。根据本项目的项目要求绘制一级进度甘特图，如图4.6所示。

![](images/959348cb5dd61ee489ab02546292cb8a2abbba90513e7736091390842e83c46d.jpg)  
4.6 项目一级进度甘特图  
Fig. 4.6 Project first level schedule gantt chart

从项目的一级进度甘特图来看，本集成热交换新产品开发项目因为项目本身的工作较多，涉及复杂的设计、商务、生产线布局以及多次样件交付与测试工作，关联研发、质量、生产、采购多方的大量合作，因此需要十分科学的对项目工作进行安排管理，以确保整体项目的进度与质量都符合公司要求。与此同时，项目后期的实施工作应严格根据进度计划实施，从而确保项目的顺利完成。

# （2）项目二级进度计划

项目二级进度计划应与项目进度计划保证原则上的一致，以保证一级进度计划有效落实为目的，更为细致的阐述对一级项目工作所做的具体安排，从而有效的推进项目工作的实施。项目组内各部门成员使用二级进度计划更好的协调配合完成项目工作。如图4.7所示，即为本次进度计划绘制的二级进度甘特图。

![](images/92a1f583a787d4ca9c88b03abdb6b242fa8dea714a5b52e7f9922dbb5fc37647.jpg)  
图4.7 项目二级进度甘特图  
Fig.4.7 Project second level schedule gantt chart

# 4.5 项目进度计划的优化

项目进度优化的目的是对其中的子任务进行合理的时间调整和资源分配，将改进和优化的办法运用到组织、技术和管理等方面，从而达到减少工期、提高工作效率以及平衡项目资源的目标，最终得到最科学有效的项目进度计划[33]。

# 4.5.1 项目进度优化的原则和目标

W 公司的集成热交换新产品的开发项目是抢占电动车市场最为关键的项目之一，为了确保下一代的电动车可以顺利的应用竞争力较强的集成热交换新产品，获得客户在行驶里程和安全舒适性上认可，公司必须要保证此开发项目在计划的时间内交付合格的新产品。由于此开发项目受到公司内部以及众多外部供应商的诸多因素影响，项目组需要对项目进度计划进行科学有效的优化，以保整项目如期完成，甚至为特殊情况预留出足够的反应时间。

单一或者强制减少各个子项目的活动时间并非是优化项目进度的最佳方案，通过运用先进的科学管理技术和具有实际操作意义的有效方法，调整优化整体项目进度计划，确保各项工作有序的进行，同时保质保量的交付符合图纸要求的产品，尽最大可能的缩短项目完成时间。统筹管理项目进度、交付质量、项目成本以及资源配置等诸多关键要素，避免硬性减少工作时间影响项目交付质量，真正做到对项目进度进行合理的优化。

# 4.5.2 项目进度计划优化内容

首要任务是构建健全项目组织结构，对项目进度进行精细化管理。设立项目主管负责人办公室，由项目经理对各个职能部门进行负责人任命，通过前期的项目进度计划的分析，统一项目目标，合理分配项目工作时间，积极构建有效的沟通机制。项目经理对项目总体情况进行把控，其中包含对项目的进度控制、成本控制、质量控制，还包括对项目总体情况的向上汇报和外部问题的统一协调沟通，资源与工作内容的快速调整，落实项目工作和纠正问题，以及对有实际意义的经验做出总结和分享。其他各相关部门应安排经验丰富的专业工程师为项目提供技术支持和技术交流，配合落实供应商的工作管理，协调在生产过程中遇到的相关问题，并及时向项目经理报告项目情况，以确保管理层可以在短时间内快速的支持解决对项目进度产生严重影响的问题。

其次是对资源的合理配置，为确保项目的进度得到合理、充分以及及时的资源分配，在项目的实施过程中应建立合理的项目管理规章制度，由项目经理对项目关键资源（如项目管理人员、专业的生产设备和生产原材料）进行监督分配，以实现项目及时获得高质量、高效率的关键资源的目的，进而保证项目的进度。

由此得出，在项目开发工作中，基于具体的特征和进度要求，需要对工作时间和供应商的工作安排进行改进和优化。

（1）A阶段验证与测试关键路径工作顺序时间调整

在原来的规划中，A 阶段样件装配验证和 A 阶段样件台架测试是串行的，待生产现场的装配验证全部结束后，再进行台架测试，因此占用了很多的时间。通过调整交样数量来将其关键路径的工作转化为平行进行的工作，将装配验证和台架验证的工作同时开始，通过此优化可以节省 10 天的时间。

（2）B 阶段样件装配验证、DV 试验与 PV 试验关键路径工作顺序时间调整

在原来的规划中，B 阶段样件装配验证、DV 试验与PV试验是串行的，待生产现场的装配验证全部结束后，再进行 DV 测试，DV 试验后再进行 PV 试验因此占用了很多的时间。通过调整交样数量来将其关键路径的工作转化为平行进行的工作，将装配验证和DV 试验与PV 试验同时进行，通过优化可以节省 37 天的时间。如图4.8所示：

![](images/df9aa796293e1114a95b6e74a5f437e451172339f8e9c057faaffa27c1e149b0.jpg)  
图4.8 验证与测试工作的连接顺序调整  
Fig.4.8 Comparison Chart Before and After Sequence Adjustment

通过采取以上措施，对验证阶段的多个关键路径进行优化，整体路径及时间更新为，AA→AB→AC AD AE/AF/AG AH AI AJ AK AL AN→AOAP/AQ AR AS/AT/AU $\mathrm { \partial ^ { \circ } A V \mathrm { \longrightarrow } A W / A X { \longrightarrow } A Y { \longrightarrow } A Z { \longrightarrow } B A { \longrightarrow } B B }$ 共计为 $2 0 { + } 2 0$ $+ 2 0 + 3 0 + 9 0 + 1 5 + 4 5 + 1 5 + 1 5 + 7 5 + 1 5 + 1 0 + 7 0 + 7 + 4 6 + 1 5 + 1 5 + 1 5 + 3 5 + 3 0 + 1 = 6 0 4$ 天。

整体时间总共节约47天，确保了项目在遇到各种因素导致延期的影响下，顺利的按照时间目标时间 2025年3月进行了生产。优化后的项目网络计划时间表4.6，如下：

表4.6 优化后项目网络计划时间参数计算表  
Table 4.6 Optimized project network plan time parameter calculation table   

<table><tr><td>字母代码</td><td>D</td><td>ES</td><td>LS</td><td>EF</td><td>LF</td><td>TF</td><td>FF</td></tr><tr><td>AA</td><td>20</td><td>0</td><td>0</td><td>20</td><td>20</td><td>0</td><td>0</td></tr><tr><td>AB</td><td>20</td><td>20</td><td>20</td><td>40</td><td>40</td><td>0</td><td>0</td></tr></table>

续表 4.6  

<table><tr><td>AC</td><td>20</td><td>40</td><td>40</td><td>60</td><td>60</td><td>0</td><td></td></tr><tr><td>AD</td><td>30</td><td>60</td><td>60</td><td>90</td><td>90</td><td>0</td><td>O</td></tr><tr><td>AE</td><td>90</td><td>90</td><td>90</td><td>180</td><td>180</td><td>0</td><td>0</td></tr><tr><td>AF</td><td>75</td><td>90</td><td>105</td><td>165</td><td>180</td><td>15</td><td>15</td></tr><tr><td>AG</td><td>60</td><td>90</td><td>120</td><td>150</td><td>180</td><td>30</td><td>30</td></tr><tr><td>AH</td><td>15</td><td>180</td><td>180</td><td>195</td><td>195</td><td>0</td><td>0</td></tr><tr><td>AI</td><td>45</td><td>195</td><td>195</td><td>240</td><td>240</td><td>0</td><td></td></tr><tr><td>AJ</td><td>15</td><td>240</td><td>240</td><td>255</td><td>255</td><td>10</td><td>O</td></tr><tr><td>AK</td><td>15</td><td>255</td><td>255</td><td>270</td><td>270</td><td>0</td><td></td></tr><tr><td>AL</td><td>75</td><td>270</td><td>270</td><td>345</td><td>345</td><td>0</td><td>0</td></tr><tr><td>AM</td><td>65</td><td>270</td><td>280</td><td>335</td><td>345</td><td>10</td><td>10</td></tr><tr><td>AN</td><td>15</td><td>345</td><td>345</td><td>360</td><td>360</td><td>0</td><td>O</td></tr><tr><td>A0</td><td>10</td><td>360</td><td>360</td><td>370</td><td>370</td><td>0</td><td>0</td></tr><tr><td>AP</td><td>10</td><td>370</td><td>430</td><td>380</td><td>380</td><td>60</td><td>60</td></tr><tr><td>AQ</td><td>70</td><td>370</td><td>370</td><td>440</td><td>440</td><td>0</td><td></td></tr><tr><td>AR</td><td>7</td><td>440</td><td>440</td><td>447</td><td>447</td><td>0</td><td>0</td></tr><tr><td>AS</td><td>7</td><td>447</td><td>486</td><td>454</td><td>493</td><td>39</td><td>39</td></tr><tr><td>AT</td><td>30</td><td>447</td><td>463</td><td>477</td><td>493</td><td>16</td><td>16</td></tr><tr><td>AU</td><td>46</td><td>447</td><td>447</td><td>493</td><td>493</td><td>0</td><td>O</td></tr><tr><td>AV</td><td>15</td><td>493</td><td>493</td><td>508</td><td>508</td><td>0</td><td></td></tr><tr><td>AW</td><td>15</td><td>508</td><td>508</td><td>523</td><td>523</td><td>0</td><td></td></tr><tr><td>AV</td><td>15</td><td>508</td><td>508</td><td>523</td><td>523</td><td>0</td><td>0</td></tr><tr><td>AY</td><td>15</td><td>523</td><td>523</td><td>538</td><td>538</td><td>0</td><td>0</td></tr><tr><td>AZ</td><td>35</td><td>538</td><td>538</td><td>573</td><td>573</td><td>0</td><td>0</td></tr><tr><td>BA</td><td>30</td><td>573</td><td>573</td><td>603</td><td>603</td><td>0</td><td>0</td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td>BB</td><td>1</td><td>603</td><td>603</td><td>604</td><td>604</td><td>0</td><td>0</td></tr></table>

# 4.5.3 优化后项目进度计划

根据分析上述开发项目中的各个关键路径，将验证阶段作为重点分析的关键路径并予以优化调整，对其进行资源分配的优化。通过给予分配更多的样件资源，得以将验证阶段中的大多数串行工作更改成可同步进行的并行工作，提高项目的效率。这样的优化极大的节约了项目中试验验证的整体时间，避免先序工作滞后所带来的进度风险，减少项目延误的可能性，提前了最终的量产交付时间。经过项目串行工作的调整优化，如图4.9所示，得出了优化后并行工作的甘特图，如图 4.10 所示。

![](images/acf965f430633fafe073e35a5311c0a95abe0f966e7bc0275a056e9ba6fe7271.jpg)

![](images/96c6d5c0abd0936ca41d492afa51fab7ecff55c0149e1a7dc27ca10ff72548b5.jpg)  
图 4.9 样件测试调整前后的甘特图

Fig. 4.9 Gantt Chart Before & After Sample Testing Adjustment

![](images/605a15e27a6125ff12d4ce71b828ce7a06533900713dd6e7b1cea33b6deff282.jpg)  
图4.10 项目优化甘特图  
Fig. 4.10 Project Gantt Chart After Optimization

# 第 5 章 W 公司汽车集成热交换新产品开发项目进度控制与保障措施

# 5.1 项目进度控制体系和措施

# 5.1.1 项目进度控制体系

在某种程度上会开发项目的进度会受到某些不可预测要素的影响。当影响要素出现时，进度偏差便会产生。只要产生此种情况理应纠正，以减小造成的影响并做出弥补，确保实际进度最终匹配计划进度的需求。

新开发项目实时比对实际和计划的进度时间并评估分析，当产生项目开发进度与计划进度出现偏差情况，应立刻做出调整，例如开发工作项目或人力分配等的调配，迅速缩小与进度计划的差距，避免产生显著的进度偏差，从而确保开发进度不被延误。进度控制流程如图 5.1 所示：

![](images/fe8839671753d96284a6b7d55fe53972d49aa9a61fcaebb9434399c58f1720d4.jpg)  
图 5.1 进度控制流程图  
Fig.5.1 Schedule control flow chart

为了有效管理开发进度，必须采用科学、高效的方法，敏锐地识别潜在的进度偏差，并立即采取行动进行调整，从而在产生严重影响之前迅速响应，高效协同。开发项目进度管理的过程中，充分的沟通与紧密的合作至关重要，这样我们才能迅速识别进度中的偏差，并及时介入，确保实际进度与计划要求保持一致，以达成整个项目顺利量产。

在开发项目启动后，为确保项目能够按照既定计划顺利进行，需要对主机厂端的设计、商务工作以及供应商端的生产验证工作进行精心安排和部署。在整个实施过程中，应保持对实际进度的严密监控，一旦发现进度偏差，各项目负责人应立即启动应急机制，采取切实有效的追赶措施，努力将进度拉回至原计划轨道。然而，必须强调的是任何调整措施都不能以牺牲产品质量为代价。产品质量是项目的核心，无论何种情况下，都应将其置于首要位置。在面临质量与进度之间的冲突时，必须坚定不移地保障产品质量，同时及时有效的调整项目计划，以确保整体项目的成功。

（1）若项目的关键里程碑节点发生了进度偏差，将导致项目整体进度受到严重影响，需要重新规划项目进度，以确保后续工作能够按照新的计划有序进行。（2）对于非关键节点上的进度偏差，虽然其影响可能相对较小，但仍需引起项目组的高度重视。在这种情况下,应调整组织内部的资源配置和工作策略，以加快落实项目实施进度，确保进度快速提升与计划进度保持同步。

（3）非关键路径上的子工作出现延期但延期程度可控时，应考虑对后续工作的时间节点进行微调，以弥补前期进度延误带来的影响。这种前慢后快的策略有助于保持整体进度的稳定性，确保项目能够按照既定目标顺利推进。

# 5.1.2 项目进度控制措施

（1）建立健全进度计划管理制度

对于项目进度的高效管理，首先需要建立健全的进度计划管理制度。这一制度应由专门的技术人员负责，对整个集成热交换新产品开发项目有全面而深入的理解。为此，项目专项小组应由研发技术部门、采购部门、物流部门以及质量管理部门中的专业工程师担任。项目组负责编制并监督实施项目进度。

（2）科学制定项目进度计划

在制定进度计划时，需要确保进度计划的合理性。这包括认真细致地分配工作，确保每项子工作和每个部门的不同任务能够立体交叉、平行流水进行。同时，综合考虑资源平衡，根据总装车间客户的需求，严格把控项目过程中的成本投资，并紧紧围绕关键路线上的工作进度。对于关键工序，组织专业性强的项目团队，提早做出合理的准备以及预防措施，充分进行进度检查，及时调整项目进度计划。

（3）后续工作提前准备

根据正常的项目流程要求，采购部门需要使用完整释放的设计图纸进行商务询价已经供应商选定，然而最长的产品设计模块也就是电机、电控热管理系统的设计需要3个月时间，采购部门可以在图纸非正式释放阶段，在 W集团战略供应商数据库中，寻找潜在供应商进行前期沟通和意向谈判，不仅可以缩短后期的正式商务谈判时间，还可以对于产品的技术难点以及后期供应商的生产制造关键点进行前期预警，将进度计划中有关联性的后续工作提前化。

# （4）强化协调与沟通

项目开发过程中，协调沟通的工作涉及范围广泛，而且往往是最为重要的一部分。本项目的协调沟通主要包括台架资源、外部协调、劳动力协调、生产计划协调等。加强与其他部门以及一二级供应商间的沟通合作，在项目会议中，提出问题、分析问题、集思广益，一起探寻最适宜的解决方案，通过及时的正向干预，确保信息畅通，从而确保项目能够整体良性有序地运行能够确保项目的顺利进行，并最终实现整体的进度目标。

# 5.1.3 项目进度控制主要内容

项目进度控制的内容主要包括对项目进展情况的监督和调整，目的是确保项目能够在成本预算内，按照预定的时间表和计划完成项目工作。在项目管理中，项目进度状况的监控涉及到项目结构图上的各个层次单元，以此来准确评估项目的进度状况。在进度控制过程中，需要挑选一个共同的、对所有工程活动都适用的计量单位，以便统一衡量和比较进度。此外，项目进度控制还需要帮助项目团队识别和解决项目进展中的问题，预测和避免潜在的延误和成本超支问题，从而提高项目的成功率和效率。同时，还有一些软件工具可以辅助项目进度控制，如Microsoft Project，GanttProject，Trello 和 Asana 等。这些工具提供了强大的进度计划和跟踪功能，可以直观地展示项目进度，方便团队成员实时跟踪和协作。

项目进度控制涉及项目进度的风险管理，识别和评估项目进度中的风险，并制定应对策略。项目的资源管理，确保项目所需的人力、物力、财力等资源的合理配置和利用。项目沟通协调，与项目团队成员、相关部门和利益相关者进行有效的沟通和协调。项目进度报告，定期向项目干系人提供进度报告，反映项目进展情况。项目变更管理，处理项目进度变更，确保变更的合理性和可控性。最后是项目的经验教训总结，项目进度控制的经验教训，可以为今后的项目提供参考。

# 5.2 项目进度动态监测与偏差分析

# 5.2.1 项目进度动态监测

在项目实施前，项目组各个部门都进行了严格的业务特点、难点以及目标分析，并在此基础上提出了具体的项目实施方案。不仅给出了明确的时间规划，而且还采用了一个高效的进度控制计划。每周在项目会议室召开的项目例会，项目实施部门成员依据每天的日程进行例行的审核和调度，并根据本周的项目实际执行情况，对项目进行进度对比跟踪。然而，集成热交换新产品的开发项目规模大，开发周期长，涉及面广，需要许多部门和供应商技术、销售、生产都要进行内外协调合作，因此，在项目开发中难免会出现一些问题影响进度的实施。

集成热交换新产品开发项目从2023年6 月1 日全面启动，要求在 2025年03月15日完成，项目整体要求为651日，通过对项目关键路径上的工作进行动态监测，将计划完成时间与实际完成时间进行对比，监测到因设计变更频繁使得项目在前期延迟了15天，通过压缩保密协议及集团框架协议签署与供应商选定过程，缩短项目的延期时间。因供应商对商务付款条件以及原材料调整机制提出不同意见，导致商务合同的签署延迟10 天，经与供应商协商，压缩生产线布局安装和员工培训时间，使得样件交付在 6 月 4 日如计划完成，具体的监测情况如下表 5.1所示。

5.1 项目进度监测表  
Table 5.1 Project schedule checklist   

<table><tr><td>序 号</td><td>工作内容</td><td>计划启动时间</td><td>实际启动时间</td><td>计划完成时间</td><td>实际完成时间</td></tr><tr><td>1.1</td><td>项目调查研究</td><td>2023-06-01</td><td>2023-06-01</td><td>2023-06-20</td><td>2023-06-20</td></tr><tr><td>1.2</td><td>项目立项审查</td><td>2023-06-21</td><td>2023-06-21</td><td>2023-07-10</td><td>2023-07-10</td></tr><tr><td>1.3</td><td>编写可行性研究 报告</td><td>2023-07-11</td><td>2023-07-11</td><td>2023-07-30</td><td>2023-07-30</td></tr><tr><td>1.4</td><td>产品功能设计</td><td>2023-07-31</td><td>2023-07-31</td><td>2023-08-29</td><td>2023-08-29</td></tr><tr><td>2.1</td><td>电机电控热管理 系统产品设计</td><td>2023-08-30</td><td>2023-08-30</td><td>2023-11-27</td><td>2023-12-12</td></tr><tr><td>3.1</td><td>保密协议及集团 框架协议签署</td><td>2023-11-28</td><td>2023-12-13</td><td>2023-12-12</td><td>2023-12-22</td></tr></table>

续表 5.1  

<table><tr><td>3.2</td><td>供应商发包 及选定</td><td>2023-12-13</td><td>2023-12-23</td><td>2024-01-26</td><td>2024-01-31</td></tr><tr><td>3.3</td><td>零部件质保协议</td><td>2024-01-27</td><td>2024-02-01</td><td>2024-02-10</td><td>2024-02-10</td></tr><tr><td>3.4</td><td>开发及量产 单价协议</td><td>2024-02-11</td><td>2024-02-11</td><td>2024-02-25</td><td>2024-03-06</td></tr><tr><td>4.1</td><td>生产线布局安装</td><td>2024-02-26</td><td>2024-03-07</td><td>2024-05-10</td><td>2024-05-15</td></tr><tr><td>4.3</td><td>生产线员工培训</td><td>2024-05-11</td><td>2024-05-16</td><td>2024-05-25</td><td>2024-05-25</td></tr><tr><td>5.1</td><td>样件交付</td><td>2024-05-26</td><td>2024-05-26</td><td>2024-06-04</td><td>2024-06-04</td></tr></table>

# 5.2.2 项目进度的偏差分析

（1）设计变更反复出现

在整个开发过程中，产生了很多未预料到的问题与难点，需要耗费很长的时间去对这些问题进行分析和解决，这就造成了项目进度的拖延。在此新产品的开发项目执行过程中，技术图纸的变更是最难控制的因素，即便在项目前期已经做出了非常全面综合的考虑，然而在开发过程中热交换新产品的设计还是出现了频繁的变更，而且多数的变更涉及尺寸干涉或者性能参数，是不得不进行的更改。由于与其物理链接的零部件众多，且均为新能源汽车内的关键零部件，包括电子变速箱（eAM）,冷却装置（Chiller），扇热器（Radiator）以及高压电路系统（HVS），因此不得不反复调整新产品物理尺寸，以保证整车装配时与对手件不干涉，相互连接的高压管路顺畅不折弯，以达到精准控制各个区域温度并交换热量的功能。因此，热交换新产品的相关零部件如图5.2 所示。

![](images/a672c7e55f540f48ea925f23494f332800ceb4331d348775cc1fde472a645be2.jpg)  
图 5.2 与热交换新产品相关联的其他零部件Fig.5.2 Relevant auto parts of new development part

（2）原材料涨价

同样受到疫情以及通货膨胀影响，全球各大原材料涨价幅度仍然保持大幅上涨，特别是铜、铝以及玻纤塑料的基材价格走势直接导致水泵、壳体等金属下级件的价格上涨，当前零部件成本远远超过供应商在项目定点时期的零部件报价。因此，供应商在零部件单价合同签署过程中多次提出了对汽车压铸铝部件（AlSi9Cu3）的价格上涨的需求，并利用延迟交货作为谈判条件。经过项目组调查第三方网站数据，发现情况属实。原材料铝在上海金属交易所的每千克人民币价格走势情况如图5.3。

![](images/7642ac110649f5863ae4c4d3804a1bc87212974b00c0d5236e8ec856be60fb47.jpg)  
图 5.3每千克铝的人民币价格走势图  
Fig.5.3 Per kilogram RMB Price of Aluminum

（3）汽车芯片短缺

自2020年来受到疫情、欧美遭遇暴风雪袭击以及日本半导体工厂遭遇火灾等多方影响，全球芯片供应出现重大缺口，各大车企选择大量囤货从而加剧芯片短缺的问题。此热交换产品内部包含多种类别芯片，使得下级件依据项目进度要求抵达一级供应商厂内进行生产装配成为难题，主机厂不得不在商务条款上做出非常大的让步，迅速协调管理会议批准临时措施，将集团的正常付款流程发票 45天后付款更改为发货前全款现汇，以确保开发项目的整体进度。

# 5.3 项目进度偏差调整

在项目的实施过程中，一旦发现实际进度与计划进度存在偏差，立即分析偏  
差可能产生的影响。不同阶段的偏差，对项目进度工期的影响也会有所差异。基  
于这些偏差分析，需要采取相应的调整措施，对原进度计划进行灵活调整，以确  
保项目能够顺利达成预定的工期目标。常见的项目进度偏差调整方法有以下几种：（1）强制性缩短法：当项目面临时间压力时，采用强制性缩短法来优化项目  
进度。在做出调整决策时，需要综合考虑项目成本状况，合理的优化资源配置，

确保项目的整体效益。

顺序法: 在产品开发过程中，对开发工作的顺序进行干预，对各个子工作的顺序进行调整，对操作时间进行了最优安排，从而使项目进度按计划实现。

选择方法：项目管理团队会基于现有资源，对项目工作的时间和过程进行合理干预和调整，以确保项目的顺利进行。

综合加权平均法：为了确保后续项目的整体时间进度，通过综合加权平均法来重点缩短重要项目工作的时间。

（2）调整工作关系

项目的工作安排主要涵盖串行安排、并行安排以及并行交叉。串行工作的具体顺序可以参考图5.4所示：

![](images/5d74d6e80c7585898014619bf5263b516741571ded6f42d0a53c7bb3a7a5510e.jpg)  
图 5.4 串行工作安排网络图

并行工作的顺序参看图5.5所示：

![](images/488a099c6bbe31631feff533a8d9fda2cf1083dc1e9e1b8c7f07684ac13a212a.jpg)  
Fig. 5.4 Serial Work Arrangement Network Diagram   
图 5.5 并行工作安排网络图  
Fig. 5.5 Parallel work arrangement network diagram

为保证集成热交换新产品在规定时间进行量产交付，同时保障新产品的质量技术满足图纸要求，减少开发项目的工作时间，借鉴其他汽车厂新产品开发项目的经验，确定此开发项目中的A阶段样件到厂测试时间为整个项目的关键时间点，样件到厂时间不可以有延期，因此通过使用强制性缩短法，压缩保密协议及集团框架协议签署、供应商选定过程以及质量保证协议各5天，压缩员工培训时间5天以缩短项目的延期时间。同时还使用了追加资源的方法，要求供应商生产现场增加熟练的技术工人，以缩短生产线布局安装工期5天。具体工期的计划与实际完成如下表5.2所示。

# 表5.2 项目进度工期调整表

Table 5.1 Project schedule adjustment   

<table><tr><td>序号</td><td>工作内容</td><td>原工期</td><td>实际工期</td></tr><tr><td>1. 1</td><td>项目调查研究</td><td>20</td><td>20</td></tr><tr><td>1.2</td><td>项目立项审查</td><td>20</td><td>20</td></tr><tr><td>1.3</td><td>编写可行性研究报告</td><td>20</td><td>20</td></tr><tr><td>1.4</td><td>产品功能设计</td><td>30</td><td>30</td></tr><tr><td>2.1</td><td>电机电控热管理系统产品设计</td><td>75</td><td>90</td></tr><tr><td>3.1</td><td>保密协议及集团框架协议签署</td><td>15</td><td>10</td></tr><tr><td>3.2</td><td>供应商发包及选定</td><td>45</td><td>40</td></tr><tr><td>3.3</td><td>零部件质保协议</td><td>15</td><td>10</td></tr><tr><td>3.4</td><td>开发及量产单价协议</td><td>15</td><td>25</td></tr><tr><td>4.1</td><td>生产线布局安装</td><td>75</td><td>70</td></tr><tr><td>4.3</td><td>生产线员工培训</td><td>15</td><td>10</td></tr><tr><td>5.1</td><td>样件交付</td><td>10</td><td>10</td></tr></table>

# 5.4 项目进度的保障措施

项目进度控制是一种对项目进行持续改进并监控的方法，对项目进度进行监控时可能发现项目进度被多方面影响。在早期的计划中可以考虑减少或者避免一些可以预计的风险。认真对项目进度实施监控，并将科学合理的相关措施制定出来，保障能够在规定的时间内完成项目目标[34]。分析进度管理的理论的要点，提出组织保障、技术保障与管理制度保障。

# 5.4.1 组织保障

在所有的保障措施中，组织保障可谓是是最为关键的组成部分，其作用在于借助工作制度的管理、相应的工作体系的建立来确保工程项目能够顺利完成并且与工程质量的要求相吻合[35]。本项目的组织保障主要包括以下三点：

# （1）沟通制度建立

公司应当制定一套完整、流畅的开发项目沟通协调制度，并能用项目进度报告的方式向管理层汇报项目工作信息，便于管理层掌控项目进展，并对遇到的问题给与及时有效的支持。要想让整个项目的进程能够顺利地按原定计划进行，就必须对每个的职责和工作进行明确的划分，缩短解决问题的时间。

为了建立健全各部门之间的沟通交流机制，确保及时有效的解决各部门间相互关联影响的项目工作。该开发项目组织研发、质量、采购以及供应商技术、销售负责人在项目阶段进行紧密的会议沟通，就开发项目的进度以及难题争议进行协调解决。并在供应商现场召开面对面的月度项目会，直接由双方专业人员进行现场沟通，共同解决争议，在项目进度管理中可以极大地保证项目进度，项目日、周以及月度会安排表如图5.6。

![](images/d457cbd421eb2cc04f996d4e2fe9ebcc172e442b0f8230a5509c7695ba6cbcf1.jpg)  
图 5.6 会议日程表  
Fig.5.6 Meeting Landscape

（2）完善的升级制度

在项目实施过程中，相当复杂的二级零部件往往由主机厂直接指定，或进行一定程度的技术指定，因此主机厂选定的一级供应商与这些专业性极强的二级供应商往往存在非常多争议，一级供应商通常需要借助主机厂的力量解决与下级件之间的问题，一套完善的升级制度在开发项目中起到了至关重要的作用。当整车厂技术指定的下级供应商出现质量或者交付问题时，需要有一级供应商调查问题点并形成升级报告提交给项目管理部门。升级流程包括向主管工程师升级，由主管工程师判定情况是否需要向部门经理进行升级（Escalationlevel 1）,由部门经理决定是否继续升级到部门总监获取支持（Escalation level 2），如果仍需向上升级可至副总裁级别获取快速支持（Escalation level 3），升级流程示意图如5.7所示。

![](images/76b2a4908ec6cf917cff16ffcfe9058725c9fddda1abc4a4e50e8ff67dc5f746.jpg)  
图5.7 项目升级制度示意图  
Fig.5.7 Schematic diagram of Project Escalation

# （3）重要业务专职人员

项目进度管理还应结合项目的实际情况，选定高水准的供应商项目管理人员与主机厂项目团队配合，创造有利的工作环境，招募素质高有能力的人，组建具有丰富工作经验和专业技术能力的专职项目管理团队，是保障项目根据进度计划落实的有效措施。专业的项目管理人员应具备项目管理的相关资质，除此之外，项目团队中的实际管理者还应充分了解掌握管理方面的知识，同时在开发项目中有着丰富的实践经历，才能对开发项目中的各项进度指标有精确地掌握，包括技术、生产以及商务等各方面，例如汽车零部件的质量标准和商务合同、供应商的资质评估、零部件的装配标准、汽车行业的试验标准等专业指标，从而才能保证项目的最终成功。本次项目的实施过程中，针对外币、原材料价格上涨问题，由项目经理与采购部经验丰富的项目主管人员分析协商后，紧急与供应商进行商务谈判，最终快速确定了双方都接受的原材料季度性调整机制，经由公司管理层特殊审批后更改了项目商务合同条款，顺利的保证了项目进度计划的有力执行。具体的季度价格调整机制如下图5.8所示。

![](images/0f5b2815b34f363768e485257074d176627191e93a83fc884dbfdefe0a491bec.jpg)  
图 5.8 欧元、铝以及铜价格季度调整机制

Fig.5.8 Quarterly adjusting on Euro, Aluminum and Copper

# 5.4.2 技术保障

依照科学的开发进度计划，采取相应的技术方法和管理手段，是确保整个项目按进度计划完成的重要环节[36]。这样的管理方式不仅可以确保项目的顺利进行，而且还可以保证项目的开发质量。有了健全的技术方法，才能更好地开展这项工作。与此同时，开发项目负责人要定期到供应商生产现场查看零部件开发进度。主要采取以下技术保障措施:

（1）技术质量体系建立

为确保开发项目的质量，项目组必须制定并执行严格的质量保障体系，各相关责任人需实施明确的责任管理措施，以确保整体项目的顺利推进和质量的可靠保障。如若在开发进行过程中出现的问题涉及到项目开发完成时间节点和质量，需要升级至管理层做报告与重新评估，然后提交问题解决计划书。在项目开发过程中，对设备、生产线以及物料等进行质量监控，也是项目实施过程中的重要环节。在项目开发期间，所用的一切物料均需提供检测报告及厂家合格证，未经许可不得进入生产制造现场。

（2）强化设计方案评审工作

在实际应用中，由于图纸的不同，方案的不同，设计理念的不同，将造成目标结果差异，从而影响到整体项目的进度。在使用和评审设计方案时，可以通过对比的方法进行分析再做评审的，综合考虑项目周期和设计的总体关系，来最终确定最适合项目的图纸方案。假定开发项目因遇到困难而延长工期，那么就有必要对设计工艺的各个方面进行细致对比和分析，并深入评估项目优化的可能性，以确保其与规定的期限能够保持一致。在开发项目进行中，应根据汽车零部件技术规范，认真完成零部件设计任务。在开发更复杂的集成零部件开发时，为了保证项目的如期实施，要有效制定技术图纸的开发计划。

# （3）加强技术专题会议

在开发项目的推进过程中，技术难题的涌现是常有的情况。始终秉持技术创新的理念召开技术讨论会，这不仅有助于成本节约，还能通过灵活调整开发计划来有效缩短项目周期。在规划开发计划时，项目组应充分考虑计划的可操作性、经济性和合理性，从中选出最优方案。当开发进度遭遇阻碍时，项目组应在保障项目进度不受影响的前提下，深入剖析问题产生的原因，并探讨可行的解决方案。如有必要，项目组可灵活调整生产、交付及验证的技术手段和方法，引入先进的机械设备以应对挑战。在确保零部件质量符合要求的前提下，项目组可适当调整开发过程中的生产制造工艺、方法和设备，以实现技术措施的优化。

在全面的质量管理和控制中，首要任务是提升设备、模具、卡具以及材料的供应周期和质量，同时，对国家现行的汽车行业法律法规保持熟知。因为提高材料质量，实质上就是增强项目进度的保障。若因设备供应商的直接或间接原因，导致无法在合同规定的期限内按数量提供质量合格的设备，那么在签署合同时就需明确奖惩制度，以约束供货商的行为。在采购关键设备时，务必提前准备替代方案，确保关键设备、模具、卡具及原材料能按规定的品质和时间节点到位，进而保障项目的顺利推进和实施。

# 5.4.3 管理制度保障

在实践中，根据实际情况构建项目管理体系至关重要。基于科学有效的进度管理计划，逐步构建与项目进度管理紧密相关的制度体系，以确保项目的顺利推进和公司的长远发展，主要的管理保障制度有以下几点：

（1）标准化管理

在制定进度计划时，应全面考虑项目期间每个星期、每个月、季度及年度的计划。为确保开发工作的规范性和一致性，开发实际操作必须严格遵循质量技术要求，并为所有开发工作设计统一的标准进度模板。此外，开发在线的进度管理系统，借助云计算技术实现进度计划的实时共享，让项目组成员都能随时掌握项目进展，从而进行必要的调整和改进，避免传统纸质管理方式的局限性。为了加强项目中的沟通，定期举行例会并保留会议纪要，确保数据的准确性和可追溯性，及时进行变更和修改签证，以保证开发进展情况得到及时的调整和优化。

（2）协调沟通制度建立

企业应建立一套完善且流畅的开发项目进展汇报制度，并采用项目进度报告的形式进行总结汇报。项目组成员需定期向相关部门主管提交项目工作信息，以便管理层能够准确掌握项目进展、面临的挑战及经验教训。每周的总结报告需提交给项目经理，每月的总结报告则直接上报给企业管理层。同时，该沟通模式也适用于同级之间的协作，使团队成员能够就项目中的问题进行高效沟通和分析，共同推动项目的改进和发展。

（3）复盘分析建立

为了确保优化方案能够顺利实施，关键在于监控、处理和检查工作的及时有效执行。因此，项目管理团队应当制定一套完善的评审制度，定期对项目的所有开发流程和进程进行全面评审。在具体处理开发工作中的各项环节，对出现的问题凭借经验和智慧找到问题的根源，积累经验。在项目开发过程中，及时地进行信息反馈是一种很有意义的工作方法。如有不能处理的，应立即告知项目主管领导如项目经理，经过对问题的评判，领导应当立刻做出决策来处理问题。若不能独立处理就与其它部门进行协作。工程经理必须以最快的速度解决出现的问题。

（4）完善激励机制

制定合理健全的激励制度，是激发员工热情、规范员工行为的有效手段。通过量化考评体系，能够确保评价的客观性和统计性，从而构建一套公正的评价准则。此外，项目的各小组应根据工期目标和具体任务签订责任书，以确保每位成员都明确自己的职责和目标。制定奖惩制度，对那些能在时间节点上完工且质量合格的人员进行奖励，对出现失误的人员进行处罚，以此激励各个部门的负责人主动地去实现自己的工作进度目标，保证项目按时顺利完成。

# （5）开发任务合理化安排

本项目在年中的工作任务最为关键。所以，做好该阶段的项目进度计划管理，必须参照该年度开发项目的总体规划。当工作时间相差过大时，必须对开发项目进度重新规划作以调整。为了保证开发工作尽早完成，降低开发项目延迟的风险，一级供应商、下级供应商应与主机厂要相互配合，协调好生产交付、样件装配、试验等问题。

# 第 6 章 结论与展望

# 6.1 本文的主要工作结论

本文主要运用项目进度管理、项目进度控制与保障等理论与方法，综合考虑W公司开发项目所具有的特点和风险因素，依据公司稳健发展的战略布局及严谨的流程规范，致力于建立并落实可助企业蓬勃发展的开发项目进度计划。在实际的落实过程中，始终围绕项目需求为核心，着力挖掘多方的协同合作和资源共享潜力，通过以点带面的方式，全面优化项目进度规划。在此过程中，不断的整合公司内部项目管理体系，积极引入外部供应商及企业内部资源，共同构建一个高效、稳定且具有高度兼容的项目进度管理计划。

针对本次开发项目的进度管理工作进行归纳总结，主要进行以下工作：

（1）强化沟通与协作。W公司的开发项目的进度管理不仅包括公司内部各部门间的紧密配合，建立起高效的信息传递渠道，更包括与供应商之间建立战略合作伙伴关系，并针对一级、二级甚至原材料供应商实施差异化的合作管理措施。始终保持良好的项目团队沟通和协调，确保项目进展顺利。

（2）制定并优化项目进度计划。基于项目进度管理的运作与思路，制定且严格执行项目进度计划，并对项目进度计划中的关键路径活动进行优化，及时合理的调整资源分配，为项目预留时间以抵御不可预见的技术变更风险或无形的干扰。

（3）强化项目进度管理制度与人才培养。在此开项目开发中，重点关注选定的供应商项目进度管理能力，强化公司的项目管理人才队伍建设，加大对项目管理能力薄弱的二级甚至三级供应商的监督管理力度，保证了集成热交换开发项目进度管理优化方案的顺利实施。

# 6.2 进一步研究的展望

通过本文的研究，今后核心新产品开发项目的进度管理将获得汽车制造性企业的重视。为了做好 W公司向后的开发项目的进度管理工作，需要引入先进的项目管理工具和技术辅助项目进度管理工作，例如使用项目管理软件和敏捷开发方法。提高项目进度工作的估算精确性，用以提高开发项目的工作效率，降低因为单项子工作延迟所带来的整体项目进度推迟。根据特定的工作需求和背景条件，汽车制造型企业将致力于深入挖掘和分析项目进度流程中的各个环节所受到的工作制约因素，不断发掘并寻找其中存在的问题并改善，进而构思并制定出切实可行的改进策略与实施方案，以此来持续优化企业在项目进度管理方面的规划和执行能力。

建立一个科学严谨且高效迅速的项目进度管理流程体系，无疑将成为企业未来发展的重要战略导向，同时也是企业降低运营成本的关键所在，更是提升企业核心竞争力的重要途径和手段。

构建一个科学化、高度灵活化的项目进度管理系统，对于任何企业而言都是其未来发展战略的重要导向之一，也是实现降本增效的关键环节所在，更是提升企业核心竞争优势的重要手段之一。向后将密切关注 W公司开发项目的进度管理流程及其规范化程度，深入探讨适用于公司执行的各类项目进度管理模型，以及最新的网络信息化技术等专业方法，用以将这些更为先进实用的方式和理念引入到现代汽车制造行业的项目进度管理实践之中，进而助力企业在激烈的经济市场竞争环境中脱颖而出，显著提升企业自身的综合管理水平。

# 参考文献

[1] 唐玉莲,张文光.汽车外覆盖件模具开发项目进度管理研究[J]. 项目管理技术,2016, 14(10):129-132.  
[2] 张延.车身开发设计中的进度管理问题研究[J]. 价值工程,2018, 37(35):268-270.  
[3] 唐元元,蔡德明, 黄祖朋, 陈旭. 项目管理在汽车产品开发过程中的应用[J].时代汽车, 2020, (16): 26-27.  
[4] Madter D, Nancy P. Exploring project management continuing professionaldevelopment in engineering construction[J]. Construction Management andEconomics, 2012, 30 :639-651.  
[5] Ramos M, Moselhi J, Osama T. Stochastic method for forecasting projecttime and cost[J]. Construction Challenges Flat World, 2017, 12: 545-555.  
[6] Wilson D，James M. Gantt charts: A centenary appreciation[J]. EuropeanJournal of Operational Research, 2018, 149:430-437.  
[7] David Pierce. Project Scheduling and Management for Construction [M].Wiley, 2012.  
[8] John N, Herman S. Project Management for Engineering, Business andTechnology [M]. Routledge, 2020.  
[9] Bellah Jeremy. Developing Standards Based Project Management InformationSystem: Project Schedule Functions[J]. PM World Journal, 2021, 10(3):1-8.  
[10] Esmail C, Babak T. A multiproject scheduling and resource managementmodel in projects construction[J]. Engineering Construction and ArchitecturalManagement, 2022, 56(3): 145-157.  
[11] 孔民. 论项目进度管理[C]. 中国武汉决策信息研究开发中心, 决策与信息杂志社, 北京大学经济管理学院. 上海巴陆信息科技有限公司:2015.  
[12] 刘泽俊，周杰, 李秀华.工程项目管理 [M]. 南京:南京东南大学出版社, 2019, 14-15.  
[13] 段世霞. 项目管理 [M]. 南京：南京大学出版社, 2020, 09-10.  
[14] 李一鸣. 项目进度管理现状及对策分析[C]. 中交二公局萌兴工程有限公司:2022.  
[15] 吴先林,王洪超,李璐俊. 基于关键链技术在座椅项目进度管理中的应用研究[J]. 价值工程, 2019, 38(07):75-78.  
[16] 陆斌. 基于蒙特卡罗模拟的项目进度管理方法对比[J]. 项目管理术,2021,19(11): 124-128.  
[17] 梁圆. B 公司新产品 ME开发项目进度管理研究[D]. 贵州: 贵州大学, 2023.  
[18] 杨翼. M 公司新产品研发项目进度管理改进[D]. 成都：电子科技大学,2022.  
[19] 佟丽娜. 汽车新产品开发计划和过程控制研究[D]. 北京：对外经济贸易大学, 2022.  
[20] 张梅花. 物流系统开发项目进度控制研究[J]. 物流工程与管理,2016,38(11): 60-61.  
[21] 蔡英武. 国五柴油发动机后处理开发项目的进度管理研究[D]. 北京：北京理工大学, 2020.  
[22] 傅志军. M 公司汽车零部件产品开发项目进度管理研究[D]. 广州：华南理工大学,2020.  
[23] 杨雪. 基于项目管理的 W 公司新产品开发模式及进度控制研究[D]. 哈尔滨：哈尔滨工业大学,2019.  
[24] 顾培民. SZ 汽车零部件公司项目进度管理研究[D]. 长春：吉林大学,2023.  
[25] 张晶. S 公司 M 项目进度管理研究[D]. 北京：北京工业大学, 2022.  
[26] 李华.BY公司产品研发项目进度管理研究[D]. 重庆：西南大学,2022.  
[27] 刁品飞. S 公司高压接触器生产线项目进度管理研究[D]. 扬州：扬州大学,2023.  
[28] 韩阳光.A县金刚石普查项目进度管理研究[D]. 大连：大连理工大学,2022.  
[29] 谭泽涛. 基于关键路径法的项目进度管理研究[J]. 建筑经济, 2019, 第 40 卷(9): 67-71.  
[30] 侯鑫明. 大型客机平尾研制项目进度管理研究[J]. 中国航班, 2023, (23):38-40.  
[31] 黄成琪. 基于关键路径的空调开发项目进度管理研究[J]. 项目管理技术, 2023, 第 21 卷(8): 18-23.  
[32] 陈林. 装配式建筑项目进度管理研究[J]. 中国房地产业, 2022, (6): 99-101.  
[33] 孟天，丁鹏. 施工项目进度管理的研究[J]. 建筑与预算, 2020, (5): 28-30.  
[34] 许文雯. 基于 WBS 的工程项目进度管理研究[J]. 化工管理,2019, (18): 168-169.  
[35] 王榆斌. 基于关键链技术的工程项目进度管理研究[J]. 世界家苑,2021,(9):149-150.  
[36] 户鲲，刘战胜，李博. 基于关键链的航天制造项目进度管理研究[J].航天工业管理, 2020, (5): 35-41.  
[37] 黄成琪. 基于关键路径的空调开发项目进度管理研究[J]. 项目管理技术,2023, 21(08):18-23.  
[38] 徐冉. Y 公司 ALD 设备开发项目的进度管理研究[D]. 南京：南京理工大学,2021.  
[39] 汤新发，钟甜，黄兴无. 项目进度管理研究[M]. 北京：中国商业出社, 2022.  
[40] Muhammad S R, Muhammad J T. Project schedule risk management throughbuilding information modelling[J]. International Journal of ConstructionManagement, 2022, 22(8): 1489-1499.  
[41] Fernando A, David P, Adolfo L P. Stochastic Earned Duration Analysis forProject Schedule Management[J]. Engineering, 2022, 9:148-161.  
[42] Xinsheng H, Haibo X. Design and Implementation of Project ScheduleData Collection and Analysis System[J]. Journal of Physics 2023, 2425-2062.  
[43] Massimiliano Caramia. Project management and scheduling[J]. Annals ofOperations Research,2020, 285(1): 1-8.  
[44] Muhammad S , Abdur R N. Project schedule risk management through buildinginformation modelling[J]. International Journal of ConstructionManagement, 2022, 22(8): 1489-1499.  
[45] Tang X , Huang X, Li D. Research on construction schedule risk management ofpower supply based on MCS model[J]. Frontiers in Energy Research, 2023, 10.

# 致 谢

两年半的 MEM 课程转眼间就要结束，本科毕业后参加工作已十余年，能获此机会来到东北大学读研，重返青春活力的校园, 我十分珍惜，这段珍贵的旅程终将成为我一生中最为难忘的经历之一。

感谢东北大学工商管理学院的各位老师，是你们专业与耐心的教导，丰富了我的眼界，全面的提升了我的学习能力和专业知识，为我写作本论文夯实了基础。

真切的向我的研究生导师戢守峰老师表示感谢，导师专业的学识和严谨的态度深深影响着我。每当我在写作论文的过程中遇到了问题，导师总是耐心的指导，竭尽全力的为我解答疑问。这两年半的倾囊相授，定终身不忘！

还有我亲爱同学们，非常开心能够有机会结识大家，互相鼓励一同前行，建立深厚的同窗情谊。永远记得我们在一起探讨、互相帮助写作论文的时光，愿我们即便毕业也能友谊长存。

对默默支持我，给予我无尽包容与鼓励的父母爱人，因为你们拼尽全力的付出，方有全心全意拼搏，无所畏惧的我。

图数 26 表数 8 总页数 73 参考文献数 45